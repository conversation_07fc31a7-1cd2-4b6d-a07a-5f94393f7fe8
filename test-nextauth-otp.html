<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار NextAuth OTP</title>
</head>
<body>
    <h1>اختبار NextAuth OTP</h1>
    <div>
        <input type="text" id="phone" placeholder="رقم الهاتف" value="96899474767">
        <input type="text" id="code" placeholder="رمز OTP" value="2949">
        <button onclick="testNextAuthOTP()">اختبار NextAuth</button>
        <button onclick="testDirectAPI()">اختبار API مباشر</button>
    </div>
    <div id="result" style="margin-top: 20px; padding: 20px; border: 1px solid #ccc;"></div>

    <script>
        async function testDirectAPI() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            const resultDiv = document.getElementById('result');
            
            console.log('🧪 اختبار API مباشر:', { phone, code });
            
            try {
                const response = await fetch('/api/auth/otp/verify', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ phone, code }),
                });
                
                const result = await response.json();
                console.log('📋 نتيجة API مباشر:', result);
                
                resultDiv.innerHTML = `
                    <h3>نتيجة API مباشر:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('❌ خطأ:', error);
                resultDiv.innerHTML = `<h3>خطأ:</h3><pre>${error.message}</pre>`;
            }
        }

        async function testNextAuthOTP() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            const resultDiv = document.getElementById('result');
            
            console.log('🔐 اختبار NextAuth:', { phone, code });
            
            try {
                const response = await fetch('/api/auth/callback/credentials', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        phone: phone,
                        code: code,
                        redirect: 'false',
                        json: 'true'
                    }),
                });
                
                const result = await response.text();
                console.log('📋 نتيجة NextAuth:', result);
                
                resultDiv.innerHTML = `
                    <h3>نتيجة NextAuth:</h3>
                    <pre>${result}</pre>
                `;
            } catch (error) {
                console.error('❌ خطأ:', error);
                resultDiv.innerHTML = `<h3>خطأ:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html> 