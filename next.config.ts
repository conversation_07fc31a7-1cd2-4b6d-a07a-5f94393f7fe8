import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // إعدادات أساسية
  serverExternalPackages: ['@prisma/client'],
  poweredByHeader: false,
  compress: true,
  
  // تحسين التجميع - إزالة experimental optimizations المعقدة
  experimental: {
    optimizePackageImports: ['react-icons'],
  },
  
  // تحسين images
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3008',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },
  
  // تبسيط webpack configuration
  webpack: (config, { dev, isServer }) => {
    // تحسين cache للتطوير فقط
    if (dev) {
      config.cache = {
        type: 'memory',
      };
    }
    
    // تحسين module resolution
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };
    
    // تبسيط chunk splitting
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }
    
    return config;
  },
  
  // معالجة إعادة التوجيه
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },
  
  // Headers أساسية للأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
