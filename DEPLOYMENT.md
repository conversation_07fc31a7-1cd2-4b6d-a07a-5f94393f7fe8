# AttendPro aaPanel Deployment Guide

## Prerequisites
- aaPanel installed on your server
- Node.js 18.17+ installed via aaPanel App Store
- PM2 Manager installed via aaPanel App Store
- MySQL database (local or remote)
- Domain name pointed to your server

## Step 1: Database Setup

### Using aaPanel MySQL
1. Go to Database > MySQL
2. Create new database
3. Note down:
   - Database name
   - Username
   - Password

## Step 2: Create Website in aaPanel

1. Go to Website > Add site
2. Enter your domain name
3. Select PHP version (any version, we'll use reverse proxy)
4. Create FTP account (optional)
5. Submit

## Step 3: Upload Project Files

1. Go to File Manager
2. Navigate to `/www/wwwroot/yourdomain.com`
3. Upload your project files (exclude `node_modules` folder)
4. Set permissions to 755 for directories, 644 for files

## Step 4: Environment Configuration

1. In File Manager, create `.env.local` file in project root
2. Add your configuration:
```env
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=generate-a-strong-secret-key-here

# WhatsApp Integration (optional)
WHATSAPP_API_URL=your-whatsapp-api-url
WHATSAPP_API_TOKEN=your-whatsapp-token
```

## Step 5: Install Dependencies & Build

1. Go to Terminal in aaPanel (or SSH)
2. Navigate to project directory:
```bash
cd /www/wwwroot/yourdomain.com
```

3. Install dependencies:
```bash
npm install
```

4. Build the application:
```bash
npm run build
```

## Step 6: Create PM2 Configuration

1. In project root, create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'attendpro',
    script: './node_modules/next/dist/bin/next',
    args: 'start',
    instances: 1,
    exec_mode: 'cluster',
    env: {
      PORT: 3000,
      NODE_ENV: 'production'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    watch: false,
    max_memory_restart: '1G',
    autorestart: true
  }]
}
```

2. Create logs directory:
```bash
mkdir logs
```

## Step 7: Setup Database

1. Run database migrations:
```bash
npx prisma migrate deploy
npx prisma generate
```

2. Seed initial data:
```bash
npm run seed
```

## Step 8: Configure PM2 in aaPanel

1. Go to App Store > PM2 Manager
2. Click "Add Project"
3. Fill in:
   - **Starting file**: `/www/wwwroot/yourdomain.com/ecosystem.config.js`
   - **Project name**: `attendpro`
   - **Execute user**: `www`
4. Click "Submit"
5. Click "Start" to run the application

## Step 9: Setup Reverse Proxy

1. Go to Website > yourdomain.com > Reverse proxy
2. Click "Add reverse proxy"
3. Configure:
   - **Proxy name**: `nextjs`
   - **Target URL**: `http://127.0.0.1:3000`
   - **Send domain**: `$host`
4. Enable the proxy

## Step 10: Configure SSL

1. Go to Website > yourdomain.com > SSL
2. Apply for Let's Encrypt certificate
3. Enable "Force HTTPS"

## Step 11: Verify Installation

1. Visit your domain: `https://yourdomain.com`
2. Check PM2 Manager for application status
3. View logs if needed:
```bash
pm2 logs attendpro
```

## Maintenance Commands

### View application status:
```bash
pm2 status
```

### Restart application:
```bash
pm2 restart attendpro
```

### View logs:
```bash
pm2 logs attendpro
```

### Update application:
```bash
cd /www/wwwroot/yourdomain.com
git pull  # if using git
npm install
npm run build
pm2 restart attendpro
```

## Troubleshooting

### Application not starting
1. Check PM2 logs: `pm2 logs attendpro`
2. Verify `.env.local` exists and has correct values
3. Check Node.js version: `node --version` (should be 18+)

### Database connection errors
1. Verify MySQL is running
2. Check DATABASE_URL format
3. Test connection: `npx prisma db pull`

### 502 Bad Gateway
1. Ensure application is running: `pm2 status`
2. Check reverse proxy configuration
3. Verify port 3000 is correct

### Performance optimization
1. Increase PM2 instances in `ecosystem.config.js`
2. Enable caching in reverse proxy
3. Use CDN for static assets

## Security Notes

- Keep `.env.local` secure (never commit to git)
- Regularly update dependencies
- Monitor PM2 logs for errors
- Use strong NEXTAUTH_SECRET
- Enable firewall rules in aaPanel