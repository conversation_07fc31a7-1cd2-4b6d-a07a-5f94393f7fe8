// اختبار التحقق من OTP
async function testOTPVerify() {
  // Debug: removed console.log
// استخدم الرقم والرمز الأخير من السيرفر لوغز
  const phone = '96899474767'; // الرقم المنسق
  const code = '2848'; // آخر رمز تم إنشاؤه حسب اللوغز
  
  try {
    // Debug: removed console.log
const response = await fetch('http://localhost:3000/api/auth/otp/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone, code }),
    });

    // Debug: removed console.log
const result = await response.json();
    // Debug: removed console.log
if (response.ok) {
      // Debug: removed console.log
// Debug: removed console.log
} else {
      // Debug: removed console.log
}
  } catch (error) {
    console.error('💥 خطأ في الطلب:', error);
  }
}

// تشغيل الاختبار
testOTPVerify(); 