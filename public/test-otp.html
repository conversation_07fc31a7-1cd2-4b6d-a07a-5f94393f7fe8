<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار OTP</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ccc; border-radius: 5px; }
        button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🧪 اختبار إرسال OTP</h2>
        
        <input type="tel" id="phoneInput" placeholder="أدخل رقم الهاتف (مثل: 99474767)" maxlength="8" dir="ltr">
        
        <button onclick="sendOTP()" id="sendBtn">إرسال رمز التحقق</button>
        
        <div id="result"></div>
        
        <div>
            <h3>📱 أرقام للاختبار:</h3>
            <ul>
                <li>99474767 (أحمد محمد)</li>
                <li>91234568 (سارة أحمد)</li>
                <li>91234569 (محمد علي)</li>
            </ul>
        </div>
    </div>

    <script>
        async function sendOTP() {
            const phone = document.getElementById('phoneInput').value;
            const resultDiv = document.getElementById('result');
            const sendBtn = document.getElementById('sendBtn');
            
            if (!phone) {
                resultDiv.innerHTML = '<div class="error">❌ يرجى إدخال رقم الهاتف</div>';
                return;
            }
            
            // Show loading
            resultDiv.innerHTML = '<div class="loading">⏳ جاري إرسال OTP...</div>';
            sendBtn.disabled = true;
            sendBtn.textContent = 'جاري الإرسال...';
            
            try {
                console.log('🚀 إرسال OTP للرقم:', phone);
                
                const response = await fetch('/api/auth/otp/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone }),
                });
                
                console.log('📨 استجابة:', {
                    status: response.status,
                    ok: response.ok,
                    statusText: response.statusText
                });
                
                const result = await response.json();
                console.log('📋 النتيجة:', result);
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ ${result.message}<br>
                            📱 الرقم: ${result.phone}<br>
                            ⏰ الانتهاء: ${result.expiresIn}<br>
                            💡 تحقق من WhatsApp للحصول على الرمز
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${result.error}</div>`;
                }
                
            } catch (error) {
                console.error('💥 خطأ:', error);
                resultDiv.innerHTML = '<div class="error">❌ حدث خطأ في الاتصال</div>';
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = 'إرسال رمز التحقق';
            }
        }
        
        // Allow Enter key to send
        document.getElementById('phoneInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendOTP();
            }
        });
    </script>
</body>
</html> 