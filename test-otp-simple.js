// اختبار بسيط لـ OTP API
async function testOTP() {
  // Debug: removed console.log
try {
    const response = await fetch('http://localhost:3000/api/auth/otp/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone: '99474767' }),
    });

    // Debug: removed console.log
const result = await response.json();
    // Debug: removed console.log
if (response.ok) {
      // Debug: removed console.log
} else {
      // Debug: removed console.log
}
  } catch (error) {
    console.error('💥 خطأ في الطلب:', error);
  }
}

// تشغيل الاختبار
testOTP();
