const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkVisitorsStatus() {
  try {
    console.log('فحص حالة جميع الزوار...');

    // البحث عن جميع الزوار
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const allVisitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log(`\n--- جميع زوار اليوم (${allVisitors.length}) ---`);
    allVisitors.forEach((v, index) => {
      const status = v.checkInTime && v.checkOutTime ? 'انتهت الزيارة' :
                    v.checkInTime ? 'داخل المبنى' : 'في انتظار الدخول';
      
      console.log(`${index + 1}. ${v.visitorName} - ${v.visitorCompany || 'بدون شركة'}`);
      console.log(`   الموظف المضيف: ${v.user.name}`);
      console.log(`   الحالة: ${v.status} - ${status}`);
      console.log(`   تاريخ الزيارة: ${v.visitDate.toLocaleDateString('en-GB')}`);
      if (v.checkInTime) {
        console.log(`   وقت الدخول: ${v.checkInTime.toLocaleString('en-GB')}`);
      }
      if (v.checkOutTime) {
        console.log(`   وقت الخروج: ${v.checkOutTime.toLocaleString('en-GB')}`);
      }
      console.log('   ---');
    });

    // البحث عن مهنا سالم تحديداً
    const mehnaVisitor = await prisma.visitorRequest.findFirst({
      where: {
        visitorName: {
          contains: 'مهنا'
        }
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (mehnaVisitor) {
      console.log('\n--- معلومات مهنا سالم ---');
      console.log('الاسم:', mehnaVisitor.visitorName);
      console.log('الشركة:', mehnaVisitor.visitorCompany);
      console.log('الموظف المضيف:', mehnaVisitor.user.name);
      console.log('تاريخ الزيارة:', mehnaVisitor.visitDate.toLocaleDateString('en-GB'));
      console.log('الحالة:', mehnaVisitor.status);
      console.log('وقت الدخول:', mehnaVisitor.checkInTime);
      console.log('وقت الخروج:', mehnaVisitor.checkOutTime);
    } else {
      console.log('\n--- لم يتم العثور على مهنا سالم ---');
    }

  } catch (error) {
    console.error('خطأ في فحص الزوار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkVisitorsStatus();
