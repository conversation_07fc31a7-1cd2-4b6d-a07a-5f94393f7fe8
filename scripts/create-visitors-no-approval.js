const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createVisitorsNoApproval() {
  try {
    console.log('إنشاء زوار جدد بدون شرط الاعتماد...');

    // البحث عن مستخدم موجود
    const user = await prisma.user.findFirst({
      where: {
        role: 'EMPLOYEE'
      }
    });

    if (!user) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    console.log('تم العثور على موظف:', user.name);

    // إنشاء زوار جدد لليوم بدون حالة اعتماد
    const today = new Date();
    const visitDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // زائر 1 - بحالة PENDING
    const visitor1 = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'سارة أحمد',
        visitorCompany: 'شركة الحلول الذكية',
        purpose: 'مناقشة مشروع جديد',
        visitDate: visitDate,
        companions: null,
        vehicleInfo: 'نيسان التيما - أبيض - أ ب ج 9999',
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء زائر في الانتظار:', visitor1.id);

    // زائر 2 - بحالة REJECTED
    const visitor2 = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'محمد خالد',
        visitorCompany: 'مؤسسة التطوير',
        purpose: 'استشارة تقنية',
        visitDate: visitDate,
        companions: 'أحمد سالم - مطور',
        vehicleInfo: null,
        status: 'REJECTED',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء زائر مرفوض:', visitor2.id);

    // زائر 3 - بحالة CANCELLED
    const visitor3 = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'نورا عبدالله',
        visitorCompany: 'شركة الابتكار التقني',
        purpose: 'عرض منتجات جديدة',
        visitDate: visitDate,
        companions: 'فاطمة محمد - مديرة المبيعات',
        vehicleInfo: 'تويوتا كورولا - رمادي - د هـ و 5555',
        status: 'CANCELLED',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء زائر ملغي:', visitor3.id);

    console.log('تم إنشاء الزوار الجدد بنجاح!');
    console.log('الآن جميع الزوار سيظهرون في صفحة موظف الأمن بغض النظر عن حالة الاعتماد');

  } catch (error) {
    console.error('خطأ في إنشاء الزوار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVisitorsNoApproval();
