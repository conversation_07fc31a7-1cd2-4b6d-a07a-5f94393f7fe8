const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateMehnaDate() {
  try {
    console.log('تحديث تاريخ زيارة مهنا سالم...');

    // البحث عن مهنا سالم
    const mehnaVisitor = await prisma.visitorRequest.findFirst({
      where: {
        visitorName: {
          contains: 'مهنا'
        }
      }
    });

    if (!mehnaVisitor) {
      console.log('لم يتم العثور على مهنا سالم');
      return;
    }

    console.log('تم العثور على مهنا سالم');
    console.log('التاريخ الحالي:', mehnaVisitor.visitDate.toLocaleDateString('en-GB'));

    // تحديث التاريخ ليكون اليوم
    const today = new Date();
    const visitDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const updatedVisitor = await prisma.visitorRequest.update({
      where: { id: mehnaVisitor.id },
      data: {
        visitDate: visitDate,
        checkInTime: null,
        checkOutTime: null,
      }
    });

    console.log('تم تحديث التاريخ بنجاح');
    console.log('التاريخ الجديد:', updatedVisitor.visitDate.toLocaleDateString('en-GB'));

    // التحقق من الزوار لليوم
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const todayVisitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        checkInTime: null,
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log('\n--- الزوار في انتظار الدخول اليوم ---');
    todayVisitors.forEach((v, index) => {
      console.log(`${index + 1}. ${v.visitorName} - ${v.visitorCompany || 'بدون شركة'} - ${v.user.name}`);
    });

  } catch (error) {
    console.error('خطأ في تحديث التاريخ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateMehnaDate();
