const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('إنشاء مستخدم تجريبي...');

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('المستخدم موجود بالفعل: <EMAIL>');
      return;
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash('123456', 10);

    // إنشاء المستخدم
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'مدير النظام',
        employeeNumber: 'ADMIN001',
        role: 'ADMIN',
        phone: '0501234567',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء المستخدم بنجاح:');
    console.log('البريد الإلكتروني: <EMAIL>');
    console.log('كلمة المرور: 123456');
    console.log('الدور: مدير النظام');

  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
