const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createWeeklyAttendanceData() {
  try {
    console.log('إنشاء بيانات الحضور للأسبوع الماضي...');

    // حذف البيانات القديمة
    await prisma.attendanceRecord.deleteMany({});
    await prisma.leaveRequest.deleteMany({});

    // البحث عن الموظفين
    const employees = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      }
    });

    if (employees.length === 0) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    // إنشاء إجازة رسمية لأحد الأيام
    const holidayDate = new Date();
    holidayDate.setDate(holidayDate.getDate() - 3); // قبل 3 أيام

    await prisma.officialHoliday.create({
      data: {
        name: 'إجازة رسمية تجريبية',
        startDate: holidayDate,
        endDate: holidayDate,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`تم إنشاء إجازة رسمية في: ${holidayDate.toLocaleDateString('en-GB')}`);

    // إنشاء بيانات لآخر 7 أيام
    for (let dayOffset = 7; dayOffset >= 0; dayOffset--) {
      const currentDate = new Date();
      currentDate.setDate(currentDate.getDate() - dayOffset);
      
      const dayOfWeek = currentDate.getDay();
      const isWeekend = dayOfWeek === 5 || dayOfWeek === 6; // جمعة وسبت
      
      console.log(`\nإنشاء بيانات لتاريخ: ${currentDate.toLocaleDateString('en-GB')} - ${getDayName(dayOfWeek)}`);
      
      if (isWeekend) {
        console.log('عطلة أسبوعية - تم التخطي');
        continue;
      }

      for (let i = 0; i < employees.length; i++) {
        const employee = employees[i];
        const pattern = (i + dayOffset) % 6; // تنويع الأنماط

        switch (pattern) {
          case 0: // حاضر كامل
            await createFullAttendance(employee.id, currentDate, employee.name);
            break;
            
          case 1: // حاضر جزئي
            await createPartialAttendance(employee.id, currentDate, employee.name);
            break;
            
          case 2: // غائب
            console.log(`${employee.name}: غائب`);
            break;
            
          case 3: // حركات متعددة مع خروج
            await createMultipleMovementsWithBreak(employee.id, currentDate, employee.name);
            break;
            
          case 4: // إجازة معتمدة
            await createApprovedLeave(employee.id, currentDate, employee.name);
            break;
            
          case 5: // حضور مع ساعات إضافية
            await createOvertimeAttendance(employee.id, currentDate, employee.name);
            break;
        }
      }
    }

    console.log('\nتم إنشاء البيانات التجريبية للأسبوع بنجاح!');
    console.log('الأنماط المختلفة:');
    console.log('- حاضر كامل: 8+ ساعات');
    console.log('- حاضر جزئي: أقل من 8 ساعات');
    console.log('- غائب: لا توجد سجلات');
    console.log('- حركات متعددة مع خروج');
    console.log('- إجازة معتمدة');
    console.log('- ساعات إضافية');
    console.log('- إجازة رسمية');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// حاضر كامل
async function createFullAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(8, Math.floor(Math.random() * 30), 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(16, Math.floor(Math.random() * 30), 0, 0);
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'OFFICIAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: حاضر كامل (8 ساعات)`);
}

// حاضر جزئي
async function createPartialAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(9, Math.floor(Math.random() * 60), 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(13, Math.floor(Math.random() * 60), 0, 0);
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'PERSONAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: حاضر جزئي (4 ساعات)`);
}

// حركات متعددة مع خروج
async function createMultipleMovementsWithBreak(userId, date, name) {
  // الحركة الأولى
  const checkIn1 = new Date(date);
  checkIn1.setHours(8, 0, 0, 0);
  
  const checkOut1 = new Date(checkIn1);
  checkOut1.setHours(11, 0, 0, 0); // خروج لمدة ساعتين
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime: checkIn1,
      checkOutTime: checkOut1,
      exitType: 'PERSONAL',
      entryNumber: 1,
      createdAt: checkIn1,
      updatedAt: checkOut1
    }
  });
  
  // الحركة الثانية
  const checkIn2 = new Date(date);
  checkIn2.setHours(13, 0, 0, 0); // عودة بعد ساعتين
  
  const checkOut2 = new Date(checkIn2);
  checkOut2.setHours(17, 0, 0, 0);
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime: checkIn2,
      checkOutTime: checkOut2,
      exitType: 'OFFICIAL',
      entryNumber: 2,
      createdAt: checkIn2,
      updatedAt: checkOut2
    }
  });
  
  console.log(`${name}: حركات متعددة مع خروج (ساعتين خروج)`);
}

// إجازة معتمدة
async function createApprovedLeave(userId, date, name) {
  await prisma.leaveRequest.create({
    data: {
      userId,
      startDate: date,
      endDate: date,
      reason: 'إجازة شخصية',
      status: 'APPROVED',
      createdAt: new Date(date.getTime() - 24 * 60 * 60 * 1000),
      updatedAt: new Date()
    }
  });
  
  console.log(`${name}: في إجازة معتمدة`);
}

// ساعات إضافية
async function createOvertimeAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(7, 30, 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(18, 0, 0, 0); // 10.5 ساعة
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'OFFICIAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: ساعات إضافية (10.5 ساعة)`);
}

function getDayName(dayOfWeek) {
  const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  return days[dayOfWeek];
}

createWeeklyAttendanceData();
