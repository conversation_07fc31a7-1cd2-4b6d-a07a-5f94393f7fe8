const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAttendanceData() {
  try {
    console.log('إنشاء سجلات حضور تجريبية...');

    // البحث عن موظفين
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      },
      take: 3
    });

    if (users.length === 0) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      console.log('إنشاء سجلات حضور للموظف:', user.name);

      // سجل الدخول الأول
      const checkIn1 = new Date(startOfDay);
      checkIn1.setHours(8, 0, 0, 0); // 8:00 AM

      const checkOut1 = new Date(startOfDay);
      checkOut1.setHours(12, 0, 0, 0); // 12:00 PM

      const record1 = await prisma.attendanceRecord.create({
        data: {
          userId: user.id,
          date: startOfDay,
          checkInTime: checkIn1,
          checkOutTime: checkOut1,
          exitType: 'PERSONAL',
          entryNumber: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`تم إنشاء السجل الأول للموظف ${user.name}:`, record1.id);

      // سجل الدخول الثاني
      const checkIn2 = new Date(startOfDay);
      checkIn2.setHours(13, 0, 0, 0); // 1:00 PM

      const checkOut2 = new Date(startOfDay);
      checkOut2.setHours(17, 0, 0, 0); // 5:00 PM

      const record2 = await prisma.attendanceRecord.create({
        data: {
          userId: user.id,
          date: startOfDay,
          checkInTime: checkIn2,
          checkOutTime: checkOut2,
          exitType: 'OFFICIAL',
          entryNumber: 2,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`تم إنشاء السجل الثاني للموظف ${user.name}:`, record2.id);

      // سجل دخول ثالث بدون خروج (لا يزال في المكتب)
      if (i === 0) { // فقط للموظف الأول
        const checkIn3 = new Date(startOfDay);
        checkIn3.setHours(18, 30, 0, 0); // 6:30 PM

        const record3 = await prisma.attendanceRecord.create({
          data: {
            userId: user.id,
            date: startOfDay,
            checkInTime: checkIn3,
            checkOutTime: null,
            exitType: null,
            entryNumber: 3,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        console.log(`تم إنشاء السجل الثالث للموظف ${user.name}:`, record3.id);
      }
    }

    console.log('تم إنشاء سجلات الحضور التجريبية بنجاح!');

  } catch (error) {
    console.error('خطأ في إنشاء سجلات الحضور:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAttendanceData();
