const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createDepartments() {
  try {
    console.log('إنشاء الأقسام التجريبية...');

    // قائمة الأقسام
    const departments = [
      'قسم الموارد البشرية',
      'قسم تقنية المعلومات',
      'قسم المحاسبة',
      'قسم التسويق',
      'قسم المبيعات',
      'قسم الإدارة العامة'
    ];

    // إنشاء الأقسام
    for (const departmentName of departments) {
      // التحقق من عدم وجود القسم
      const existingDepartment = await prisma.department.findFirst({
        where: { name: departmentName }
      });

      if (!existingDepartment) {
        await prisma.department.create({
          data: {
            name: departmentName,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
        console.log(`تم إنشاء القسم: ${departmentName}`);
      } else {
        console.log(`القسم موجود بالفعل: ${departmentName}`);
      }
    }

    // تحديث الموظفين الموجودين وربطهم بالأقسام
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      }
    });

    const createdDepartments = await prisma.department.findMany();

    // ربط الموظفين بالأقسام بشكل عشوائي
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const randomDepartment = createdDepartments[i % createdDepartments.length];
      
      await prisma.user.update({
        where: { id: user.id },
        data: { departmentId: randomDepartment.id }
      });
      
      console.log(`تم ربط ${user.name} بقسم ${randomDepartment.name}`);
    }

    console.log('\nتم إنشاء الأقسام وربط الموظفين بنجاح!');
    console.log(`إجمالي الأقسام: ${createdDepartments.length}`);
    console.log(`إجمالي الموظفين المربوطين: ${users.length}`);

  } catch (error) {
    console.error('خطأ في إنشاء الأقسام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDepartments();
