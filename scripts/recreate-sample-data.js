const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function recreateSampleData() {
  try {
    console.log('🔄 إعادة إنشاء البيانات التجريبية...\n');

    // إنشاء الأقسام
    console.log('🏢 إنشاء الأقسام...');
    const departments = await Promise.all([
      prisma.department.create({
        data: {
          name: 'الموارد البشرية',
        }
      }),
      prisma.department.create({
        data: {
          name: 'تقنية المعلومات',
        }
      }),
      prisma.department.create({
        data: {
          name: 'المالية',
        }
      }),
      prisma.department.create({
        data: {
          name: 'التسويق',
        }
      })
    ]);
    console.log(`✅ تم إنشاء ${departments.length} أقسام`);

    // إنشاء المستخدمين
    console.log('\n👥 إنشاء المستخدمين...');
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const users = await Promise.all([
      // مدير الموارد البشرية
      prisma.user.create({
        data: {
          employeeNumber: 'HR001',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234567',
          position: 'مدير الموارد البشرية',
          role: 'HR',
          departmentId: departments[0].id,
        }
      }),
      // مدير تقنية المعلومات
      prisma.user.create({
        data: {
          employeeNumber: 'IT001',
          name: 'سارة أحمد',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234568',
          position: 'مديرة تقنية المعلومات',
          role: 'MANAGER',
          departmentId: departments[1].id,
        }
      }),
      // موظفين في تقنية المعلومات
      prisma.user.create({
        data: {
          employeeNumber: 'IT002',
          name: 'محمد علي',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234569',
          position: 'مطور برمجيات',
          role: 'EMPLOYEE',
          departmentId: departments[1].id,
        }
      }),
      prisma.user.create({
        data: {
          employeeNumber: 'IT003',
          name: 'فاطمة سالم',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234570',
          position: 'محللة أنظمة',
          role: 'EMPLOYEE',
          departmentId: departments[1].id,
        }
      }),
      // موظفين في المالية
      prisma.user.create({
        data: {
          employeeNumber: 'FIN001',
          name: 'خالد حسن',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234571',
          position: 'محاسب',
          role: 'EMPLOYEE',
          departmentId: departments[2].id,
        }
      }),
      prisma.user.create({
        data: {
          employeeNumber: 'MKT001',
          name: 'نورا عبدالله',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '96891234572',
          position: 'أخصائية تسويق',
          role: 'EMPLOYEE',
          departmentId: departments[3].id,
        }
      })
    ]);
    console.log(`✅ تم إنشاء ${users.length} مستخدمين`);

    // تعيين المديرين للموظفين
    console.log('\n👨‍💼 تعيين المديرين...');
    const itManager = users[1]; // سارة أحمد
    await prisma.user.updateMany({
      where: {
        departmentId: departments[1].id,
        role: 'EMPLOYEE'
      },
      data: {
        managerId: itManager.id
      }
    });
    console.log('✅ تم تعيين المديرين');

    // إنشاء بيانات الحضور
    console.log('\n📊 إنشاء بيانات الحضور...');
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    const attendanceData = [];
    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      
      for (const user of users) {
        if (Math.random() > 0.2) { // 80% حضور
          const checkIn = new Date(date);
          checkIn.setHours(7 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60));
          
          const checkOut = new Date(checkIn);
          checkOut.setHours(checkIn.getHours() + 7 + Math.floor(Math.random() * 2));
          
          attendanceData.push({
            userId: user.id,
            date: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
            checkInTime: checkIn,
            checkOutTime: Math.random() > 0.1 ? checkOut : null,
            exitType: 'OFFICIAL',
            entryNumber: 1
          });
        }
      }
    }
    
    await prisma.attendanceRecord.createMany({
      data: attendanceData
    });
    console.log(`✅ تم إنشاء ${attendanceData.length} سجل حضور`);

    // إنشاء طلبات الإجازة
    console.log('\n🏖️ إنشاء طلبات الإجازة...');
    const leaveRequests = [];
    for (let i = 0; i < 3; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const startDate = new Date(today);
      startDate.setDate(today.getDate() + Math.floor(Math.random() * 30) + 1);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + Math.floor(Math.random() * 5) + 1);
      
      leaveRequests.push({
        userId: user.id,
        startDate,
        endDate,
        reason: ['إجازة سنوية', 'إجازة مرضية', 'إجازة طارئة'][Math.floor(Math.random() * 3)],
        status: ['PENDING', 'APPROVED', 'REJECTED'][Math.floor(Math.random() * 3)]
      });
    }
    
    await prisma.leaveRequest.createMany({
      data: leaveRequests
    });
    console.log(`✅ تم إنشاء ${leaveRequests.length} طلب إجازة`);

    // إنشاء طلبات الزوار
    console.log('\n👥 إنشاء طلبات الزوار...');
    const visitorRequests = [];
    for (let i = 0; i < 4; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const visitDate = new Date(today);
      visitDate.setDate(today.getDate() + Math.floor(Math.random() * 10));
      
      visitorRequests.push({
        userId: user.id,
        visitorName: ['أحمد سالم', 'فاطمة محمد', 'خالد عبدالله', 'نورا حسن'][i],
        visitorCompany: ['شركة التقنية', 'مؤسسة التطوير', 'شركة الاستشارات', 'مكتب المحاماة'][i],
        purpose: ['اجتماع عمل', 'مراجعة عقد', 'استشارة تقنية', 'مقابلة شخصية'][i],
        visitDate,
        visitTime: ['09:00', '10:30', '14:00', '11:00'][i],
        status: ['PENDING', 'APPROVED'][Math.floor(Math.random() * 2)]
      });
    }
    
    await prisma.visitorRequest.createMany({
      data: visitorRequests
    });
    console.log(`✅ تم إنشاء ${visitorRequests.length} طلب زائر`);

    // إنشاء تصاريح العمل بعد الدوام
    console.log('\n🌙 إنشاء تصاريح العمل بعد الدوام...');
    const afterHoursPermits = [];
    for (let i = 0; i < 3; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const workDate = new Date(today);
      workDate.setDate(today.getDate() + Math.floor(Math.random() * 7));
      
      const startTime = new Date(workDate);
      startTime.setHours(16, 0, 0, 0);
      const endTime = new Date(startTime);
      endTime.setHours(startTime.getHours() + 2 + Math.floor(Math.random() * 3));
      
      afterHoursPermits.push({
        userId: user.id,
        date: workDate,
        startTime,
        endTime,
        reason: ['إنهاء مشروع عاجل', 'صيانة النظام', 'اجتماع مع العميل'][i],
        status: ['PENDING', 'APPROVED'][Math.floor(Math.random() * 2)]
      });
    }
    
    await prisma.afterHoursPermit.createMany({
      data: afterHoursPermits
    });
    console.log(`✅ تم إنشاء ${afterHoursPermits.length} تصريح عمل`);

    console.log('\n🎉 تم إنشاء جميع البيانات التجريبية بنجاح!');
    console.log('\n📋 ملخص البيانات:');
    console.log(`   - ${departments.length} أقسام`);
    console.log(`   - ${users.length} مستخدمين`);
    console.log(`   - ${attendanceData.length} سجل حضور`);
    console.log(`   - ${leaveRequests.length} طلب إجازة`);
    console.log(`   - ${visitorRequests.length} طلب زائر`);
    console.log(`   - ${afterHoursPermits.length} تصريح عمل`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

recreateSampleData();
