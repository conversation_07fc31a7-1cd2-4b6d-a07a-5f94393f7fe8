const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createVisitorToday() {
  try {
    console.log('إنشاء زائر لتاريخ اليوم...');

    // البحث عن موظف أيمن علي
    const user = await prisma.user.findFirst({
      where: {
        name: {
          contains: 'أيمن'
        }
      }
    });

    if (!user) {
      console.log('لم يتم العثور على الموظف أيمن علي');
      return;
    }

    console.log('تم العثور على الموظف:', user.name);

    // إنشاء زائر بتاريخ اليوم
    const today = new Date();
    const visitDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    console.log('تاريخ الزيارة:', visitDate.toLocaleDateString('en-GB'));

    const visitor = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'مهنا سالم',
        visitorCompany: 'الخارجية',
        purpose: 'اجتماع عمل',
        visitDate: visitDate,
        companions: null,
        vehicleInfo: null,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء الزائر بنجاح:', visitor.id);
    console.log('اسم الزائر:', visitor.visitorName);
    console.log('الشركة:', visitor.visitorCompany);
    console.log('الموظف المضيف:', user.name);
    console.log('تاريخ الزيارة:', visitor.visitDate.toLocaleDateString('en-GB'));

    // التحقق من جميع الزوار لليوم
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const todayVisitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    console.log('\n--- جميع زوار اليوم ---');
    todayVisitors.forEach((v, index) => {
      console.log(`${index + 1}. ${v.visitorName} - ${v.visitorCompany} - ${v.user.name} - ${v.visitDate.toLocaleDateString('en-GB')}`);
    });

  } catch (error) {
    console.error('خطأ في إنشاء الزائر:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVisitorToday();
