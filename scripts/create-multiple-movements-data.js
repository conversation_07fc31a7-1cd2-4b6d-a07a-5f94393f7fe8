const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createMultipleMovementsData() {
  try {
    console.log('إنشاء بيانات تجريبية للحركات المتعددة...');

    // البحث عن الموظفين
    const employees = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      },
      take: 2 // أول موظفين فقط
    });

    if (employees.length === 0) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // إنشاء حركات متعددة للموظف الأول
    const employee1 = employees[0];
    
    // الحركة الأولى - الصباح
    await prisma.attendanceRecord.create({
      data: {
        userId: employee1.id,
        date: startOfDay,
        checkInTime: new Date(startOfDay.getTime() + 8 * 60 * 60 * 1000), // 8:00 AM
        checkOutTime: new Date(startOfDay.getTime() + 12 * 60 * 60 * 1000), // 12:00 PM
        exitType: 'PERSONAL',
        entryNumber: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // الحركة الثانية - بعد الظهر
    await prisma.attendanceRecord.create({
      data: {
        userId: employee1.id,
        date: startOfDay,
        checkInTime: new Date(startOfDay.getTime() + 13 * 60 * 60 * 1000), // 1:00 PM
        checkOutTime: new Date(startOfDay.getTime() + 15 * 60 * 60 * 1000), // 3:00 PM
        exitType: 'WORK',
        entryNumber: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // الحركة الثالثة - المساء
    await prisma.attendanceRecord.create({
      data: {
        userId: employee1.id,
        date: startOfDay,
        checkInTime: new Date(startOfDay.getTime() + 16 * 60 * 60 * 1000), // 4:00 PM
        checkOutTime: new Date(startOfDay.getTime() + 18 * 60 * 60 * 1000), // 6:00 PM
        exitType: 'OFFICIAL',
        entryNumber: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`تم إنشاء 3 حركات للموظف: ${employee1.name}`);

    // إنشاء حركات متعددة للموظف الثاني
    if (employees.length > 1) {
      const employee2 = employees[1];
      
      // الحركة الأولى
      await prisma.attendanceRecord.create({
        data: {
          userId: employee2.id,
          date: startOfDay,
          checkInTime: new Date(startOfDay.getTime() + 7.5 * 60 * 60 * 1000), // 7:30 AM
          checkOutTime: new Date(startOfDay.getTime() + 11.5 * 60 * 60 * 1000), // 11:30 AM
          exitType: 'HEALTH',
          entryNumber: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      // الحركة الثانية
      await prisma.attendanceRecord.create({
        data: {
          userId: employee2.id,
          date: startOfDay,
          checkInTime: new Date(startOfDay.getTime() + 14 * 60 * 60 * 1000), // 2:00 PM
          checkOutTime: null, // لم يخرج بعد
          exitType: null,
          entryNumber: 2,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`تم إنشاء 2 حركة للموظف: ${employee2.name}`);
    }

    console.log('\nتم إنشاء البيانات التجريبية للحركات المتعددة بنجاح!');
    console.log('يمكنك الآن اختبار تعديل الحركات المختلفة');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMultipleMovementsData();
