const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateSettings() {
  try {
    console.log('تحديث الإعدادات...');

    // البحث عن الإعدادات الموجودة
    const existingSettings = await prisma.settings.findFirst();

    if (existingSettings) {
      // تحديث الإعدادات الموجودة
      await prisma.settings.update({
        where: { id: existingSettings.id },
        data: {
          workStartTime: '08:00',
          workEndTime: '14:30',
          workHoursRequired: 6.5,
          updatedAt: new Date()
        }
      });
      console.log('تم تحديث الإعدادات الموجودة');
    } else {
      // إنشاء إعدادات جديدة
      await prisma.settings.create({
        data: {
          workStartTime: '08:00',
          workEndTime: '14:30',
          workHoursRequired: 6.5,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('تم إنشاء إعدادات جديدة');
    }

    // عرض الإعدادات الحالية
    const currentSettings = await prisma.settings.findFirst();
    console.log('\nالإعدادات الحالية:');
    console.log(`وقت بداية العمل: ${currentSettings?.workStartTime}`);
    console.log(`وقت نهاية العمل: ${currentSettings?.workEndTime}`);
    console.log(`ساعات العمل المطلوبة: ${currentSettings?.workHoursRequired}`);

  } catch (error) {
    console.error('خطأ في تحديث الإعدادات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateSettings();
