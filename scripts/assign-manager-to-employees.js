const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function assignManagerToEmployees() {
  try {
    console.log('تعيين مدير للموظفين...');

    // البحث عن مدير
    const manager = await prisma.user.findFirst({
      where: {
        role: 'MANAGER'
      }
    });

    if (!manager) {
      console.log('لا يوجد مدراء في النظام');
      return;
    }

    console.log('تم العثور على المدير:', manager.name);

    // البحث عن الموظفين
    const employees = await prisma.user.findMany({
      where: {
        role: 'EMPLOYEE',
        managerId: null // الموظفين الذين لا يوجد لهم مدير
      }
    });

    console.log(`تم العثور على ${employees.length} موظف بدون مدير`);

    // تعيين المدير للموظفين
    for (const employee of employees) {
      await prisma.user.update({
        where: { id: employee.id },
        data: { managerId: manager.id }
      });
      
      console.log(`تم تعيين المدير ${manager.name} للموظف ${employee.name}`);
    }

    // التحقق من النتيجة
    const updatedEmployees = await prisma.user.findMany({
      where: {
        managerId: manager.id
      },
      select: {
        id: true,
        name: true,
        role: true,
      }
    });

    console.log('\n--- الموظفين التابعين للمدير ---');
    updatedEmployees.forEach((emp, index) => {
      console.log(`${index + 1}. ${emp.name} - ${emp.role}`);
    });

    console.log(`\nإجمالي الموظفين التابعين للمدير: ${updatedEmployees.length}`);

  } catch (error) {
    console.error('خطأ في تعيين المدير:', error);
  } finally {
    await prisma.$disconnect();
  }
}

assignManagerToEmployees();
