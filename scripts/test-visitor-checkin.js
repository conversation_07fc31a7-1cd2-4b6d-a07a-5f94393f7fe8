const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testVisitorCheckin() {
  try {
    console.log('اختبار تسجيل دخول الزوار...');

    // البحث عن زائر لليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const visitor = await prisma.visitorRequest.findFirst({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        checkInTime: null, // لم يسجل دخول بعد
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!visitor) {
      console.log('لا يوجد زوار متاحين لتسجيل الدخول');
      return;
    }

    console.log('تم العثور على زائر:', visitor.visitorName);
    console.log('الشركة:', visitor.visitorCompany);
    console.log('الحالة:', visitor.status);
    console.log('الموظف المضيف:', visitor.user.name);

    // محاكاة تسجيل الدخول
    const updatedVisitor = await prisma.visitorRequest.update({
      where: { id: visitor.id },
      data: {
        checkInTime: new Date(),
      },
    });

    console.log('تم تسجيل دخول الزائر بنجاح!');
    console.log('وقت الدخول:', updatedVisitor.checkInTime.toLocaleString('en-GB'));

    // عرض جميع الزوار وحالتهم
    const allVisitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log('\n--- حالة جميع الزوار ---');
    allVisitors.forEach((v, index) => {
      const status = v.checkInTime && v.checkOutTime ? 'انتهت الزيارة' :
                    v.checkInTime ? 'داخل المبنى' : 'في انتظار الدخول';
      console.log(`${index + 1}. ${v.visitorName} - ${v.visitorCompany} - ${status}`);
    });

  } catch (error) {
    console.error('خطأ في اختبار تسجيل الدخول:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testVisitorCheckin();
