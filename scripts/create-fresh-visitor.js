const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createFreshVisitor() {
  try {
    console.log('إنشاء زائر جديد لم يسجل دخول...');

    // البحث عن موظف
    const user = await prisma.user.findFirst({
      where: {
        name: {
          contains: 'أيمن'
        }
      }
    });

    if (!user) {
      console.log('لم يتم العثور على موظف');
      return;
    }

    // إنشاء زائر جديد بتاريخ اليوم
    const today = new Date();
    const visitDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const visitor = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'خالد أحمد',
        visitorCompany: 'شركة المستقبل',
        purpose: 'مراجعة العقود',
        visitDate: visitDate,
        companions: null,
        vehicleInfo: 'BMW X5 - أسود - ك ل م 7777',
        status: 'PENDING',
        checkInTime: null,
        checkOutTime: null,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء زائر جديد:', visitor.visitorName);
    console.log('معرف الزائر:', visitor.id);
    console.log('الحالة:', visitor.status);
    console.log('وقت الدخول:', visitor.checkInTime);

    // عرض جميع الزوار الذين لم يسجلوا دخول
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const pendingVisitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        checkInTime: null,
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log('\n--- الزوار في انتظار الدخول ---');
    pendingVisitors.forEach((v, index) => {
      console.log(`${index + 1}. ${v.visitorName} - ${v.visitorCompany} - ${v.status} - ${v.user.name}`);
    });

  } catch (error) {
    console.error('خطأ في إنشاء الزائر:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createFreshVisitor();
