const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('التحقق من المستخدمين الموجودين...');

    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        employeeNumber: true
      }
    });

    console.log(`إجمالي المستخدمين: ${users.length}`);
    
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}) - ${user.role} - ${user.employeeNumber}`);
    });

    // التحقق من الأقسام
    const departments = await prisma.department.findMany();
    console.log(`\nإجمالي الأقسام: ${departments.length}`);
    
    departments.forEach(dept => {
      console.log(`- ${dept.name}`);
    });

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
