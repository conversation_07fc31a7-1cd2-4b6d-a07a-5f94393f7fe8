const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createDashboardTestData() {
  try {
    console.log('إنشاء بيانات تجريبية للوحات التحكم...');

    // البحث عن المستخدمين
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      },
      take: 5
    });

    if (users.length === 0) {
      console.log('لا يوجد مستخدمين في النظام');
      return;
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // إنشاء طلبات إجازات متنوعة
    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      // طلب إجازة معلق
      await prisma.leaveRequest.create({
        data: {
          userId: user.id,
          startDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // الأسبوع القادم
          endDate: new Date(today.getTime() + 9 * 24 * 60 * 60 * 1000),
          reason: `إجازة سنوية للموظف ${user.name}`,
          status: 'PENDING',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      // طلب إجازة معتمد
      if (i < 2) {
        await prisma.leaveRequest.create({
          data: {
            userId: user.id,
            startDate: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000),
            endDate: new Date(today.getTime() + 16 * 24 * 60 * 60 * 1000),
            reason: `إجازة مرضية للموظف ${user.name}`,
            status: 'APPROVED',
            createdAt: yesterday,
            updatedAt: new Date()
          }
        });
      }
    }

    // إنشاء طلبات زيارات
    for (let i = 0; i < 3; i++) {
      const user = users[i];

      await prisma.visitorRequest.create({
        data: {
          userId: user.id,
          visitorName: `زائر تجريبي ${i + 1}`,
          visitorCompany: `شركة ${i + 1}`,
          purpose: `اجتماع عمل ${i + 1}`,
          visitDate: new Date(today.getTime() + (i + 1) * 24 * 60 * 60 * 1000),
          companions: null,
          vehicleInfo: null,
          status: i === 0 ? 'PENDING' : 'APPROVED',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    // إنشاء تصاريح عمل بعد الدوام
    for (let i = 0; i < 2; i++) {
      const user = users[i];

      const startTime = new Date(today);
      startTime.setHours(18, 0, 0, 0);

      const endTime = new Date(today);
      endTime.setHours(22, 0, 0, 0);

      await prisma.afterHoursPermit.create({
        data: {
          userId: user.id,
          date: new Date(today.getTime() + (i + 2) * 24 * 60 * 60 * 1000),
          startTime: startTime,
          endTime: endTime,
          reason: `مشروع مهم ${i + 1}`,
          status: i === 0 ? 'PENDING' : 'APPROVED',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    // إنشاء سجلات حضور متنوعة
    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      // حضور اليوم
      if (i < 3) {
        const checkInTime = new Date(startOfDay);
        checkInTime.setHours(8, Math.floor(Math.random() * 60), 0, 0); // بين 8:00 و 8:59

        const record = await prisma.attendanceRecord.create({
          data: {
            userId: user.id,
            date: startOfDay,
            checkInTime: checkInTime,
            checkOutTime: i === 0 ? null : new Date(checkInTime.getTime() + 8 * 60 * 60 * 1000), // 8 ساعات عمل
            exitType: i === 0 ? null : 'OFFICIAL',
            entryNumber: 1,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        console.log(`تم إنشاء سجل حضور للموظف ${user.name}`);
      }

      // حضور الأمس
      const yesterdayCheckIn = new Date(yesterday);
      yesterdayCheckIn.setHours(8, Math.floor(Math.random() * 30), 0, 0);

      const yesterdayCheckOut = new Date(yesterdayCheckIn);
      yesterdayCheckOut.setHours(17, Math.floor(Math.random() * 30), 0, 0);

      await prisma.attendanceRecord.create({
        data: {
          userId: user.id,
          date: yesterday,
          checkInTime: yesterdayCheckIn,
          checkOutTime: yesterdayCheckOut,
          exitType: 'OFFICIAL',
          entryNumber: 1,
          createdAt: yesterday,
          updatedAt: yesterday
        }
      });
    }

    // إنشاء إجازة رسمية
    await prisma.officialHoliday.create({
      data: {
        name: 'اليوم الوطني',
        startDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000),
        endDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء البيانات التجريبية بنجاح!');
    console.log('- طلبات إجازات معلقة ومعتمدة');
    console.log('- طلبات زيارات متنوعة');
    console.log('- تصاريح عمل بعد الدوام');
    console.log('- سجلات حضور لعدة أيام');
    console.log('- إجازة رسمية قادمة');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDashboardTestData();
