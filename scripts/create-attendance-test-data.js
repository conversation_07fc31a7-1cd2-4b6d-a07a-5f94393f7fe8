const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAttendanceTestData() {
  try {
    console.log('إنشاء بيانات تجريبية للحضور والانصراف...');

    // البحث عن الموظفين
    const employees = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      }
    });

    if (employees.length === 0) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    const twoDaysAgo = new Date(today);
    twoDaysAgo.setDate(today.getDate() - 2);

    // إنشاء سجلات حضور متنوعة لثلاثة أيام
    const dates = [twoDaysAgo, yesterday, today];

    for (const date of dates) {
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      
      console.log(`\nإنشاء بيانات الحضور لتاريخ: ${date.toLocaleDateString('en-GB')}`);

      for (let i = 0; i < employees.length; i++) {
        const employee = employees[i];
        
        // تنويع أنماط الحضور
        const attendancePattern = i % 4;
        
        switch (attendancePattern) {
          case 0: // حضور كامل (8+ ساعات)
            await createFullAttendance(employee.id, startOfDay);
            console.log(`${employee.name}: حضور كامل`);
            break;
            
          case 1: // حضور جزئي (4-6 ساعات)
            await createPartialAttendance(employee.id, startOfDay);
            console.log(`${employee.name}: حضور جزئي`);
            break;
            
          case 2: // غياب (لا توجد سجلات)
            console.log(`${employee.name}: غائب`);
            break;
            
          case 3: // حضور مع حركات متعددة
            await createMultipleMovements(employee.id, startOfDay);
            console.log(`${employee.name}: حركات متعددة`);
            break;
        }
      }
    }

    // إنشاء إجازة معتمدة لموظف واحد لليوم
    const employeeOnLeave = employees[0];
    await prisma.leaveRequest.create({
      data: {
        userId: employeeOnLeave.id,
        startDate: today,
        endDate: today,
        reason: 'إجازة شخصية',
        status: 'APPROVED',
        createdAt: yesterday,
        updatedAt: yesterday
      }
    });
    
    console.log(`\n${employeeOnLeave.name}: في إجازة معتمدة اليوم`);

    console.log('\nتم إنشاء البيانات التجريبية بنجاح!');
    console.log('- حضور كامل لبعض الموظفين');
    console.log('- حضور جزئي لبعض الموظفين');
    console.log('- غياب لبعض الموظفين');
    console.log('- حركات متعددة لبعض الموظفين');
    console.log('- إجازة معتمدة لموظف واحد');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// حضور كامل (8+ ساعات)
async function createFullAttendance(userId, date) {
  const checkInTime = new Date(date);
  checkInTime.setHours(8, Math.floor(Math.random() * 30), 0, 0); // 8:00-8:30
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(17, Math.floor(Math.random() * 30), 0, 0); // 17:00-17:30
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'OFFICIAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
}

// حضور جزئي (4-6 ساعات)
async function createPartialAttendance(userId, date) {
  const checkInTime = new Date(date);
  checkInTime.setHours(9, Math.floor(Math.random() * 60), 0, 0); // 9:00-10:00
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(14, Math.floor(Math.random() * 60), 0, 0); // 14:00-15:00
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'PERSONAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
}

// حركات متعددة
async function createMultipleMovements(userId, date) {
  // الحركة الأولى - الصباح
  const checkIn1 = new Date(date);
  checkIn1.setHours(8, 0, 0, 0);
  
  const checkOut1 = new Date(checkIn1);
  checkOut1.setHours(12, 0, 0, 0);
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime: checkIn1,
      checkOutTime: checkOut1,
      exitType: 'PERSONAL',
      entryNumber: 1,
      createdAt: checkIn1,
      updatedAt: checkOut1
    }
  });
  
  // الحركة الثانية - بعد الظهر
  const checkIn2 = new Date(date);
  checkIn2.setHours(13, 0, 0, 0);
  
  const checkOut2 = new Date(checkIn2);
  checkOut2.setHours(17, 30, 0, 0);
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime: checkIn2,
      checkOutTime: checkOut2,
      exitType: 'OFFICIAL',
      entryNumber: 2,
      createdAt: checkIn2,
      updatedAt: checkOut2
    }
  });
}

createAttendanceTestData();
