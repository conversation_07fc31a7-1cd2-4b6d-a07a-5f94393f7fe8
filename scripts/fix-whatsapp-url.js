const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixWhatsAppUrl() {
  try {
    console.log('🔧 تنظيف رابط WhatsApp API...');

    // جلب الإعدادات الحالية
    const settings = await prisma.settings.findFirst();

    if (!settings) {
      console.log('❌ لم يتم العثور على إعدادات');
      return;
    }

    console.log('📋 الرابط الحالي:', settings.whatsappApiUrl);

    if (settings.whatsappApiUrl) {
      // تنظيف الرابط
      let cleanUrl = settings.whatsappApiUrl;

      // إزالة جميع أجزاء /send/whatsapp
      cleanUrl = cleanUrl.replace(/\/send\/whatsapp.*$/g, '');

      // إزالة الشرطة المائلة في النهاية
      cleanUrl = cleanUrl.replace(/\/$/, '');

      // التأكد من أن الرابط صحيح
      if (!cleanUrl.startsWith('http')) {
        cleanUrl = 'https://w.gcccons.org/api';
      }

      console.log('✅ الرابط المنظف:', cleanUrl);

      // تحديث قاعدة البيانات
      await prisma.settings.update({
        where: { id: settings.id },
        data: { whatsappApiUrl: cleanUrl }
      });

      console.log('🎉 تم تحديث الرابط بنجاح!');
    } else {
      console.log('⚠️ لا يوجد رابط WhatsApp محفوظ');
    }

  } catch (error) {
    console.error('❌ خطأ في تنظيف الرابط:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixWhatsAppUrl();
