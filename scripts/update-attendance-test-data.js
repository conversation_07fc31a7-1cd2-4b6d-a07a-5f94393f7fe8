const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateAttendanceTestData() {
  try {
    console.log('تحديث بيانات الحضور لاختبار النظام الجديد...');

    // حذف البيانات القديمة
    await prisma.attendanceRecord.deleteMany({});
    await prisma.leaveRequest.deleteMany({
      where: {
        reason: 'إجازة شخصية'
      }
    });

    // البحث عن الموظفين
    const employees = await prisma.user.findMany({
      where: {
        role: {
          in: ['EMPLOYEE', 'MANAGER', 'HR']
        }
      }
    });

    if (employees.length === 0) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    console.log(`إنشاء بيانات الحضور لتاريخ: ${today.toLocaleDateString('en-GB')}`);

    // إنشاء أنماط حضور مختلفة
    for (let i = 0; i < employees.length; i++) {
      const employee = employees[i];
      const pattern = i % 5;

      switch (pattern) {
        case 0: // حاضر - 8 ساعات كاملة
          await createFullAttendance(employee.id, startOfDay, employee.name);
          break;
          
        case 1: // حاضر - 9 ساعات (أكثر من الحد الأدنى)
          await createOvertimeAttendance(employee.id, startOfDay, employee.name);
          break;
          
        case 2: // جزئي - 4 ساعات (أقل من الحد الأدنى)
          await createPartialAttendance(employee.id, startOfDay, employee.name);
          break;
          
        case 3: // غائب - لا توجد سجلات
          console.log(`${employee.name}: غائب (لا توجد سجلات)`);
          break;
          
        case 4: // في إجازة معتمدة
          await createApprovedLeave(employee.id, today, employee.name);
          break;
      }
    }

    console.log('\nتم تحديث البيانات بنجاح!');
    console.log('الأنماط المختلفة:');
    console.log('- حاضر: 8+ ساعات عمل');
    console.log('- جزئي: أقل من 8 ساعات');
    console.log('- غائب: لا توجد سجلات');
    console.log('- في إجازة: إجازة معتمدة');

  } catch (error) {
    console.error('خطأ في تحديث البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// حاضر - 8 ساعات كاملة (= الحد الأدنى)
async function createFullAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(8, 0, 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(16, 0, 0, 0); // 8 ساعات بالضبط
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'OFFICIAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: حاضر (8 ساعات - يساوي الحد الأدنى)`);
}

// حاضر - 9 ساعات (أكبر من الحد الأدنى)
async function createOvertimeAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(8, 0, 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(17, 0, 0, 0); // 9 ساعات
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'OFFICIAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: حاضر (9 ساعات - أكبر من الحد الأدنى)`);
}

// جزئي - 4 ساعات (أقل من الحد الأدنى)
async function createPartialAttendance(userId, date, name) {
  const checkInTime = new Date(date);
  checkInTime.setHours(9, 0, 0, 0);
  
  const checkOutTime = new Date(checkInTime);
  checkOutTime.setHours(13, 0, 0, 0); // 4 ساعات فقط
  
  await prisma.attendanceRecord.create({
    data: {
      userId,
      date,
      checkInTime,
      checkOutTime,
      exitType: 'PERSONAL',
      entryNumber: 1,
      createdAt: checkInTime,
      updatedAt: checkOutTime
    }
  });
  
  console.log(`${name}: جزئي (4 ساعات - أقل من الحد الأدنى)`);
}

// إنشاء إجازة معتمدة
async function createApprovedLeave(userId, date, name) {
  await prisma.leaveRequest.create({
    data: {
      userId,
      startDate: date,
      endDate: date,
      reason: 'إجازة شخصية معتمدة',
      status: 'APPROVED',
      createdAt: new Date(date.getTime() - 24 * 60 * 60 * 1000), // أمس
      updatedAt: new Date()
    }
  });
  
  console.log(`${name}: في إجازة معتمدة`);
}

updateAttendanceTestData();
