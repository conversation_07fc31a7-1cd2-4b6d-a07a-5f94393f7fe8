const https = require('https');
const fs = require('fs');
const path = require('path');

// إنشاء مجلد الخطوط إذا لم يكن موجود
const fontsDir = path.join(__dirname, '..', 'public', 'fonts');
if (!fs.existsSync(fontsDir)) {
  fs.mkdirSync(fontsDir, { recursive: true });
}

// روابط خطوط Tajawal من Google Fonts
const fontUrls = {
  '200': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
  '300': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l8ahLuvPDQ.woff2',
  '400': 'https://fonts.gstatic.com/s/tajawal/v9/Iurf6YBj_oCad4k1rzaLCw.woff2',
  '500': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l5KiLuvPDQ.woff2',
  '700': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l9ajLuvPDQ.woff2',
  '800': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l96kLuvPDQ.woff2',
  '900': 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l-alLuvPDQ.woff2'
};

function downloadFont(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(fontsDir, filename);
    
    // تحقق من وجود الملف
    if (fs.existsSync(filePath)) {
      console.log(`الخط موجود بالفعل: ${filename}`);
      resolve();
      return;
    }

    console.log(`تحميل الخط: ${filename}...`);
    
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`فشل في تحميل ${filename}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`تم تحميل: ${filename}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // حذف الملف في حالة الخطأ
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function downloadAllFonts() {
  console.log('بدء تحميل خطوط Tajawal...');
  
  try {
    const downloads = Object.entries(fontUrls).map(([weight, url]) => {
      const filename = `tajawal-${weight}.woff2`;
      return downloadFont(url, filename);
    });
    
    await Promise.all(downloads);
    
    console.log('\n✅ تم تحميل جميع الخطوط بنجاح!');
    console.log('📁 مكان الخطوط: public/fonts/');
    console.log('🎨 يمكنك الآن استخدام الخطوط محلياً بدون الحاجة للإنترنت');
    
  } catch (error) {
    console.error('❌ خطأ في تحميل الخطوط:', error.message);
    console.log('\n💡 نصيحة: تأكد من اتصالك بالإنترنت وحاول مرة أخرى');
  }
}

// تشغيل التحميل
downloadAllFonts();
