const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addLogoSettings() {
  try {
    console.log('إضافة إعدادات الشعار والفافيكون...');

    // البحث عن الإعدادات الموجودة
    const existingSettings = await prisma.settings.findFirst();

    const updateData = {
      companyName: 'شركة الخليج للاستشارات',
      // يمكنك إضافة رابط الشعار والفافيكون هنا
      // logo: 'https://example.com/logo.png',
      // favicon: 'https://example.com/favicon.ico',
      primaryColor: '#1e40af',
      secondaryColor: '#3b82f6',
      updatedAt: new Date()
    };

    if (existingSettings) {
      // تحديث الإعدادات الموجودة
      await prisma.settings.update({
        where: { id: existingSettings.id },
        data: updateData
      });
      console.log('تم تحديث الإعدادات الموجودة');
    } else {
      // إنشاء إعدادات جديدة
      await prisma.settings.create({
        data: {
          ...updateData,
          workStartTime: '08:00',
          workEndTime: '14:30',
          workHoursRequired: 6.5,
          timezone: 'Asia/Muscat',
          whatsappApiUrl: 'https://w.gcccons.org/api',
          whatsappEnabled: false,
          otpTemplate: 'رمز التحقق الخاص بك هو: {otp}',
          earlyExitTemplate: 'الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.',
          visitorArrivalTemplate: 'وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}',
          permitApprovalTemplate: 'تم الموافقة على طلبك. التفاصيل: {details}',
          createdAt: new Date()
        }
      });
      console.log('تم إنشاء إعدادات جديدة');
    }

    // عرض الإعدادات الحالية
    const currentSettings = await prisma.settings.findFirst();
    console.log('\nالإعدادات الحالية:');
    console.log(`اسم الشركة: ${currentSettings?.companyName}`);
    console.log(`الشعار: ${currentSettings?.logo || 'غير محدد'}`);
    console.log(`الفافيكون: ${currentSettings?.favicon || 'غير محدد'}`);
    console.log(`اللون الرئيسي: ${currentSettings?.primaryColor}`);
    console.log(`اللون الفرعي: ${currentSettings?.secondaryColor}`);

    console.log('\nملاحظة: يمكنك رفع الشعار والفافيكون من صفحة الإعدادات في التطبيق');

  } catch (error) {
    console.error('خطأ في إضافة إعدادات الشعار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addLogoSettings();
