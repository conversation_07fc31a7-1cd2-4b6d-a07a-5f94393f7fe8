const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('إنشاء بيانات تجريبية...');

    // البحث عن مستخدم موجود
    const user = await prisma.user.findFirst({
      where: {
        role: 'EMPLOYEE'
      }
    });

    if (!user) {
      console.log('لا يوجد موظفين في النظام');
      return;
    }

    console.log('تم العثور على موظف:', user.name);

    // إنشاء طلب زائر معتمد لليوم
    const today = new Date();
    const visitDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const visitor = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'أحمد محمد',
        visitorCompany: 'شركة التقنية المتقدمة',
        purpose: 'اجتماع عمل مهم',
        visitDate: visitDate,
        companions: 'محمد علي - مدير المشاريع',
        vehicleInfo: 'تويوتا كامري - أبيض - ل م ن 1234',
        status: 'APPROVED',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء طلب زائر معتمد:', visitor.id);

    // إنشاء تصريح عمل بعد الدوام معتمد لليوم
    const startTime = new Date();
    startTime.setHours(18, 0, 0, 0); // 6:00 PM
    
    const endTime = new Date();
    endTime.setHours(22, 0, 0, 0); // 10:00 PM

    const permit = await prisma.afterHoursPermit.create({
      data: {
        userId: user.id,
        date: visitDate,
        startTime: startTime,
        endTime: endTime,
        reason: 'إنهاء مشروع مهم قبل الموعد النهائي',
        status: 'APPROVED',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء تصريح عمل بعد الدوام معتمد:', permit.id);

    // إنشاء زائر آخر معتمد
    const visitor2 = await prisma.visitorRequest.create({
      data: {
        userId: user.id,
        visitorName: 'فاطمة أحمد',
        visitorCompany: 'مؤسسة الابتكار',
        purpose: 'مراجعة العقود',
        visitDate: visitDate,
        companions: null,
        vehicleInfo: 'هوندا أكورد - أسود - ب ج د 5678',
        status: 'APPROVED',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('تم إنشاء طلب زائر معتمد آخر:', visitor2.id);

    console.log('تم إنشاء البيانات التجريبية بنجاح!');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
