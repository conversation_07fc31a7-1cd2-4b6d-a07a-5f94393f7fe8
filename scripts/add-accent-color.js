const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addAccentColor() {
  try {
    console.log('إضافة عمود لون المؤشر...');

    // البحث عن الإعدادات الموجودة
    const existingSettings = await prisma.settings.findFirst();

    if (existingSettings) {
      // تحديث الإعدادات الموجودة لإضافة لون المؤشر
      await prisma.settings.update({
        where: { id: existingSettings.id },
        data: {
          accentColor: '#10b981', // لون أخضر افتراضي
          updatedAt: new Date()
        }
      });
      console.log('تم تحديث الإعدادات الموجودة بلون المؤشر');
    } else {
      // إنشاء إعدادات جديدة
      await prisma.settings.create({
        data: {
          companyName: 'شركة الخليج للاستشارات',
          workStartTime: '08:00',
          workEndTime: '14:30',
          workHoursRequired: 6.5,
          timezone: 'Asia/Muscat',
          primaryColor: '#1e40af',
          secondaryColor: '#3b82f6',
          accentColor: '#10b981',
          whatsappApiUrl: 'https://w.gcccons.org/api',
          whatsappEnabled: false,
          otpTemplate: 'رمز التحقق الخاص بك هو: {otp}',
          earlyExitTemplate: 'الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.',
          visitorArrivalTemplate: 'وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}',
          permitApprovalTemplate: 'تم الموافقة على طلبك. التفاصيل: {details}',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('تم إنشاء إعدادات جديدة مع لون المؤشر');
    }

    // عرض الإعدادات الحالية
    const currentSettings = await prisma.settings.findFirst();
    console.log('\nالإعدادات الحالية:');
    console.log(` اسم المؤسسة: ${currentSettings?.companyName}`);
    console.log(`اللون الرئيسي: ${currentSettings?.primaryColor}`);
    console.log(`اللون الفرعي: ${currentSettings?.secondaryColor}`);
    console.log(`لون المؤشر: ${currentSettings?.accentColor}`);

    console.log('\n✅ تم إضافة لون المؤشر بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة لون المؤشر:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addAccentColor();
