const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAttendanceData() {
  try {
    console.log('التحقق من بيانات الحضور...');

    // الحصول على بداية ونهاية الشهر الحالي
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    console.log(`الفترة: من ${startOfMonth.toISOString().split('T')[0]} إلى ${endOfMonth.toISOString().split('T')[0]}`);

    // جلب سجلات الحضور
    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { userId: 'asc' },
        { entryNumber: 'asc' }
      ]
    });

    console.log(`إجمالي سجلات الحضور: ${attendanceRecords.length}`);

    if (attendanceRecords.length === 0) {
      console.log('لا توجد سجلات حضور في هذه الفترة');
      
      // إنشاء بعض البيانات التجريبية
      console.log('إنشاء بيانات تجريبية...');
      
      const users = await prisma.user.findMany({
        where: {
          role: {
            in: ['EMPLOYEE', 'MANAGER', 'HR']
          }
        },
        take: 3
      });

      if (users.length > 0) {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        for (const user of users) {
          // سجل اليوم
          await prisma.attendanceRecord.create({
            data: {
              userId: user.id,
              date: today,
              checkInTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0),
              checkOutTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 17, 0),
              exitType: 'OFFICIAL',
              entryNumber: 1
            }
          });

          // سجل أمس
          await prisma.attendanceRecord.create({
            data: {
              userId: user.id,
              date: yesterday,
              checkInTime: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 8, 30),
              checkOutTime: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 16, 30),
              exitType: 'PERSONAL',
              entryNumber: 1
            }
          });

          console.log(`تم إنشاء سجلات للموظف: ${user.name}`);
        }

        console.log('تم إنشاء البيانات التجريبية بنجاح!');
      }
    } else {
      console.log('\nتفاصيل السجلات:');
      attendanceRecords.forEach(record => {
        console.log(`- ${record.user.name} (${record.user.employeeNumber}) - ${record.date.toISOString().split('T')[0]} - حركة #${record.entryNumber}`);
        console.log(`  دخول: ${record.checkInTime ? record.checkInTime.toLocaleTimeString() : 'غير محدد'}`);
        console.log(`  خروج: ${record.checkOutTime ? record.checkOutTime.toLocaleTimeString() : 'لم يخرج'}`);
        console.log(`  نوع: ${record.exitType || 'غير محدد'}`);
      });
    }

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAttendanceData();
