const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupTestWhatsApp() {
  try {
    console.log('🧪 إعداد إعدادات WhatsApp للاختبار...\n');

    const settings = await prisma.settings.findFirst();
    
    if (!settings) {
      console.log('❌ لم يتم العثور على إعدادات النظام');
      return;
    }

    // Set up test credentials (you need to replace these with real ones)
    await prisma.settings.update({
      where: { id: settings.id },
      data: {
        whatsappApiUrl: 'https://w.gcccons.org/api',
        whatsappApiSecret: 'YOUR_REAL_API_SECRET_HERE',  // 🔑 Replace with real secret
        whatsappAccountKey: 'YOUR_REAL_ACCOUNT_KEY_HERE', // 🔑 Replace with real key
        whatsappEnabled: true
      }
    });

    console.log('⚠️ تحذير: تم إعداد إعدادات اختبار!');
    console.log('🔑 يجب عليك استبدال الإعدادات التالية بالإعدادات الحقيقية:');
    console.log('   - whatsappApiSecret: احصل عليه من CloudText');
    console.log('   - whatsappAccountKey: احصل عليه من CloudText');
    console.log('');
    console.log('✅ تم تفعيل خدمة WhatsApp');
    console.log('');
    console.log('🧪 الآن يمكنك اختبار OTP:');
    console.log('   node test-otp-simple.js');
    console.log('');
    console.log('🌐 أو اذهب إلى http://localhost:3000/login واختبر إرسال OTP');

  } catch (error) {
    console.error('❌ خطأ في إعداد WhatsApp:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Warning message
console.log('⚠️ تحذير: هذا سكريبت لإعداد إعدادات اختبار');
console.log('🔑 يجب عليك الحصول على إعدادات حقيقية من CloudText أولاً');
console.log('');
console.log('هل تريد المتابعة؟ (قم بتحرير الملف أولاً لإضافة إعداداتك الحقيقية)');

setupTestWhatsApp(); 