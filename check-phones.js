const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkPhones() {
  try {
    console.log('🔍 فحص أرقام الهواتف في قاعدة البيانات...\n');

    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true
      }
    });

    console.log(`👥 إجمالي المستخدمين: ${users.length}\n`);

    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.role})`);
      console.log(`   📧 البريد: ${user.email}`);
      console.log(`   📱 الهاتف: ${user.phone || 'غير محدد'}`);
      console.log('');
    });

    // فحص المستخدمين الذين لديهم أرقام هواتف
    const usersWithPhones = users.filter(user => user.phone);
    console.log(`📱 المستخدمين الذين لديهم أرقام هواتف: ${usersWithPhones.length}`);

    if (usersWithPhones.length > 0) {
      console.log('\n📋 أرقام الهواتف المتاحة للاختبار:');
      usersWithPhones.forEach((user, index) => {
        const cleanPhone = user.phone.replace(/[^\d]/g, '');
        const last8 = cleanPhone.slice(-8);
        console.log(`${index + 1}. ${user.name}: ${last8} (آخر 8 أرقام)`);
      });
    }

    // فحص إعدادات WhatsApp
    const settings = await prisma.settings.findFirst();
    console.log('\n⚙️ إعدادات WhatsApp:');
    console.log(`   🔗 الرابط: ${settings?.whatsappApiUrl || 'غير محدد'}`);
    console.log(`   🔑 المفتاح السري: ${settings?.whatsappApiSecret ? 'موجود' : 'غير محدد'}`);
    console.log(`   👤 مفتاح الحساب: ${settings?.whatsappAccountKey ? 'موجود' : 'غير محدد'}`);
    console.log(`   ✅ مفعل: ${settings?.whatsappEnabled ? 'نعم' : 'لا'}`);

  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPhones();
