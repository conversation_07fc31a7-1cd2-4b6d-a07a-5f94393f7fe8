const axios = require('axios');

// Define the API details
const url = "https://w.gcccons.org/api/get/credits";
const params = {
    secret: "YOUR_API_SECRET"
};

// Make the GET request
axios.get(url, { params })
    .then(response => {
        console.log("Success:", response.data);
    })
    .catch(error => {
        if (error.response) {
            console.error("Error:", error.response.status, error.response.data);
        } else {
            console.error("Error:", error.message);
        }
    });