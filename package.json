{"name": "attendpro_aug_next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "dev:fast": "next dev --turbo", "clean": "rm -rf .next && rm -rf node_modules/.cache", "dev:clean": "npm run clean && npm run dev", "build:analyze": "ANALYZE=true npm run build"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "mysql2": "^3.14.1", "next": "15.3.2", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}