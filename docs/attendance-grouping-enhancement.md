# تحسين تجميع بيانات الحضور حسب الموظف

## نظرة عامة

تم تحسين صفحة كشف الحضور والانصراف (`/dashboard/attendance`) لتجميع البيانات حسب اسم الموظف عند الفلترة باسم موظف محدد، بدلاً من التجميع حسب التاريخ. هذا التحسين يقلل من تكرار اسم الموظف ويوفر عرضاً أكثر تنظيماً للبيانات.

## المشكلة

عند فلترة بيانات الحضور لموظف محدد، كانت البيانات تُعرض مجمعة حسب التاريخ، مما يؤدي إلى:
- تكرار اسم الموظف في كل صف
- عرض غير منطقي للبيانات عندما يكون هناك موظف واحد فقط
- صعوبة في متابعة سجل الحضور للموظف الواحد

## الحل المطبق

### التجميع الذكي للبيانات

تم تطبيق منطق تجميع ذكي يعتمد على حالة الفلتر:

1. **عند فلترة موظف محدد**: التجميع حسب الموظف
2. **عند عدم وجود فلتر موظف**: التجميع حسب التاريخ (السلوك الافتراضي)

### التغييرات المطبقة

#### 1. منطق التجميع الشرطي

```typescript
{filters.employee ? (
  // تجميع حسب الموظف
  Object.entries(
    attendanceData.reduce((groups: any, item) => {
      const employeeKey = `${item.employeeName}-${item.employeeNumber}`;
      if (!groups[employeeKey]) {
        groups[employeeKey] = {
          employee: item,
          records: []
        };
      }
      groups[employeeKey].records.push(item);
      return groups;
    }, {})
  )
) : (
  // تجميع حسب التاريخ (السلوك الافتراضي)
  Object.entries(
    attendanceData.reduce((groups: any, item) => {
      const date = item.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(item);
      return groups;
    }, {})
  )
)}
```

#### 2. عرض معلومات الموظف

عند التجميع حسب الموظف، يتم عرض:
- اسم الموظف ورقمه
- القسم التابع له
- إحصائيات شاملة (إجمالي الأيام، حاضر، جزئي، غائب، إجازة)

#### 3. جدول مخصص للموظف الواحد

```typescript
<table className="min-w-full divide-y divide-gray-200">
  <thead className="bg-gray-50">
    <tr>
      <th>التاريخ</th>
      <th>الحالة</th>
      <th>إجمالي الخروج</th>
      <th>الحركات</th>
      <th>الإجراءات</th>
    </tr>
  </thead>
  <tbody>
    {/* عرض سجلات الحضور مرتبة حسب التاريخ */}
  </tbody>
</table>
```

## الميزات الجديدة

### 1. عرض معلومات الموظف الشاملة

- **اسم الموظف ورقمه**: عرض واضح في رأس المجموعة
- **القسم**: عرض القسم التابع له الموظف
- **أيقونة مميزة**: استخدام أيقونة المستخدم للتمييز البصري

### 2. إحصائيات مفصلة

- **إجمالي الأيام**: عدد أيام الحضور المسجلة
- **حاضر**: عدد الأيام الحاضر فيها
- **جزئي**: عدد الأيام ذات الحضور الجزئي
- **غائب**: عدد أيام الغياب
- **إجازة**: عدد أيام الإجازة

### 3. ترتيب زمني محسن

- ترتيب السجلات من الأحدث إلى الأقدم
- عرض التاريخ بالتنسيق العربي الكامل (اليوم، التاريخ، الشهر، السنة)

### 4. الحفاظ على الوظائف الموجودة

- جميع وظائف التعديل والحذف متاحة
- حساب مدة الخروج وإجمالي الخروج
- عرض تفاصيل الحركات والاستئذانات
- **تحديث تقرير الطباعة**: يتبع نفس منطق التجميع (حسب الموظف أو التاريخ)

## تحسين عرض مدة الخروج

### التحسين المطبق

تم تحسين عرض مدة الخروج لتظهر بتنسيق أكثر دقة ووضوحاً:

#### قبل التحسين
- عرض المدة بالساعات فقط (مثل: 2.5 ساعة)
- صعوبة في فهم الوقت الدقيق

#### بعد التحسين
- عرض المدة بالساعات والدقائق (مثل: 2 ساعة و 30 دقيقة)
- وضوح أكبر في فهم مدة الخروج الفعلية

### التطبيق التقني

#### دالة التحويل الجديدة

```typescript
const formatDuration = (hours: number) => {
  if (hours <= 0) return '-';
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (wholeHours === 0) {
    return `${minutes} دقيقة`;
  } else if (minutes === 0) {
    return `${wholeHours} ساعة`;
  } else {
    return `${wholeHours} ساعة و ${minutes} دقيقة`;
  }
};
```

#### الأماكن المحدثة

1. **عمود إجمالي الخروج في الجدول الرئيسي**
2. **تفاصيل مدة الخروج في الحركات**
3. **تقرير الطباعة - إجمالي الخروج**
4. **تقرير الطباعة - تفاصيل الحركات**

### أمثلة على التحسين

| المدة بالساعات | العرض الجديد |
|-----------------|---------------|
| 0.5 | 30 دقيقة |
| 1.0 | 1 ساعة |
| 1.25 | 1 ساعة و 15 دقيقة |
| 2.75 | 2 ساعة و 45 دقيقة |
| 0.17 | 10 دقيقة |

### الفوائد

1. **دقة أكبر**: عرض الدقائق بدلاً من الكسور العشرية
2. **وضوح في القراءة**: تنسيق مألوف للمستخدمين العرب
3. **سهولة الفهم**: تجنب الحاجة لتحويل الكسور العشرية ذهنياً
4. **اتساق في العرض**: نفس التنسيق في جميع أجزاء النظام

## الفوائد

### 1. تحسين تجربة المستخدم

- **تقليل التكرار**: عدم تكرار اسم الموظف في كل صف
- **عرض منطقي**: تجميع منطقي للبيانات حسب السياق
- **سهولة المتابعة**: متابعة أسهل لسجل الموظف الواحد

### 2. تحسين الأداء

- **تقليل العناصر المكررة**: تقليل عدد العناصر المعروضة
- **تحسين القراءة**: عرض أكثر تنظيماً وأقل ازدحاماً

### 3. مرونة في العرض

- **تجميع ذكي**: تغيير طريقة التجميع حسب نوع الفلتر
- **الحفاظ على السلوك الافتراضي**: عدم تأثير على العرض العام

## التطبيق التقني

### الملفات المعدلة

- `src/app/dashboard/attendance/page.tsx`: تحديث منطق عرض البيانات

### التغييرات الرئيسية

1. **إضافة شرط التجميع**: `filters.employee ? groupByEmployee : groupByDate`
2. **تطوير دالة التجميع حسب الموظف**: تجميع السجلات تحت مفتاح الموظف
3. **تصميم واجهة مخصصة**: عرض معلومات الموظف والإحصائيات
4. **ترتيب السجلات**: ترتيب زمني من الأحدث للأقدم
5. **تحديث تقرير الطباعة**: تطبيق نفس منطق التجميع في وظيفة الطباعة

### الكود المضاف

```typescript
// تجميع حسب الموظف
const employeeKey = `${item.employeeName}-${item.employeeNumber}`;
if (!groups[employeeKey]) {
  groups[employeeKey] = {
    employee: item,
    records: []
  };
}
groups[employeeKey].records.push(item);
```

## تحديث تقرير الطباعة

### التحسين المطبق

تم تحديث وظيفة `handlePrintReport` لتتبع نفس منطق التجميع المطبق في العرض الرئيسي:

#### 1. التجميع الذكي في الطباعة

```typescript
// تجميع البيانات حسب التاريخ أو الموظف
let groupedData: { [key: string]: any[] } = {};
let isGroupedByEmployee = false;

if (filters.employee) {
  // تجميع حسب الموظف
  isGroupedByEmployee = true;
  attendanceData.forEach(item => {
    const employeeKey = `${item.employeeName}-${item.employeeNumber}`;
    if (!groupedData[employeeKey]) {
      groupedData[employeeKey] = [];
    }
    groupedData[employeeKey].push(item);
  });
} else {
  // تجميع حسب التاريخ (السلوك الافتراضي)
  attendanceData.forEach(item => {
    if (!groupedData[item.date]) {
      groupedData[item.date] = [];
    }
    groupedData[item.date].push(item);
  });
}
```

#### 2. تخصيص العرض في الطباعة

**عند التجميع حسب الموظف:**
- عرض معلومات الموظف في رأس مميز بلون مختلف
- جدول يحتوي على: التاريخ، الحالة، الحركات، إجمالي الخروج، ساعات العمل
- ترتيب السجلات من الأحدث للأقدم

**عند التجميع حسب التاريخ:**
- عرض التاريخ في رأس القسم
- جدول يحتوي على: الموظف، رقم الموظف، القسم، الحالة، الحركات، إجمالي الخروج، ساعات العمل

#### 3. تحسينات إضافية في الطباعة

- **إضافة معلومات الموظف في الرأس**: عند فلترة موظف محدد، يظهر اسمه ورقمه في رأس التقرير
- **ألوان مميزة**: استخدام ألوان مختلفة لرؤوس الموظفين (بنفسجي) والتواريخ (أزرق)
- **ترتيب ذكي**: ترتيب أبجدي للموظفين، وترتيب زمني للتواريخ

### الكود المضاف للطباعة

```typescript
// تخصيص CSS للموظفين
htmlContent += '.employee-header { background-color: #f3e5f5; padding: 10px; margin-bottom: 10px; font-weight: bold; color: #7b1fa2; }';

// عرض معلومات الموظف في رأس التقرير
if (filters.employee) {
  const selectedEmployee = filteredEmployees.find(emp => emp.id === filters.employee);
  if (selectedEmployee) {
    htmlContent += '<p>الموظف: ' + selectedEmployee.name + ' - رقم: ' + selectedEmployee.employeeNumber + '</p>';
  }
}
```

## الاختبار

### سيناريوهات الاختبار

1. **فلترة موظف محدد**:
   - اختيار موظف من القائمة المنسدلة
   - التحقق من التجميع حسب الموظف
   - التحقق من عرض الإحصائيات الصحيحة
   - **اختبار الطباعة**: التحقق من تطبيق نفس التجميع في التقرير المطبوع

2. **عدم فلترة موظف**:
   - ترك فلتر الموظف فارغاً
   - التحقق من التجميع حسب التاريخ (السلوك الافتراضي)
   - **اختبار الطباعة**: التحقق من التجميع حسب التاريخ في التقرير المطبوع

3. **التبديل بين الفلاتر**:
   - التبديل من فلتر موظف إلى عدم فلترة
   - التحقق من تغيير طريقة العرض تلقائياً
   - **اختبار الطباعة**: التحقق من تغيير تنسيق الطباعة تلقائياً

4. **اختبار عرض مدة الخروج**:
   - التحقق من عرض المدة بتنسيق ساعات ودقائق
   - اختبار حالات مختلفة (دقائق فقط، ساعات فقط، ساعات ودقائق)
   - التحقق من دقة التحويل من الكسور العشرية
   - **اختبار الطباعة**: التحقق من نفس التنسيق في التقرير المطبوع

### النتائج المتوقعة

- **مع فلتر الموظف**: عرض مجمع حسب الموظف مع إحصائيات شاملة
- **بدون فلتر الموظف**: عرض مجمع حسب التاريخ كما هو معتاد
- **الوظائف**: جميع وظائف التعديل والحذف تعمل بشكل طبيعي
- **تقرير الطباعة**: يتبع نفس منطق التجميع المطبق في العرض الرئيسي
- **عرض مدة الخروج**: تظهر بتنسيق ساعات ودقائق واضح ومفهوم
- **دقة التوقيت**: تحويل دقيق من الكسور العشرية إلى ساعات ودقائق

## التوافق

### المتصفحات المدعومة

- جميع المتصفحات الحديثة
- دعم كامل للغة العربية واتجاه RTL

### الأجهزة

- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية (responsive design)

## الصيانة

### نقاط المراقبة

1. **أداء التجميع**: مراقبة أداء دوال التجميع مع البيانات الكبيرة
2. **دقة الإحصائيات**: التحقق من صحة حسابات الإحصائيات
3. **تجربة المستخدم**: متابعة ردود فعل المستخدمين

### التحديثات المستقبلية

- إمكانية حفظ تفضيل طريقة العرض
- إضافة خيارات تجميع إضافية (حسب القسم، حسب الحالة)
- تحسين الإحصائيات لتشمل معدلات الحضور

---

**تاريخ التطبيق**: 25/05/2025  
**آخر تحديث**: 25/05/2025 - تحسين عرض مدة الخروج بتنسيق ساعات ودقائق  
**الإصدار**: 1.1  
**المطور**: فريق تطوير AttendPro  
**الحالة**: مطبق ومختبر 