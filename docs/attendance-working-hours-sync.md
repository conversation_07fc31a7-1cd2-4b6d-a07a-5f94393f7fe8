# مزامنة ساعات العمل المرنة في سجلات الحضور

## نظرة عامة

تم تطوير نظام مزامنة ساعات العمل المرنة في سجلات الحضور لإعطاء الموظفين مرونة في أوقات العمل مع الحفاظ على مدة العمل المطلوبة. النظام الجديد يحسب وقت انتهاء العمل المتوقع بناءً على وقت الدخول الفعلي + ساعات العمل من الإعدادات.

## الهدف من الميزة

- **مرونة في أوقات العمل**: السماح للموظفين بالدخول في أوقات مختلفة مع الحفاظ على مدة العمل المطلوبة
- **حساب دقيق لمدة الخروج**: استخدام وقت انتهاء العمل المتوقع بدلاً من وقت ثابت
- **ثبات البيانات التاريخية**: حفظ وقت انتهاء العمل المتوقع مع كل سجل حضور
- **التوافق العكسي**: دعم السجلات القديمة التي تستخدم النظام الثابت

## الفكرة الأساسية

### النظام القديم (الثابت)
```
وقت العمل: 8:00 ص - 3:00 م (7 ساعات)
جميع الموظفين يجب أن يعملوا نفس الساعات
```

### النظام الجديد (المرن)
```
مدة العمل: 7 ساعات
الموظف يدخل في 9:00 ص → وقت انتهاء العمل المتوقع: 4:00 م
الموظف يدخل في 7:30 ص → وقت انتهاء العمل المتوقع: 2:30 م
```

## التطبيق التقني

### 1. هيكل قاعدة البيانات

#### تحديث حقل ساعات العمل في سجل الحضور
```prisma
model AttendanceRecord {
  id              String    @id @default(cuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  date            DateTime  // تاريخ اليوم
  checkInTime     DateTime
  checkOutTime    DateTime?
  exitType        ExitType?
  entryNumber     Int       @default(1) // رقم الدخول في اليوم (1-4)
  recordedBy      String?   // معرف موظف الأمن الذي سجل الحركة
  notes           String?   // ملاحظات إضافية
  emp_working_hrs Float?    // وقت انتهاء العمل المتوقع (timestamp) = وقت الدخول + ساعات العمل
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}
```

#### إعدادات ساعات العمل
```prisma
model Settings {
  id                String   @id @default(cuid())
  // ... الحقول الأخرى
  workHoursRequired Float    @default(4.0)  // الحد الأدنى لساعات العمل
  workingHours      Float    @default(7.0)  // إجمالي ساعات العمل اليومية
  // ... باقي الحقول
}
```

### 2. تسجيل الحضور بواسطة موظف الأمن

**الملف:** `src/app/api/security/check-in/route.ts`

```typescript
export async function POST(request: NextRequest) {
  try {
    // ... التحقق من الصلاحيات والبيانات

    // جلب الإعدادات للحصول على ساعات العمل
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // ... التحقق من الموظف والقيود

    // حساب وقت انتهاء العمل المتوقع = وقت الدخول + ساعات العمل
    const checkInTime = new Date();
    const expectedEndTime = new Date(checkInTime.getTime() + (workingHours * 60 * 60 * 1000));

    // إنشاء سجل حضور جديد مع وقت انتهاء العمل المتوقع
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        userId: employeeId,
        date: startOfDay,
        checkInTime: checkInTime,
        entryNumber,
        recordedBy: session.user.id,
        emp_working_hrs: expectedEndTime.getTime(), // حفظ وقت انتهاء العمل المتوقع كـ timestamp
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تسجيل الدخول بنجاح",
      record: {
        ...attendanceRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
    });
  } catch (error) {
    // ... معالجة الأخطاء
  }
}
```

**الميزات:**
- ✅ جلب ساعات العمل من الإعدادات تلقائياً
- ✅ استخدام القيمة الافتراضية (7 ساعات) في حالة عدم وجود إعدادات
- ✅ حفظ ساعات العمل مع سجل الحضور
- ✅ ربط السجل بموظف الأمن الذي سجل الحضور

### 3. الإدخال اليدوي للحضور من قبل الإدارة

**الملف:** `src/app/api/attendance/manual/route.ts`

```typescript
export async function POST(request: NextRequest) {
  try {
    // ... التحقق من الصلاحيات

    const { employeeId, date, checkInTime, checkOutTime, exitType, entryNumber } = body;

    // جلب الإعدادات للحصول على ساعات العمل
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // حساب وقت انتهاء العمل المتوقع = وقت الدخول + ساعات العمل
    const checkIn = new Date(checkInTime);
    const expectedEndTime = new Date(checkIn.getTime() + (workingHours * 60 * 60 * 1000));

    // إنشاء سجل الحضور مع وقت انتهاء العمل المتوقع
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        userId: employeeId,
        date: new Date(date),
        checkInTime: checkIn,
        checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
        exitType: exitType || 'OFFICIAL',
        entryNumber: entryNumber || 1,
        emp_working_hrs: expectedEndTime.getTime(), // حفظ وقت انتهاء العمل المتوقع كـ timestamp
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم إنشاء سجل الحضور بنجاح",
      record: {
        ...attendanceRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
    });
  } catch (error) {
    // ... معالجة الأخطاء
  }
}
```

**الميزات:**
- ✅ صلاحية محدودة للإدارة والموارد البشرية فقط
- ✅ مزامنة ساعات العمل من الإعدادات الحالية
- ✅ دعم الإدخال الكامل (دخول وخروج) أو الجزئي (دخول فقط)
- ✅ التحقق من عدم تكرار السجلات

### 4. تحديث السجلات الموجودة

**الملف:** `src/app/api/attendance/manual/[id]/route.ts`

```typescript
export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // ... التحقق من الصلاحيات

    // جلب الإعدادات للحصول على ساعات العمل الحالية
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // تحديد وقت الدخول (الجديد أو الموجود)
    const finalCheckInTime = checkInTime ? new Date(checkInTime) : existingRecord.checkInTime;
    
    // حساب وقت انتهاء العمل المتوقع الجديد = وقت الدخول + ساعات العمل
    const expectedEndTime = new Date(finalCheckInTime.getTime() + (workingHours * 60 * 60 * 1000));

    // تحديث السجل مع وقت انتهاء العمل المتوقع الجديد
    const updatedRecord = await prisma.attendanceRecord.update({
      where: { id: recordId },
      data: {
        checkInTime: finalCheckInTime,
        checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
        exitType: exitType || existingRecord.exitType,
        emp_working_hrs: expectedEndTime.getTime(), // تحديث وقت انتهاء العمل المتوقع
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تحديث سجل الحضور بنجاح",
      record: {
        ...updatedRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
    });
  } catch (error) {
    // ... معالجة الأخطاء
  }
}
```

**الميزات:**
- ✅ تحديث ساعات العمل عند تعديل السجل
- ✅ استخدام الإعدادات الحالية وليس القديمة
- ✅ الحفاظ على البيانات الأخرى في السجل

### 5. حساب مدة الخروج في التقارير

**الملفات:** `src/app/api/attendance/report/route.ts` و `src/app/api/attendance/range-report/route.ts`

```typescript
function calculateBreakTime(movements: Record<string, any>[], settings: Record<string, any> | null): number {
  if (movements.length === 0) return 0;

  let totalBreakTime = 0;
  const workEndTime = settings?.workEndTime || '17:00';
  const workingHours = settings?.workingHours || 7;

  // ترتيب الحركات حسب entryNumber
  const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);

  // حساب مدة الخروج بين الحركات
  for (let i = 0; i < sortedMovements.length - 1; i++) {
    const currentMovement = sortedMovements[i];
    const nextMovement = sortedMovements[i + 1];

    // إذا كان الموظف خرج في الحركة الحالية ودخل في الحركة التالية
    if (currentMovement.checkOutTime && nextMovement.checkInTime) {
      const checkOut = new Date(currentMovement.checkOutTime);
      const nextCheckIn = new Date(nextMovement.checkInTime);

      // حساب الفترة بين الخروج والدخول التالي
      const breakDuration = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
      if (breakDuration > 0) {
        totalBreakTime += breakDuration;
      }
    }
  }

  // للحركة الأخيرة: إذا خرج الموظف ولم يعد
  const lastMovement = sortedMovements[sortedMovements.length - 1];
  if (lastMovement.checkOutTime) {
    const firstMovement = sortedMovements[0];
    
    // إذا كان هناك دخول في الحركة الأولى وخروج في الحركة الأخيرة ولم يعد
    if (firstMovement.checkInTime) {
      // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
      if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
        // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
        const expectedEndTime = new Date(firstMovement.emp_working_hrs);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // إذا خرج قبل الوقت المتوقع
        if (checkOut < expectedEndTime) {
          const breakTime = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      } else {
        // النظام القديم: حساب بناءً على ساعات العمل الثابتة
        const checkIn = new Date(firstMovement.checkInTime);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // حساب الوقت الفعلي الذي قضاه الموظف في العمل
        const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        
        // إذا كان الوقت الفعلي أقل من عدد ساعات العمل المطلوبة
        if (actualWorkHours < workingHours) {
          // مدة الخروج = عدد ساعات العمل - الوقت الفعلي
          const breakTime = workingHours - actualWorkHours;
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      }
    } else {
      // إذا لم يكن هناك دخول، استخدم المنطق القديم
      const lastCheckOut = new Date(lastMovement.checkOutTime);
      
      // إنشاء وقت انتهاء العمل لنفس التاريخ
      const workEnd = new Date(lastCheckOut);
      const [hours, minutes] = workEndTime.split(':');
      workEnd.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // إذا كان الخروج قبل انتهاء العمل ولم يعد
      if (lastCheckOut < workEnd) {
        const remainingTime = (workEnd.getTime() - lastCheckOut.getTime()) / (1000 * 60 * 60);
        totalBreakTime += remainingTime;
      }
    }
  }

  return totalBreakTime;
}
```

## سيناريوهات الاستخدام

### 1. موظف يدخل في وقت مبكر

```mermaid
sequenceDiagram
    participant Employee as الموظف
    participant Security as موظف الأمن
    participant System as النظام

    Employee->>Security: طلب تسجيل دخول في 7:30 ص
    Security->>System: تسجيل حضور
    System->>System: حساب وقت انتهاء العمل = 7:30 ص + 7 ساعات = 2:30 م
    System-->>Security: تم التسجيل - وقت انتهاء العمل المتوقع: 2:30 م
```

### 2. موظف يدخل في وقت متأخر

```mermaid
sequenceDiagram
    participant Employee as الموظف
    participant Security as موظف الأمن
    participant System as النظام

    Employee->>Security: طلب تسجيل دخول في 9:00 ص
    Security->>System: تسجيل حضور
    System->>System: حساب وقت انتهاء العمل = 9:00 ص + 7 ساعات = 4:00 م
    System-->>Security: تم التسجيل - وقت انتهاء العمل المتوقع: 4:00 م
```

### 3. حساب مدة الخروج المرنة

```mermaid
sequenceDiagram
    participant Employee as الموظف
    participant System as النظام

    Note over Employee,System: الموظف دخل في 8:00 ص، وقت انتهاء العمل المتوقع: 3:00 م
    Employee->>System: خروج في 1:00 م
    System->>System: حساب مدة الخروج = 3:00 م - 1:00 م = ساعتان
    System-->>Employee: مدة الخروج: ساعتان
```

## الفوائد والمزايا

### 1. مرونة في أوقات العمل
- **المشكلة**: أوقات عمل ثابتة لا تناسب جميع الموظفين
- **الحل**: حساب وقت انتهاء العمل بناءً على وقت الدخول الفعلي
- **النتيجة**: مرونة أكبر للموظفين مع الحفاظ على مدة العمل المطلوبة

### 2. دقة في حساب مدة الخروج
- **المشكلة**: حساب مدة الخروج بناءً على وقت ثابت
- **الحل**: استخدام وقت انتهاء العمل المتوقع لكل موظف
- **النتيجة**: حسابات أكثر دقة وعدالة

### 3. التوافق العكسي
- **المشكلة**: السجلات القديمة قد لا تعمل مع النظام الجديد
- **الحل**: دعم النظامين القديم والجديد في نفس الكود
- **النتيجة**: انتقال سلس دون فقدان البيانات

### 4. ثبات البيانات التاريخية
- **المشكلة**: تغيير إعدادات ساعات العمل يؤثر على السجلات القديمة
- **الحل**: حفظ وقت انتهاء العمل المتوقع مع كل سجل
- **النتيجة**: السجلات التاريخية تحتفظ بالحسابات الصحيحة

## التحقق والاختبار

### سيناريوهات الاختبار

#### 1. تسجيل حضور مرن بواسطة الأمن
```bash
# طلب POST إلى /api/security/check-in
{
  "employeeId": "employee_id_here"
}

# النتيجة المتوقعة (دخول في 8:30 ص مع 7 ساعات عمل)
{
  "message": "تم تسجيل الدخول بنجاح",
  "record": {
    "id": "record_id",
    "checkInTime": "2024-12-30T08:30:00.000Z",
    "emp_working_hrs": 1735556200000, // timestamp لـ 3:30 م
    "expectedEndTime": "2024-12-30T15:30:00.000Z",
    // ... باقي البيانات
  }
}
```

#### 2. إدخال حضور يدوي مرن
```bash
# طلب POST إلى /api/attendance/manual
{
  "employeeId": "employee_id_here",
  "date": "2024-12-30",
  "checkInTime": "2024-12-30T09:00:00",
  "checkOutTime": "2024-12-30T16:00:00",
  "exitType": "OFFICIAL",
  "entryNumber": 1
}

# النتيجة المتوقعة (دخول في 9:00 ص مع 7 ساعات عمل)
{
  "message": "تم إنشاء سجل الحضور بنجاح",
  "record": {
    "emp_working_hrs": 1735558800000, // timestamp لـ 4:00 م
    "expectedEndTime": "2024-12-30T16:00:00.000Z",
    // ... باقي البيانات
  }
}
```

#### 3. حساب مدة الخروج المرنة
```bash
# موظف دخل في 8:00 ص (وقت انتهاء العمل المتوقع: 3:00 م)
# خرج في 1:00 م ولم يعد

# النتيجة المتوقعة في التقرير
{
  "totalBreakTime": 2.0, // ساعتان (3:00 م - 1:00 م)
  "movements": [
    {
      "checkInTime": "2024-12-30T08:00:00.000Z",
      "checkOutTime": "2024-12-30T13:00:00.000Z",
      "emp_working_hrs": 1735549200000 // timestamp لـ 3:00 م
    }
  ]
}
```

## الملفات المحدثة

### APIs المحدثة
1. `src/app/api/security/check-in/route.ts` - تسجيل حضور مرن بواسطة الأمن
2. `src/app/api/attendance/manual/route.ts` - إدخال حضور يدوي مرن
3. `src/app/api/attendance/manual/[id]/route.ts` - تحديث سجل حضور مرن
4. `src/app/api/attendance/report/route.ts` - حساب مدة الخروج المرنة
5. `src/app/api/attendance/range-report/route.ts` - حساب مدة الخروج المرنة للفترات

### قاعدة البيانات
1. `prisma/schema.prisma` - تحديث تعليق حقل emp_working_hrs

### الوثائق
1. `docs/attendance-working-hours-sync.md` - هذا الملف (محدث)

## ملاحظات مهمة

### التوافق العكسي
- ✅ الحقل `emp_working_hrs` يدعم النظامين القديم والجديد
- ✅ السجلات القديمة تعمل بالنظام الثابت
- ✅ السجلات الجديدة تستخدم النظام المرن

### الأمان
- ✅ التحقق من الصلاحيات قبل تسجيل الحضور
- ✅ التحقق من صحة البيانات المدخلة
- ✅ منع تكرار السجلات في نفس التاريخ

### الأداء
- ✅ جلب الإعدادات مرة واحدة فقط لكل طلب
- ✅ استخدام timestamp لتحسين سرعة الحسابات
- ✅ فهرسة مناسبة لجداول قاعدة البيانات

### المشاكل المعروفة
- ⚠️ صفحة الحضور تحتاج إلى تحديث لدعم النظام المرن (مشكلة syntax في الكود)
- ⚠️ واجهة المستخدم تحتاج إلى عرض وقت انتهاء العمل المتوقع

---

**تاريخ التطبيق:** ديسمبر 2024  
**المطور:** نظام إدارة الحضور AttendPro  
**النسخة:** 2.0.0  
**الحالة:** ✅ مطبق في APIs ومختبر | ⚠️ يحتاج تحديث واجهة المستخدم

## تحقيق الهدف

### الهدف الأساسي
يوفر هذا النظام مرونة في أوقات بداية العمل مع ضمان عدد ساعات العمل المطلوبة. بدلاً من الجدول الثابت (8:00 ص - 3:00 م للجميع)، يمكن للموظفين البدء في أوقات مختلفة والنظام يحسب وقت انتهاء العمل المتوقع تلقائياً.

### الهدف الثاني
يوفر النظام حسابات أكثر دقة لمدة الخروج، مما يؤدي إلى تتبع أكثر دقة لمدة العمل المطلوبة.

### الهدف الثالث
يوفر النظام ثبات البيانات التاريخية، مما يضمن أن مدة العمل المطلوبة تحتفظ بالحسابات الصحيحة على المدى الطويل.

### الهدف الرابع
يوفر النظام التوافق العكسي، مما يضمن أن السجلات القديمة يمكنها العمل مع النظام الجديد دون خسائر.

## التحقق والاختبار

### سيناريو اختبار 1: موظف يدخل في الوقت المعتاد
- **الدخول**: 8:00 صباحاً
- **وقت الانتهاء المتوقع**: 3:00 مساءً
- **النتيجة**: ✅ يعمل النظام كالمعتاد

### سيناريو اختبار 2: موظف يدخل متأخراً
- **الدخول**: 9:30 صباحاً  
- **وقت الانتهاء المتوقع**: 4:30 مساءً
- **النتيجة**: ✅ النظام يتكيف مع الوقت الجديد

### سيناريو اختبار 3: موظف يدخل مبكراً
- **الدخول**: 7:00 صباحاً
- **وقت الانتهاء المتوقع**: 2:00 مساءً  
- **النتيجة**: ✅ يمكن إنهاء العمل مبكراً

## حالات الاستخدام

### 1. الدوام المرن
- موظفون لديهم التزامات شخصية
- مواعيد طبية أو عائلية
- تجنب ساعات الذروة المرورية

### 2. العمل بنظام الورديات
- تغطية ساعات عمل مختلفة
- خدمة العملاء على مدار اليوم
- التناوب في المسؤوليات

### 3. الحالات الطارئة
- وصول متأخر بسبب ظروف خارجة عن السيطرة
- إنهاء مبكر بسبب ظروف شخصية
- تعويض ساعات في أيام أخرى

## الهجرة والتوافق

### السجلات القديمة
- تحتفظ بالقيم الرقمية (7.0, 8.0, etc.)
- النظام يتعرف عليها تلقائياً
- تستخدم منطق الحساب القديم

### السجلات الجديدة  
- تحتوي على طوابع زمنية (timestamps)
- تستخدم منطق الحساب المرن
- توفر دقة أكبر في التتبع

### عدم الحاجة لهجرة البيانات
- النظام يعمل مع كلا النوعين
- لا حاجة لتحديث السجلات السابقة
- انتقال سلس للنظام الجديد

## الأمان والتحقق

### التحقق من صحة البيانات
```javascript
// التحقق من كون القيمة طابع زمني صالح
const isTimestamp = typeof emp_working_hrs === 'number' && emp_working_hrs > 1000000000000;
```

### منع التلاعب
- التحقق من منطقية الأوقات
- ضمان عدم تجاوز الحد الأقصى لساعات العمل
- تسجيل جميع العمليات للمراجعة

## اعتبارات الأداء

### قاعدة البيانات
- استخدام فهارس على حقول التاريخ
- تحسين استعلامات التجميع
- ذاكرة تخزين مؤقت للإعدادات

### الواجهة الأمامية
- عرض الأوقات بالتوقيت المحلي
- تحديث تلقائي للحسابات
- واجهة مستخدم متجاوبة

## دليل استكشاف الأخطاء

### "الطابع الزمني يبدو رقماً غريباً"
**المشكلة**: القيمة `1748519355676` تبدو وكأنها "رقم سحري"

**الحل**: هذا طبيعي! إنه طابع زمني يمثل:
- **التاريخ**: 29 مايو 2025
- **الوقت**: 3:49:15 مساءً (بتوقيت مسقط)
- **المعنى**: وقت انتهاء العمل المتوقع للموظف

**التحقق**: 
```javascript
console.log(new Date(1748519355676).toLocaleString('ar-SA', {timeZone: 'Asia/Muscat'}));
// النتيجة: ٢٩‏/٥‏/٢٠٢٥، ٣:٤٩:١٥ م
```

### "الحسابات غير صحيحة"
**التحقق من**:
1. إعدادات ساعات العمل في النظام
2. صحة التوقيت المحلي للخادم
3. تطبيق التوقيت الصيفي إن وجد

### "السجلات القديمة لا تعمل"
**الحل**: النظام يدعم التوافق التلقائي:
```javascript
if (typeof emp_working_hrs === 'number' && emp_working_hrs < 1000000000000) {
  // نظام قديم - استخدم منطق المدة
} else {
  // نظام جديد - استخدم منطق الطابع الزمني
}
```

## المراجع التقنية

### ملفات المصدر المحدثة
- `src/app/api/security/check-in/route.ts`
- `src/app/api/attendance/manual/route.ts`
- `src/app/api/attendance/manual/[id]/route.ts`
- `src/app/api/attendance/report/route.ts`
- `src/app/api/attendance/range-report/route.ts`

### قاعدة البيانات
- `prisma/schema.prisma`: تحديث تعليق حقل `emp_working_hrs`

### الواجهة الأمامية
- `src/app/dashboard/attendance/page.tsx`: دعم النظام المرن

---

*تم تطوير هذا النظام لتوفير مرونة أكبر في إدارة أوقات العمل مع الحفاظ على دقة التتبع ومتطلبات العمل.*

## ✅ التحقق من سلامة البيانات التاريخية (Historical Data Integrity)

### المشكلة المحتملة
عند تغيير إعدادات ساعات العمل في النظام، يمكن أن تتأثر حسابات مدة الخروج للسجلات القديمة إذا كان النظام يعتمد على الإعدادات الحالية بدلاً من البيانات المحفوظة مع كل سجل.

### الحل المطبق
```javascript
// النظام الجديد: يستخدم emp_working_hrs المحفوظ مع كل سجل
if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
  // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع عند إنشاء السجل
  const expectedEndTime = new Date(firstMovement.emp_working_hrs);
  const checkOut = new Date(lastMovement.checkOutTime);
  
  // حساب مدة الخروج بناءً على الوقت المحفوظ وليس الإعدادات الحالية
  if (checkOut < expectedEndTime) {
    const breakTime = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
    if (breakTime > 0) {
      totalBreakTime += breakTime;
    }
  }
}
```

### التحقق من النتائج
تم اختبار النظام بالسيناريو التالي:

#### سجل قديم (من شهر مضى):
- **وقت الدخول**: 8:49 صباحاً
- **وقت الخروج**: 2:49 مساءً
- **وقت انتهاء العمل المتوقع المحفوظ**: 3:49 مساءً
- **مدة الخروج الصحيحة**: ساعة واحدة

#### تغيير الإعدادات بعد إنشاء السجل:
- **الإعدادات القديمة**: 7 ساعات عمل
- **الإعدادات الجديدة**: 8 ساعات عمل

#### النتائج:
- ✅ **النظام المرن (الصحيح)**: 1.00 ساعة
- ❌ **لو استخدم الإعدادات القديمة**: 1.00 ساعة 
- ❌ **لو استخدم الإعدادات الجديدة**: 2.00 ساعة

### الخلاصة
✅ **تم ضمان سلامة البيانات التاريخية!**
- حسابات مدة الخروج تستخدم `emp_working_hrs` المحفوظ مع كل سجل
- تغيير الإعدادات لا يؤثر على السجلات التاريخية  
- البيانات تبقى دقيقة بغض النظر عن الإعدادات الحالية
- لا يحدث فساد في البيانات أو تقارير تاريخية خاطئة

### الملفات المحدثة للتحقق من السلامة
1. `src/app/api/attendance/range-report/route.ts` - تضمين `emp_working_hrs` في بيانات الحركات
2. `src/app/api/attendance/report/route.ts` - تضمين `emp_working_hrs` في بيانات الحركات
3. `src/app/dashboard/attendance/page.tsx` - يدعم النظام المرن بالفعل

---