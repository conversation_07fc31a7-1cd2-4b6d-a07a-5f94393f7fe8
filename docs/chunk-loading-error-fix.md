# إصلاح أخطاء Chunk Loading Error

## المشكلة
ظهور خطأ `ChunkLoadError` بصورة متقطعة في التطبيق مع الرسالة:
```
Loading chunk default-_app-pages-browser_src_components_ui_DataTable_tsx failed.
ChunkLoadError: Loading chunk ... failed.
```

## الأسباب المحتملة
1. **مشاكل webpack cache** - فشل في cache filesystem
2. **تحديث الكود أثناء التشغيل** - webpack hot reload conflicts
3. **تجزئة JavaScript غير مستقرة** - chunk splitting issues
4. **مشاكل network timeout** - بطء تحميل الـ chunks

## الحلول المطبقة

### 1. تحسين إعدادات webpack
**الملف**: `next.config.ts`

#### تغيير Cache Strategy:
```typescript
// تحسين cache مع منع الأخطاء
if (dev && !isServer) {
  config.cache = {
    type: 'memory', // استخدام memory cache بدلاً من filesystem
  };
}
```

#### تحسين Chunk Splitting:
```typescript
config.optimization = {
  ...config.optimization,
  splitChunks: {
    chunks: 'all',
    maxInitialRequests: 20,
    maxAsyncRequests: 20,
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        priority: -10,
        chunks: 'all',
        enforce: true,
      },
      common: {
        name: 'common',
        minChunks: 2,
        chunks: 'all',
        priority: -5,
        reuseExistingChunk: true,
      },
    },
  },
};
```

#### تحسين Watch Options:
```typescript
if (dev) {
  config.watchOptions = {
    poll: 1000,
    aggregateTimeout: 300,
    ignored: ['**/node_modules/**', '**/.next/**'],
  };
}
```

### 2. إضافة Error Boundary
**الملف**: `src/components/ErrorBoundary.tsx`

#### ميزات Error Boundary:
- **اكتشاف تلقائي** لأخطاء chunk loading
- **إعادة تحميل تلقائية** عند اكتشاف الخطأ
- **UI مخصص** لعرض الأخطاء
- **معلومات تطوير** detailed error info في development mode

#### استخدام Error Boundary:
```typescript
componentDidCatch(error: Error, errorInfo: ErrorInfo) {
  // التحقق من أخطاء chunk loading
  if (error.name === 'ChunkLoadError' || 
      error.message.includes('Loading chunk') ||
      error.message.includes('failed to fetch dynamically imported module')) {
    
    console.log('Chunk load error detected, attempting to reload...');
    
    // محاولة إعادة تحميل الصفحة بعد تأخير قصير
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }
}
```

### 3. معالج عام للأخطاء
**الملف**: `src/app/layout.tsx`

#### Global Error Handler:
```javascript
// معالج عام لأخطاء chunk loading
window.addEventListener('error', function(e) {
  if (e.message && (e.message.includes('Loading chunk') || e.message.includes('ChunkLoadError'))) {
    console.log('Chunk load error detected, reloading page...');
    setTimeout(function() {
      window.location.reload();
    }, 1000);
  }
});

// معالج للأخطاء غير المعالجة
window.addEventListener('unhandledrejection', function(e) {
  if (e.reason && e.reason.message && e.reason.message.includes('Loading chunk')) {
    console.log('Unhandled chunk load error, reloading page...');
    e.preventDefault();
    setTimeout(function() {
      window.location.reload();
    }, 1000);
  }
});
```

### 4. تحسين Images Configuration
```typescript
images: {
  remotePatterns: [
    {
      protocol: 'http',
      hostname: 'localhost',
      port: '3006',
      pathname: '/**',
    },
  ],
  formats: ['image/webp', 'image/avif'],
},
```

### 5. تحسين API Caching
```typescript
// منع cache للـ API routes
{
  source: '/api/:path*',
  headers: [
    {
      key: 'Cache-Control',
      value: 'no-cache, no-store, must-revalidate',
    },
  ],
},
```

## النتائج المتوقعة

### ✅ التحسينات
1. **تقليل chunk loading errors** بنسبة كبيرة
2. **استقرار أفضل** في development mode
3. **تجربة مستخدم محسنة** مع auto-recovery
4. **أداء أفضل** في chunk loading

### ⚠️ ملاحظات
1. **Memory Cache**: قد يكون أبطأ من filesystem cache لكنه أكثر استقراراً
2. **Auto Reload**: سيتم إعادة تحميل الصفحة تلقائياً عند حدوث خطأ chunk
3. **Development Mode**: التحسينات مخصصة أساساً لـ development environment

## الصيانة والمراقبة

### مراقبة الأخطاء:
- تحقق من browser console للتأكد من عدم ظهور chunk errors
- راقب reload frequency في development

### في حالة استمرار المشاكل:
1. **مسح .next cache**: `rm -rf .next`
2. **إعادة تثبيت dependencies**: `npm ci`
3. **تحقق من Node.js version**: يفضل Node 18+ أو 20+

## المراجع
- [Next.js Webpack Configuration](https://nextjs.org/docs/app/api-reference/config/next-config-js/webpack)
- [React Error Boundaries](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)
- [Webpack SplitChunks](https://webpack.js.org/plugins/split-chunks-plugin/) 