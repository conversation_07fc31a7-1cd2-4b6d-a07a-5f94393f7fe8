# إضافة حقل عدد ساعات العمل إلى الإعدادات

## نظرة عامة

تم إضافة حقل جديد "عدد ساعات العمل" إلى إعدادات النظام لتحديد إجمالي ساعات العمل اليومية المطلوبة.

## الهدف من التحسين

- **تحديد ساعات العمل الكاملة**: إمكانية تحديد إجمالي ساعات العمل اليومية (مثل 7 أو 8 ساعات)
- **مرونة في الإعدادات**: فصل مفهوم "الحد الأدنى لساعات العمل" عن "إجمالي ساعات العمل"
- **حسابات دقيقة**: استخدام هذا الحقل في حسابات الحضور والغياب والإحصائيات

## التطبيق التقني

### 1. تحديث قاعدة البيانات

```sql
-- إضافة حقل workingHours إلى جدول Settings
ALTER TABLE Settings ADD COLUMN workingHours FLOAT DEFAULT 7.0;
```

### 2. تحديث نموذج Prisma

```prisma
model Settings {
  // ... الحقول الموجودة
  workHoursRequired Float    @default(4.0)  // الحد الأدنى لساعات العمل
  workingHours      Float    @default(7.0)  // إجمالي ساعات العمل اليومية
  // ... باقي الحقول
}
```

### 3. تحديث واجهة الإعدادات

#### Schema التحقق
```typescript
const settingsSchema = z.object({
  // ... الحقول الأخرى
  workHoursRequired: z.number().min(1, "الحد الأدنى لساعات العمل مطلوب"),
  workingHours: z.number().min(1, "عدد ساعات العمل مطلوب"),
  // ... باقي الحقول
});
```

#### واجهة المستخدم
```tsx
<div>
  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
    عدد ساعات العمل *
  </label>
  <input
    type="number"
    step="0.5"
    min="1"
    max="12"
    className="input"
    {...register("workingHours", { valueAsNumber: true })}
    disabled={saving}
  />
  {errors.workingHours && (
    <p className="text-red-500 text-sm mt-1">{errors.workingHours.message}</p>
  )}
</div>
```

### 4. تحديث API

#### GET - جلب الإعدادات
```typescript
// إضافة القيمة الافتراضية عند عدم وجود إعدادات
const defaultSettings = await prisma.settings.create({
  data: {
    // ... الحقول الأخرى
    workingHours: 7,
    // ... باقي الحقول
  },
});
```

#### PUT - تحديث الإعدادات
```typescript
// التحقق من صحة القيمة
if (workingHours < 1 || workingHours > 12) {
  return NextResponse.json(
    { error: "عدد ساعات العمل يجب أن يكون بين 1 و 12 ساعة" },
    { status: 400 }
  );
}

// تحديث الإعدادات
const updatedSettings = await prisma.settings.update({
  where: { id: existingSettings.id },
  data: {
    // ... الحقول الأخرى
    workingHours,
    // ... باقي الحقول
  },
});
```

## الفرق بين الحقلين

| الحقل | الوصف | الاستخدام | مثال |
|-------|-------|----------|-------|
| `workHoursRequired` | الحد الأدنى لساعات العمل | تحديد الحد الأدنى المطلوب للحضور | 4 ساعات |
| `workingHours` | إجمالي ساعات العمل | تحديد إجمالي ساعات العمل اليومية | 7 ساعات |

## الاستخدامات المستقبلية

### 1. حسابات الحضور
```typescript
// حساب نسبة الحضور
const attendancePercentage = (actualHours / settings.workingHours) * 100;
```

### 2. تقارير الإنتاجية
```typescript
// حساب ساعات العمل المفقودة
const missingHours = settings.workingHours - actualHours;
```

### 3. حسابات الراتب
```typescript
// حساب الراتب بناءً على ساعات العمل
const dailySalary = monthlySalary / (settings.workingHours * workingDaysPerMonth);
```

## التحقق والاختبار

### سيناريوهات الاختبار

1. **إضافة قيمة صحيحة**:
   - إدخال قيمة بين 1-12 ساعة
   - التحقق من حفظ القيمة بنجاح

2. **قيم غير صحيحة**:
   - إدخال قيمة أقل من 1 أو أكبر من 12
   - التحقق من ظهور رسالة خطأ مناسبة

3. **القيمة الافتراضية**:
   - التحقق من تعيين 7 ساعات كقيمة افتراضية
   - التحقق من عمل النظام مع الإعدادات الجديدة

### النتائج المتوقعة

- ✅ حفظ الحقل الجديد في قاعدة البيانات
- ✅ عرض الحقل في واجهة الإعدادات
- ✅ التحقق من صحة القيم المدخلة
- ✅ استخدام القيمة الافتراضية (7 ساعات)
- ✅ عمل جميع وظائف الإعدادات الأخرى بشكل طبيعي

## الملفات المحدثة

1. `prisma/schema.prisma` - إضافة حقل workingHours
2. `src/app/dashboard/settings/page.tsx` - تحديث واجهة الإعدادات
3. `src/app/api/settings/route.ts` - تحديث API للإعدادات
4. `docs/working-hours-setting.md` - وثائق التحسين

## ملاحظات مهمة

- **التوافق العكسي**: الحقل الجديد لا يؤثر على الوظائف الموجودة
- **القيمة الافتراضية**: 7 ساعات كما هو مطلوب
- **التحقق**: قيم بين 1-12 ساعة فقط مقبولة
- **المرونة**: يمكن تعديل القيمة حسب احتياجات المؤسسة

---

**تاريخ التطبيق**: 25/05/2025  
**الإصدار**: 1.0  
**المطور**: فريق تطوير AttendPro  
**الحالة**: مطبق ومختبر 