# تحويل خط Tajawal إلى نسخة محلية

## نظرة عامة
تم تحويل خط Tajawal من Google Fonts CDN إلى نسخة محلية لتحسين سرعة تحميل التطبيق وتقليل الاعتماد على الخدمات الخارجية.

## المتطلبات
- ملفات خط Tajawal بجميع الأوزان (200-900)
- إعداد CSS للخطوط المحلية
- إزالة روابط Google Fonts CDN

## خطوات التنفيذ

### 1. تنزيل ملفات الخط
تم تنزيل ملفات خط Tajawal من مستودع Google Fonts على GitHub:

```bash
# تنزيل جميع أوزان خط Tajawal
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Light.ttf" -OutFile "public/fonts/tajawal-300.ttf"
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Regular.ttf" -OutFile "public/fonts/tajawal-400.ttf"
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Medium.ttf" -OutFile "public/fonts/tajawal-500.ttf"
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Bold.ttf" -OutFile "public/fonts/tajawal-700.ttf"
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-ExtraBold.ttf" -OutFile "public/fonts/tajawal-800.ttf"
Invoke-WebRequest -Uri "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Black.ttf" -OutFile "public/fonts/tajawal-900.ttf"
```

### 2. إعداد CSS للخطوط المحلية
تم إنشاء ملف `src/styles/fonts.css` مع تعريفات @font-face:

```css
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-400.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

### 3. إزالة Google Fonts CDN
تم إزالة الروابط التالية:
- من `src/app/layout.tsx`: روابط preconnect و stylesheet
- من `src/app/globals.css`: @import للخط من Google

### 4. الملفات المحدثة
- `src/styles/fonts.css` - تعريفات الخطوط المحلية
- `src/app/layout.tsx` - إزالة روابط Google Fonts
- `src/app/globals.css` - إزالة @import
- `public/fonts/` - ملفات الخط المحلية

## الفوائد

### تحسين الأداء
- **تقليل طلبات الشبكة**: لا حاجة للاتصال بخوادم Google
- **تحميل أسرع**: الخطوط محملة محلياً
- **لا توجد تأخيرات DNS**: عدم الحاجة لحل عناوين Google

### الموثوقية
- **عمل بدون إنترنت**: التطبيق يعمل حتى بدون اتصال
- **عدم الاعتماد على خدمات خارجية**: لا تأثر بانقطاع خدمات Google
- **تحكم كامل**: إدارة كاملة لملفات الخط

### الخصوصية
- **عدم تتبع المستخدمين**: لا إرسال بيانات لـ Google
- **امتثال GDPR**: تقليل مشاركة البيانات مع أطراف ثالثة

## الاستخدام

### في CSS
```css
.my-text {
  font-family: var(--font-tajawal);
  font-weight: 400; /* أو أي وزن من 300-900 */
}
```

### في Tailwind CSS
```html
<div class="font-tajawal font-medium">النص باللغة العربية</div>
```

## أوزان الخط المتاحة
- **300**: Light (خفيف)
- **400**: Regular (عادي)
- **500**: Medium (متوسط)
- **700**: Bold (غامق)
- **800**: ExtraBold (غامق جداً)
- **900**: Black (أسود)

## ملاحظات تقنية
- تم استخدام `font-display: swap` لتحسين الأداء
- الخطوط تدعم RTL بشكل كامل
- متوافق مع جميع المتصفحات الحديثة
- حجم الملفات محسن للويب

## الصيانة
- تحديث الخطوط: تنزيل إصدارات جديدة من مستودع Google Fonts
- إضافة أوزان جديدة: تحديث ملف fonts.css
- تحسين الأداء: تحويل TTF إلى WOFF2 عند الحاجة

## الأمان
- جميع ملفات الخط من مصادر موثوقة (Google Fonts)
- لا توجد مخاطر أمنية من الملفات المحلية
- ترخيص مفتوح المصدر (OFL 1.1) 