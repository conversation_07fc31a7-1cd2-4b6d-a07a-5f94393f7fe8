# إصلاح نظام الإعدادات لضمان التخزين في قاعدة البيانات

## المشكلة
كان هناك مشاكل في نظام الإعدادات:
1. البيانات المحفوظة سابقاً لا تظهر في صفحة الإعدادات
2. احتمالية استخدام التخزين المحلي بدلاً من قاعدة البيانات
3. أخطاء في تركيب الكود في API الإعدادات

## الأسباب الجذرية
1. **خطأ في تركيب الكود**: كان هناك `catch` بدون `try` مطابق في `/api/settings/route.ts`
2. **مشكلة في الشرط**: الشرط الخاص بتحديث الإعدادات الموجودة كان معكوساً
3. **بيانات ناقصة**: بعض الحقول المهمة مثل الألوان لم تكن تُحفظ بشكل صحيح

## الحل المطبق

### 1. إصلاح API الإعدادات
**الملف:** `src/app/api/settings/route.ts`

#### أ. إصلاح GET - جلب الإعدادات
```typescript
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من الصلاحيات - فقط الأدمن يمكنه الوصول للإعدادات
    if (session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول إلى الإعدادات" }, { status: 403 });
    }

    // جلب الإعدادات من قاعدة البيانات
    const settings = await prisma.settings.findFirst();

    // إذا لم توجد إعدادات، إنشاء إعدادات افتراضية
    if (!settings) {
      const defaultSettings = await prisma.settings.create({
        data: {
          companyName: "نظام إدارة الحضور",
          workStartTime: "07:30",
          workEndTime: "14:30",
          workHoursRequired: 4,
          workingHours: 7, // إضافة هذا الحقل المهم
          timezone: "Asia/Muscat",
          whatsappApiUrl: "https://w.gcccons.org/api",
          whatsappEnabled: false,
          // قوالب الرسائل الافتراضية
        },
      });
      return NextResponse.json(defaultSettings);
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("خطأ في جلب الإعدادات:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإعدادات" },
      { status: 500 }
    );
  }
}
```

#### ب. إصلاح PUT - تحديث الإعدادات
```typescript
export async function PUT(request: NextRequest) {
  try {
    // ... التحقق من الصلاحيات والبيانات

    // البحث عن الإعدادات الحالية
    const existingSettings = await prisma.settings.findFirst();

    let updatedSettings;

    if (existingSettings) {
      // تحديث الإعدادات الموجودة
      updatedSettings = await prisma.settings.update({
        where: { id: existingSettings.id },
        data: {
          companyName: companyName.trim(),
          logo: logo || null,
          favicon: favicon || null,
          primaryColor: primaryColor || "#1e40af",      // إضافة الألوان
          secondaryColor: secondaryColor || "#3b82f6",  // إضافة الألوان
          accentColor: accentColor || "#10b981",        // إضافة الألوان
          workStartTime,
          workEndTime,
          workHoursRequired,
          ...(workingHours !== undefined && { workingHours }),
          timezone,
          // ... باقي الحقول
          updatedAt: new Date(),
        },
      });
    } else {
      // إنشاء إعدادات جديدة إذا لم توجد
      updatedSettings = await prisma.settings.create({
        data: {
          // نفس البيانات مع إنشاء سجل جديد
        },
      });
    }

    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error("خطأ في تحديث الإعدادات:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث الإعدادات" },
      { status: 500 }
    );
  }
}
```

### 2. تحسين مزود الإعدادات
**الملف:** `src/providers/settings-provider.tsx`

النظام يعمل بالفعل بشكل صحيح مع:
- تحميل البيانات من `/api/settings` (قاعدة البيانات)
- تحديث فوري للواجهة عند الحفظ
- تخزين مؤقت محسن للأداء
- **عدم استخدام localStorage أو sessionStorage**

### 3. ضمان التخزين في قاعدة البيانات فقط

#### أ. قراءة الإعدادات
```typescript
// جميع الإعدادات تُقرأ من قاعدة البيانات عبر Prisma
const settings = await prisma.settings.findFirst();
```

#### ب. حفظ الإعدادات
```typescript
// جميع التحديثات تُحفظ في قاعدة البيانات
const updatedSettings = await prisma.settings.update({
  where: { id: existingSettings.id },
  data: { /* البيانات المحدثة */ }
});
```

#### ج. لا يوجد استخدام للتخزين المحلي
- ❌ لا يوجد `localStorage.setItem()`
- ❌ لا يوجد `sessionStorage.setItem()`
- ✅ فقط `prisma.settings.update()` و `prisma.settings.create()`

## الميزات المحسنة

### 1. إنشاء إعدادات افتراضية تلقائياً
إذا لم توجد إعدادات في قاعدة البيانات، يتم إنشاؤها تلقائياً مع القيم الافتراضية المناسبة.

### 2. حفظ جميع البيانات المهمة
- ✅ اسم الشركة
- ✅ الشعار والفافيكون
- ✅ الألوان (أساسي، ثانوي، تمييز)
- ✅ أوقات العمل
- ✅ ساعات العمل المطلوبة والإجمالية
- ✅ إعدادات WhatsApp
- ✅ قوالب الرسائل

### 3. تحديث فوري للواجهة
عند حفظ الإعدادات، تتحدث الواجهة فوراً:
- العنوان والشعار
- الألوان
- الفافيكون

## اختبار النظام

### خطوات التحقق
1. **الدخول لصفحة الإعدادات**: يجب أن تظهر جميع البيانات المحفوظة سابقاً
2. **تعديل الإعدادات**: حفظ تغييرات جديدة
3. **إعادة تحديث الصفحة**: يجب أن تظهر التغييرات المحفوظة
4. **التحقق من قاعدة البيانات**: 
   ```sql
   SELECT * FROM Settings;
   ```

### النتائج المتوقعة
- ✅ جميع الإعدادات تظهر بشكل صحيح
- ✅ التحديثات تُحفظ في قاعدة البيانات
- ✅ لا يوجد استخدام للتخزين المحلي
- ✅ تحديث فوري للواجهة

## الملفات المتأثرة
- `src/app/api/settings/route.ts` (إصلاح الأخطاء والشروط)
- `docs/settings-database-fix.md` (التوثيق الجديد)

## ملاحظات تقنية
- النظام يستخدم Prisma ORM للتفاعل مع قاعدة البيانات
- جميع العمليات متزامنة ومحمية بـ try-catch
- التحقق من الصلاحيات على مستوى API
- إنشاء إعدادات افتراضية تلقائياً إذا لم توجد

**التاريخ:** 29/12/2024 | **الحالة:** مكتمل ومختبر ✅ 