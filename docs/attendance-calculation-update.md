# تحديث منطق حساب الحضور في الداشبورد

## نظرة عامة

تم تحديث منطق حساب الحضور في جميع واجهات الداشبورد ليعتمد بالكامل على بيانات نموذج الحضور والانصراف (`AttendanceRecord`) بدلاً من الاعتماد على منطق مختلط. هذا التحديث يضمن التوافق مع نظام التقارير ويوفر دقة أكبر في حساب الحضور.

## التغييرات الرئيسية

### 1. المنطق الجديد لحساب الحضور

#### النظام القديم
```typescript
// كان يعتمد على exitType
if (!record.checkOutTime) {
  // في المكتب = حضور كامل
} else if (record.exitType === 'PERSONAL') {
  // خروج شخصي = حضور جزئي
} else {
  // خروج رسمي = حضور كامل
}
```

#### النظام الجديد
```typescript
// يعتمد على مجموع ساعات العمل الفعلية
const totalWorkingHours = records.reduce((total, record) => {
  if (record.checkInTime && record.checkOutTime) {
    const hours = (checkOut - checkIn) / (1000 * 60 * 60);
    return total + hours;
  }
  return total;
}, 0);

if (totalWorkingHours >= minWorkingHours) {
  // حضور: ساعات العمل >= الحد الأدنى
} else if (totalWorkingHours > 0) {
  // حضور جزئي: ساعات العمل < الحد الأدنى
} else {
  // غائب: لا توجد ساعات عمل
}
```

### 2. الإعدادات المستخدمة

- **`workHoursRequired`**: الحد الأدنى لساعات العمل (افتراضياً 4 ساعات)
- **`workingHours`**: إجمالي ساعات العمل اليومية (افتراضياً 7 ساعات)

### 3. تصنيف الحضور الجديد

#### حضور (Full Attendance)
- **الشرط**: مجموع ساعات العمل >= الحد الأدنى لساعات العمل
- **يشمل**: 
  - الموظفين الذين أكملوا الحد الأدنى من ساعات العمل
  - الموظفين الذين لا يزالون في المكتب (لم يسجلوا خروج)

#### حضور جزئي (Partial Attendance)
- **الشرط**: 0 < مجموع ساعات العمل < الحد الأدنى لساعات العمل
- **يشمل**: الموظفين الذين عملوا ساعات أقل من الحد الأدنى المطلوب

#### في إجازة (On Leave)
- **الشرط**: لديه إجازة معتمدة تشمل اليوم الحالي
- **المصدر**: جدول `LeaveRequest` بحالة `APPROVED`

#### غائب (Absent)
- **الشرط**: لا يوجد سجل حضور ولا يوجد إجازة معتمدة
- **يشمل**: الموظفين الذين لم يسجلوا أي حضور في اليوم

## الملفات المحدثة

### 1. APIs الداشبورد
- `src/app/api/dashboard/admin-stats/route.ts`
- `src/app/api/dashboard/manager-stats/route.ts`
- `src/app/api/dashboard/employee-stats/route.ts`

### 2. واجهات الداشبورد
- `src/app/dashboard/page.tsx` (السوبر أدمن)
- `src/app/dashboard/manager/page.tsx` (المدير)
- `src/app/dashboard/employee/page.tsx` (الموظف)

## المزايا الجديدة

### 1. دقة أكبر في الحساب
- يأخذ في الاعتبار جميع حركات الدخول والخروج في اليوم
- يحسب مجموع ساعات العمل الفعلية بدلاً من الاعتماد على نوع الخروج

### 2. مرونة في التصنيف
- يمكن تعديل الحد الأدنى لساعات العمل من الإعدادات
- يدعم الحركات المتعددة في اليوم الواحد

### 3. التوافق مع التقارير
- نفس المنطق المستخدم في تقارير الحضور
- ضمان التطابق بين الداشبورد والتقارير

### 4. دعم النظام المرن
- يدعم ساعات العمل المرنة (`emp_working_hrs`)
- يتوافق مع النظام الجديد لساعات العمل المتغيرة

## أمثلة عملية

### مثال 1: موظف بحركات متعددة
```
الدخول الأول: 08:00 - الخروج: 12:00 (4 ساعات)
الدخول الثاني: 13:00 - الخروج: 16:00 (3 ساعات)
المجموع: 7 ساعات

النتيجة: حضور (7 >= 4 ساعات الحد الأدنى)
```

### مثال 2: موظف بحضور جزئي
```
الدخول: 09:00 - الخروج: 12:00 (3 ساعات)
المجموع: 3 ساعات

النتيجة: حضور جزئي (3 < 4 ساعات الحد الأدنى)
```

### مثال 3: موظف لا يزال في المكتب
```
الدخول: 08:00 - لم يخرج بعد
الوقت الحالي: 14:00 (6 ساعات حتى الآن)

النتيجة: حضور (6 >= 4 ساعات الحد الأدنى)
```

## التحديثات في الواجهة

### 1. عرض معلومات إضافية
- الحد الأدنى لساعات العمل
- إجمالي ساعات العمل اليومية
- مجموع ساعات العمل لكل موظف

### 2. تحسين الوصف
- إضافة توضيح أن الحساب يعتمد على ساعات العمل الفعلية
- عرض معايير التصنيف في الواجهة

### 3. دقة أكبر في النسب المئوية
- حساب النسب بناءً على البيانات الفعلية
- عرض تفاصيل أكثر للموظفين

## الاختبار والتحقق

### 1. التحقق من التطابق
```bash
# مقارنة نتائج الداشبورد مع التقارير
curl /api/dashboard/admin-stats
curl /api/attendance/report?date=2024-01-01
```

### 2. اختبار الحالات المختلفة
- موظف بحركة واحدة
- موظف بحركات متعددة
- موظف لا يزال في المكتب
- موظف في إجازة
- موظف غائب

### 3. التحقق من الإعدادات
- تغيير الحد الأدنى لساعات العمل
- تغيير إجمالي ساعات العمل
- التأكد من تطبيق التغييرات

## الصيانة والتطوير المستقبلي

### 1. مراقبة الأداء
- مراقبة أوقات الاستجابة للـ APIs
- تحسين الاستعلامات إذا لزم الأمر

### 2. التحديثات المحتملة
- إضافة المزيد من المعايير للتصنيف
- دعم قواعد حضور مخصصة لكل قسم
- تحسين عرض البيانات في الواجهة

### 3. التوثيق
- تحديث دليل المستخدم
- إضافة أمثلة للمطورين
- توثيق أي تغييرات مستقبلية

## الخلاصة

هذا التحديث يضمن:
- **الدقة**: حساب دقيق للحضور بناءً على ساعات العمل الفعلية
- **التوافق**: تطابق كامل بين الداشبورد والتقارير
- **المرونة**: دعم للإعدادات المختلفة وساعات العمل المرنة
- **الوضوح**: عرض واضح للمعايير والنتائج في الواجهة 