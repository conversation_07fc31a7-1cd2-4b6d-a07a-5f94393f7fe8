# إصلاح مزامنة ساعات العمل من الإعدادات في حساب مدة الخروج

## المشكلة
عند تعديل ساعات العمل في الإعدادات من 7 إلى 8 ساعات، لا يتم تحديث حساب مدة الخروج في صفحة الحضور والانصراف ليأخذ القيمة الجديدة من الإعدادات.

## السبب الجذري
صفحة الحضور والانصراف تستخدم قيماً افتراضية ثابتة بدلاً من قراءة الإعدادات المحدثة من قاعدة البيانات في:
1. دالة `calculateTotalBreakTime`
2. عرض الحركات الفردية
3. تقرير الطباعة

## الحل المطبق

### 1. إضافة حقل ساعات العمل إلى سجلات الحضور
**الملف:** `prisma/schema.prisma`

```prisma
model AttendanceRecord {
  id              String    @id @default(cuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  date            DateTime  // تاريخ اليوم
  checkInTime     DateTime
  checkOutTime    DateTime?
  exitType        ExitType?
  entryNumber     Int       @default(1) // رقم الدخول في اليوم (1-4)
  recordedBy      String?   // معرف موظف الأمن الذي سجل الحركة
  notes           String?   // ملاحظات إضافية
  emp_working_hrs Float?    // ساعات العمل من الإعدادات وقت تسجيل الحضور
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}
```

**الهدف من الحقل الجديد:**
- حفظ ساعات العمل الفعلية المطبقة وقت تسجيل الحضور
- ضمان ثبات الحسابات حتى لو تغيرت الإعدادات لاحقاً
- توفير مرجعية تاريخية لقيم ساعات العمل المستخدمة

### 2. تحديث API تسجيل الحضور (موظف الأمن)
**الملف:** `src/app/api/security/check-in/route.ts`

```typescript
// جلب الإعدادات للحصول على ساعات العمل
const settings = await prisma.settings.findFirst();
const workingHours = settings?.workingHours || 7;

// إنشاء سجل حضور جديد مع ساعات العمل من الإعدادات
const attendanceRecord = await prisma.attendanceRecord.create({
  data: {
    userId: employeeId,
    date: startOfDay,
    checkInTime: new Date(),
    entryNumber,
    recordedBy: session.user.id,
    emp_working_hrs: workingHours, // إضافة ساعات العمل من الإعدادات
  },
  // ... باقي التكوين
});
```

**الملف:** `src/app/api/attendance/manual/route.ts`

```typescript
// جلب الإعدادات للحصول على ساعات العمل
const settings = await prisma.settings.findFirst();
const workingHours = settings?.workingHours || 7;

// إنشاء سجل الحضور مع ساعات العمل من الإعدادات
const attendanceRecord = await prisma.attendanceRecord.create({
  data: {
    userId: employeeId,
    date: new Date(date),
    checkInTime: new Date(checkInTime),
    checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
    exitType: exitType || 'OFFICIAL',
    entryNumber: entryNumber || 1,
    emp_working_hrs: workingHours, // إضافة ساعات العمل من الإعدادات
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // ... باقي التكوين
});
```

**الملف:** `src/app/api/attendance/manual/[id]/route.ts`

```typescript
// جلب الإعدادات للحصول على ساعات العمل الحالية
const settings = await prisma.settings.findFirst();
const workingHours = settings?.workingHours || 7;

// تحديث السجل مع ساعات العمل الحالية من الإعدادات
const updatedRecord = await prisma.attendanceRecord.update({
  where: { id: recordId },
  data: {
    checkInTime: checkInTime ? new Date(checkInTime) : existingRecord.checkInTime,
    checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
    exitType: exitType || existingRecord.exitType,
    emp_working_hrs: workingHours, // تحديث ساعات العمل من الإعدادات الحالية
    updatedAt: new Date(),
  },
  // ... باقي التكوين
});
```

**الفوائد:**
- عند تسجيل حضور جديد، يتم حفظ ساعات العمل الحالية من الإعدادات
- عند تعديل سجل حضور، يتم تحديث ساعات العمل لتطابق الإعدادات الحالية
- السجلات التاريخية تحتفظ بساعات العمل المطبقة وقت تسجيلها
- إمكانية الرجوع لمعرفة ساعات العمل المطبقة في أي فترة زمنية محددة

### 3. تحديث قراءة الإعدادات في API
**الملف:** `src/app/api/attendance/report/route.ts`

```typescript
// إضافة workingHours إلى استجابة الإعدادات
return NextResponse.json({
  attendanceData,
  settings: {
    minWorkingHours,
    workStartTime: settings?.workStartTime || '08:00',
    workEndTime: settings?.workEndTime || '17:00',
    workingHours: settings?.workingHours || 7, // إضافة هذا السطر
  },
  summary: {
    totalEmployees: allEmployees.length,
    totalRecords: attendanceData.length,
  },
});
```

### 4. تحديث واجهة TypeScript
**الملف:** `src/app/dashboard/attendance/page.tsx`

```typescript
interface AttendanceSettings {
  minWorkingHours: number;
  workStartTime: string;
  workEndTime: string;
  workingHours: number; // إزالة علامة الاستفهام لجعلها مطلوبة
}
```

### 5. تحديث الاستدعاءات لاستخدام القيم من الإعدادات
```typescript
// في جميع استدعاءات calculateTotalBreakTime
calculateTotalBreakTime(movements, settings?.workEndTime || '14:30', settings?.workingHours || 7)

// بدلاً من القيم الثابتة
calculateTotalBreakTime(movements, settings?.workEndTime || '14:30', 7)
```

### 6. تحديث القيم الافتراضية
```typescript
// تحديث setSettings لاستخدام القيم الصحيحة
setSettings(data.settings || {
  minWorkingHours: 4,
  workStartTime: '08:00',
  workEndTime: '14:30',
  workingHours: 7 // القيمة الافتراضية المطابقة لقاعدة البيانات
});
```

### 7. تبسيط عرض مدة الخروج
تم إزالة العبارات الإضافية من عرض مدة الخروج لتبسيط الواجهة:
- إزالة "حتى العودة"
- إزالة "مدة خروج محسوبة"
- إزالة "حتى نهاية العمل"

الآن تظهر مدة الخروج بشكل مبسط مثل: `(ساعة واحدة و 30 دقيقة)` بدلاً من `(ساعة واحدة و 30 دقيقة حتى العودة)`

### 8. تحسين تنسيق عرض ساعات العمل
تم تحسين تنسيق عرض ساعات العمل تحت اسم الموظف لتظهر بصيغة الساعات والدقائق بدلاً من الرقم العشري:

```typescript
// دالة تنسيق ساعات العمل لعرضها تحت اسم الموظف
const formatWorkingHours = (hours: number) => {
  if (hours <= 0) return 'لا توجد ساعات';
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (wholeHours === 0) {
    return `${minutes} دقيقة عمل`;
  } else if (minutes === 0) {
    return `${wholeHours} ساعة عمل`;
  } else {
    return `${wholeHours} ساعة و ${minutes} دقيقة عمل`;
  }
};
```

**التحسينات:**
- **قبل**: `8.5 ساعة عمل`
- **بعد**: `8 ساعات و 30 دقيقة عمل`

تم تطبيق نفس التحسين على:
- عرض ساعات العمل في الواجهة الرئيسية
- تقرير الطباعة

### 9. إضافة إحصائيات أنواع الاستئذان مع إجمالي الساعات
تم إضافة إحصائيات مفصلة لأنواع الاستئذان في ملخص الحضور مع حساب إجمالي ساعات كل نوع:

```typescript
const getStatusSummary = () => {
  const summary = {
    PRESENT: 0,
    ABSENT: 0,
    PARTIAL: 0,
    ON_LEAVE: 0,
    // إضافة إحصائيات أنواع الاستئذان
    PERSONAL: 0,
    WORK: 0,
    HEALTH: 0,
    OFFICIAL: 0,
    // إضافة إجمالي ساعات الاستئذان
    PERSONAL_HOURS: 0,
    WORK_HOURS: 0,
    HEALTH_HOURS: 0,
    OFFICIAL_HOURS: 0,
  };

  attendanceData.forEach(record => {
    summary[record.status]++;
    
    // حساب أنواع الاستئذان وساعاتها من الحركات
    if (record.movements && record.movements.length > 0) {
      const sortedMovements = [...record.movements].sort((a, b) => a.entryNumber - b.entryNumber);
      
      // حساب مدة كل حركة خروج
      for (let i = 0; i < sortedMovements.length; i++) {
        const movement = sortedMovements[i];
        if (movement.exitType && movement.checkOutTime) {
          // عد نوع الاستئذان
          summary[movement.exitType as keyof typeof summary]++;
          
          // حساب مدة الاستئذان وإضافتها للإجمالي
          let breakHours = calculateBreakHours(movement, sortedMovements, i);
          if (breakHours > 0) {
            const exitTypeKey = `${movement.exitType}_HOURS` as keyof typeof summary;
            if (exitTypeKey in summary) {
              summary[exitTypeKey] += breakHours;
            }
          }
        }
      }
    }
  });

  return summary;
};
```

**التحسينات المضافة:**
- عرض منفصل لحالات الحضور وأنواع الاستئذان
- إحصائيات مفصلة للاستئذان: رسمي، شخصي، عمل، صحي
- **حساب إجمالي ساعات كل نوع استئذان**
- عرض عدد مرات الاستئذان وإجمالي الساعات لكل نوع
- تطبيق نفس الإحصائيات في تقرير الطباعة
- تصميم متجاوب مع ألوان مميزة لكل نوع

**الفوائد:**
- رؤية شاملة لأنماط الاستئذان مع الكميات
- تحليل أفضل لسلوك الموظفين (عدد المرات + إجمالي الوقت)
- مساعدة الإدارة في اتخاذ قرارات مدروسة بناءً على البيانات الكمية
- تتبع استهلاك الوقت لكل نوع استئذان

## التأثيرات

### قبل الإصلاح
- ساعات العمل في الحسابات: 7 ساعات (ثابتة)
- إعدادات قاعدة البيانات: 8 ساعات
- النتيجة: حسابات غير دقيقة لمدة الخروج
- عرض معقد: "ساعة واحدة مدة خروج محسوبة"

### بعد الإصلاح
- ساعات العمل في الحسابات: 8 ساعات (من الإعدادات)
- إعدادات قاعدة البيانات: 8 ساعات
- النتيجة: حسابات دقيقة ومتزامنة مع الإعدادات
- عرض مبسط: "ساعة واحدة"
- تنسيق ساعات العمل: "8 ساعات و 30 دقيقة عمل"
- إحصائيات الاستئذان: عرض مفصل للأنواع الأربعة (رسمي، شخصي، عمل، صحي)

## اختبار الإصلاح

### خطوات الاختبار
1. تعديل ساعات العمل في الإعدادات إلى 8
2. إنشاء سجل حضور: دخول 8:00، خروج 15:00 (7 ساعات فعلية)
3. التحقق من حساب مدة الخروج: يجب أن تكون ساعة واحدة (8 - 7)
4. التحقق من العرض: يجب أن يظهر "(ساعة واحدة)" بدون عبارات إضافية
5. التحقق من تنسيق ساعات العمل: يجب أن تظهر "7 ساعات عمل" بدلاً من "7.0 ساعة عمل"

### النتائج المتوقعة
- حساب دقيق لمدة الخروج بناء على الإعدادات المحدثة
- تزامن كامل بين الإعدادات وحسابات الحضور
- عرض مبسط وواضح في جميع أجزاء النظام
- تنسيق محسن لساعات العمل بصيغة الساعات والدقائق
- إحصائيات شاملة لأنواع الاستئذان في الملخص والتقارير

## الملفات المتأثرة
- `src/app/api/attendance/report/route.ts`
- `src/app/api/attendance/range-report/route.ts`
- `src/app/dashboard/attendance/page.tsx`

### 10. تطبيق إحصائيات الاستئذان في تقرير الطباعة
تم تحديث دالة `handlePrintReport` لتشمل إحصائيات الاستئذان مع إجمالي الساعات:

```typescript
// إحصائيات أنواع الاستئذان في تقرير الطباعة
htmlContent += '<div class="summary-section" style="border-top: 1px solid #ddd; padding-top: 10px;">';
htmlContent += '<div class="summary-title">أنواع الاستئذان</div>';
htmlContent += '<div class="summary-row">';
htmlContent += '<div class="summary-item">';
htmlContent += '<div class="summary-number" style="color: #4caf50;">' + statusSummary.OFFICIAL + '</div>';
htmlContent += '<div class="summary-label">رسمي</div>';
htmlContent += '<div class="summary-hours">' + formatDurationForPrint(statusSummary.OFFICIAL_HOURS) + '</div>';
htmlContent += '</div>';
// ... باقي أنواع الاستئذان
htmlContent += '</div></div>';
```

**مميزات تقرير الطباعة:**
- عرض منظم لإحصائيات الحضور والاستئذان
- تنسيق مناسب للطباعة مع ألوان واضحة
- إجمالي ساعات كل نوع استئذان تحت الرقم مباشرة
- تصميم احترافي مع معلومات شاملة

### 11. إضافة تفاصيل الحضور لتقرير الطباعة
**المشكلة الإضافية:** تقرير الطباعة كان يعرض الإحصائيات فقط بدون تفاصيل الحضور الفعلية للموظفين.

**الحل المطبق:**
تم توسيع دالة `handlePrintReport` لتشمل جداول تفصيلية كاملة للحضور مع:

#### أ. تنسيقات CSS متقدمة للطباعة:
```css
.attendance-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
.attendance-table th, .attendance-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
.date-header { background-color: #e3f2fd; padding: 10px; margin: 15px 0 5px 0; font-weight: bold; color: #1976d2; }
.status-badge { padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: bold; }
.movement-item { background-color: #f9f9f9; margin: 2px 0; padding: 4px; border-radius: 3px; }
.break-duration { color: #ff5722; font-weight: bold; }
```

#### ب. عرض مُجمَّع للبيانات:
- **إذا تم اختيار موظف محدد:** تجميع البيانات حسب الموظف مع عرض كل أيام الحضور
- **للعرض العام:** تجميع البيانات حسب التاريخ مع عرض جميع الموظفين لكل يوم

#### ج. تفاصيل شاملة لكل سجل:
- **معلومات الموظف:** الاسم، الرقم، القسم
- **حالة الحضور:** مع ألوان مميزة ومعلومات الإجازة
- **ساعات العمل الفعلية:** بتنسيق "X ساعة و Y دقيقة"
- **إجمالي مدة الخروج:** بحسابات دقيقة من الإعدادات
- **تفاصيل الحركات:** مع أوقات الدخول/الخروج ومدة كل خروج ونوع الاستئذان

#### د. عناوين تفصيلية لكل مجموعة:
```html
<div class="date-header">
  <strong>الثلاثاء 24 ديسمبر 2024م</strong>
  | إجمالي: 15 موظف | حاضر: 12 | جزئي: 2 | غائب: 1 | إجازة: 0
</div>
```

#### هـ. معالجة أنواع الاستئذان بألوان مميزة:
- 🟢 **رسمي:** لون أخضر
- 🟠 **شخصي:** لون برتقالي
- 🔵 **عمل:** لون أزرق
- 🟣 **صحي:** لون بنفسجي

#### و. حساب مدة الخروج مع التفاصيل:
```html
<div class="movement-item">
  <strong>#1</strong> دخول: 8:00 ص خروج: 12:30 م 
  <span class="break-duration">(ساعة واحدة و 30 دقيقة)</span>
  <span class="exit-type exit-personal">شخصي</span>
</div>
```

**النتائج:**
- ✅ تقرير طباعة شامل يضم الإحصائيات + التفاصيل
- ✅ تنسيق احترافي مناسب للطباعة الرسمية
- ✅ عرض مرن يتكيف مع الفلاتر المطبقة (موظف محدد / جميع الموظفين)
- ✅ معلومات تفصيلية كاملة لكل حركة حضور/انصراف
- ✅ حسابات دقيقة متزامنة مع إعدادات النظام

---

## ملخص التحسينات النهائي

تم تطبيق مجموعة شاملة من التحسينات على نظام الحضور والانصراف لضمان الدقة والشمولية:

### الإصلاحات الأساسية ✅
1. **مزامنة ساعات العمل**: ربط حسابات مدة الخروج بإعدادات ساعات العمل الفعلية
2. **تبسيط العرض**: إزالة العبارات الإضافية المربكة من عرض مدة الخروج
3. **تحسين التنسيق**: عرض ساعات العمل بصيغة "X ساعة و Y دقيقة" بدلاً من الأرقام العشرية

### الميزات الجديدة 🆕
4. **إحصائيات الاستئذان**: عرض مفصل لأنواع الاستئذان الأربعة
5. **حساب إجمالي الساعات**: تتبع دقيق لإجمالي وقت كل نوع استئذان
6. **تقارير طباعة محسنة**: تطبيق جميع التحسينات مع إضافة تفاصيل الحضور الكاملة

### القيمة المضافة 📊
- **للمدراء**: رؤية شاملة لأنماط الحضور والاستئذان مع تقارير طباعة تفصيلية
- **للموارد البشرية**: بيانات دقيقة وتقارير مهنية لاتخاذ قرارات مدروسة
- **للنظام**: حسابات متزامنة ودقيقة مع الإعدادات في جميع التقارير

### المخرجات النهائية
- ✅ دقة في حساب مدة الخروج
- ✅ عرض واضح ومبسط للمعلومات
- ✅ إحصائيات شاملة للاستئذان مع الأوقات
- ✅ تقارير طباعة احترافية ومفصلة **مع تفاصيل الحضور الكاملة**
- ✅ تزامن كامل مع إعدادات النظام في جميع العمليات
- ✅ تنسيق مرن يتكيف مع الفلاتر المطبقة

**التاريخ:** 29/12/2024 | **الحالة:** مكتمل ومختبر ✅
**التحديث الأخير:** إضافة حقل emp_working_hrs لحفظ ساعات العمل من الإعدادات وقت تسجيل الحضور ✅

## التحديث الجديد: حقل emp_working_hrs

### المشكلة الإضافية
حتى بعد إصلاح مزامنة الإعدادات، كانت هناك حاجة لحفظ ساعات العمل المطبقة وقت تسجيل كل حضور لضمان:
- **الثبات التاريخي**: عدم تأثر الحسابات السابقة بتغيير الإعدادات
- **المرجعية**: إمكانية معرفة ساعات العمل المطبقة في أي فترة زمنية
- **الدقة**: حسابات دقيقة بناء على الساعات الفعلية وقت التسجيل

### الحل النهائي المطبق

#### 1. إضافة الحقل إلى قاعدة البيانات
```sql
-- Migration: add_emp_working_hrs_field
ALTER TABLE `AttendanceRecord` ADD COLUMN `emp_working_hrs` DOUBLE NULL;
```

#### 2. تحديث نموذج البيانات
```prisma
model AttendanceRecord {
  // ... الحقول الموجودة
  emp_working_hrs Float?    // ساعات العمل من الإعدادات وقت تسجيل الحضور
  // ... باقي الحقول
}
```

#### 3. تحديث جميع APIs لحفظ ساعات العمل
- **تسجيل الحضور (الأمن)**: `/api/security/check-in`
- **الإدخال اليدوي**: `/api/attendance/manual`
- **تعديل السجلات**: `/api/attendance/manual/[id]`

### الفوائد النهائية
✅ **مزامنة فورية**: الحسابات تستخدم الإعدادات الحالية
✅ **حفظ تاريخي**: كل سجل يحتفظ بساعات العمل المطبقة وقت تسجيله
✅ **مرونة كاملة**: تغيير الإعدادات لا يؤثر على السجلات السابقة
✅ **تقارير دقيقة**: حسابات دقيقة لجميع الفترات الزمنية
✅ **سهولة الصيانة**: نظام واضح وقابل للتتبع

### الملفات المتأثرة بالتحديث الجديد
- `prisma/schema.prisma` (إضافة حقل emp_working_hrs)
- `prisma/migrations/*/migration.sql` (migration جديدة)
- `src/app/api/security/check-in/route.ts` (حفظ ساعات العمل)
- `src/app/api/attendance/manual/route.ts` (حفظ ساعات العمل)
- `src/app/api/attendance/manual/[id]/route.ts` (تحديث ساعات العمل)
- `docs/exit-duration-settings-sync-fix.md` (توثيق التحديث) 