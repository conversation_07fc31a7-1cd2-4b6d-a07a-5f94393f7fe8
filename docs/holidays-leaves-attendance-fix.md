# إصلاح عرض الإجازات والعطل الرسمية في كشف الحضور

## المشكلة المبلغ عنها
1. **الثلاثاء 20 مايو** كانت إجازة رسمية ولكن ظهرت في القائمة
2. **الإجازات الشخصية المعتمدة** يجب أن تظهر في الكشف مع حالة "إجازة"

## الحل المطبق

### 1. تعديل API الخلفي
**الملف**: `src/app/api/attendance/range-report/route.ts`

#### إضافة استعلامات الإجازات والعطل:
```typescript
// جلب الإجازات المعتمدة في الفترة
const approvedLeaves = await prisma.leaveRequest.findMany({
  where: {
    status: 'APPROVED',
    userId: { in: employees.map(e => e.id) },
    OR: [
      { startDate: { gte: startDate, lte: endDate } },
      { endDate: { gte: startDate, lte: endDate } },
      { startDate: { lte: startDate }, endDate: { gte: endDate } }
    ]
  }
});

// جلب العطل الرسمية في الفترة
const officialHolidays = await prisma.officialHoliday.findMany({
  where: {
    OR: [
      { startDate: { gte: startDate, lte: endDate } },
      { endDate: { gte: startDate, lte: endDate } },
      { startDate: { lte: startDate }, endDate: { gte: endDate } }
    ]
  }
});
```

#### المنطق الجديد:
```typescript
// التحقق من العطل الرسمية - تخطي هذه الأيام
const isOfficialHoliday = officialHolidays.some(holiday => {
  const holidayStart = new Date(holiday.startDate);
  const holidayEnd = new Date(holiday.endDate);
  return currentDate >= holidayStart && currentDate <= holidayEnd;
});

if (isOfficialHoliday) {
  continue; // لا تظهر العطل الرسمية في الكشف
}

// التحقق من الإجازات المعتمدة - إظهارها بحالة "إجازة"
const employeeLeave = approvedLeaves.find(leave => {
  const leaveStart = new Date(leave.startDate);
  const leaveEnd = new Date(leave.endDate);
  return leave.userId === employee.id && 
         currentDate >= leaveStart && currentDate <= leaveEnd;
});

if (employeeLeave) {
  status = 'ON_LEAVE';
  leaveReason = employeeLeave.reason || '';
}
```

### 2. تعديل الواجهة الأمامية
**الملف**: `src/app/dashboard/attendance/page.tsx`

#### تحديث Interface:
```typescript
interface AttendanceData {
  // ... باقي الحقول
  status: 'PRESENT' | 'ABSENT' | 'PARTIAL' | 'ON_LEAVE';
  leaveReason?: string;
}
```

#### إضافة حالة الإجازة:
```typescript
const statusConfig = {
  PRESENT: { label: 'حاضر', color: 'bg-green-100 text-green-800' },
  ABSENT: { label: 'غائب', color: 'bg-red-100 text-red-800' },
  PARTIAL: { label: 'جزئي', color: 'bg-yellow-100 text-yellow-800' },
  ON_LEAVE: { label: 'إجازة', color: 'bg-blue-100 text-blue-800' },
};
```

#### عرض سبب الإجازة:
```tsx
{row.leaveReason && value === 'ON_LEAVE' && (
  <div className="text-xs text-gray-600 mt-1">
    {row.leaveReason}
  </div>
)}
```

### 3. تحديث الفلاتر والملخصات

#### إضافة فلتر الإجازة:
```html
<option value="ON_LEAVE">إجازة</option>
```

#### تحديث getStatusSummary:
```typescript
const summary = {
  PRESENT: 0,
  ABSENT: 0,
  PARTIAL: 0,
  ON_LEAVE: 0,
};
```

#### ملخص الحضور:
```tsx
<div className="flex items-center gap-2">
  <div className="text-2xl font-bold text-blue-600">{statusSummary.ON_LEAVE}</div>
  <div className="text-sm text-gray-600">إجازة</div>
</div>
```

### 4. تحديث تقرير الطباعة
- إضافة حالة "إجازة" إلى `statusText`
- إضافة عداد الإجازات في الملخص المطبوع

## السلوك الجديد

### ✅ العطل الرسمية:
- **لا تظهر** في كشف الحضور نهائياً
- يتم تخطيها بالكامل من التقرير
- مثال: الثلاثاء 20 مايو لن يظهر في القائمة

### ✅ الإجازات الشخصية المعتمدة:
- **تظهر** في كشف الحضور مع حالة "إجازة"
- عرض سبب الإجازة تحت الحالة
- تُحسب في الإحصائيات والملخصات
- متوفرة في فلتر البحث

### ✅ أيام العمل العادية:
- الأحد - الخميس: تظهر بحالات (حاضر، غائب، جزئي)
- الجمعة والسبت: لا تظهر (weekends)

## الفوائد

1. **دقة البيانات**: العطل الرسمية لا تختلط مع أيام العمل
2. **شفافية الإجازات**: الإجازات المعتمدة واضحة مع أسبابها
3. **سهولة المراجعة**: إحصائيات دقيقة لكل نوع حضور
4. **مرونة الفلترة**: إمكانية فلترة حسب نوع الحضور

## اختبار النتائج

### قبل التعديل:
- العطل الرسمية تظهر كأيام غياب ❌
- الإجازات المعتمدة لا تظهر ❌

### بعد التعديل:
- العطل الرسمية لا تظهر نهائياً ✅
- الإجازات المعتمدة تظهر بحالة "إجازة" مع السبب ✅
- فلترة دقيقة حسب نوع الحضور ✅
- إحصائيات شاملة ودقيقة ✅

## الملفات المعدلة

1. `src/app/api/attendance/range-report/route.ts` - منطق API
2. `src/app/dashboard/attendance/page.tsx` - واجهة المستخدم
3. `docs/holidays-leaves-attendance-fix.md` - هذا التوثيق

---
*تاريخ التحديث: 24 مايو 2025م* 