# دليل البيانات التجريبية - نظام الحضور والانصراف

## نظرة عامة
تم إنشاء نظام شامل لبذر البيانات التجريبية لجميع وحدات نظام الحضور والانصراف. يتضمن هذا النظام بيانات واقعية ومتنوعة لاختبار جميع ميزات النظام.

## محتويات البيانات التجريبية

### 👥 المستخدمين والأقسام
**إجمالي المستخدمين**: 19 مستخدم
- **1 مدير عام** (ADMIN)
- **1 مدير موارد بشرية** (HR) 
- **1 مدير تقنية معلومات** (MANAGER)
- **1 موظف أمن** (SECURITY)
- **15 موظف** (EMPLOYEE) موزعين على 4 أقسام

#### الأقسام المُنشأة
1. **قسم تقنية المعلومات** (6 موظفين) - برئاسة خالد البلوشي
2. **قسم الموارد البشرية** (4 موظفين) - برئاسة فاطمة الزدجالية
3. **قسم المحاسبة** (4 موظفين) - بدون رئيس قسم
4. **قسم التسويق** (3 موظفين) - بدون رئيس قسم

### 📋 سجلات الحضور
**إجمالي السجلات**: ~360 سجل (آخر 30 يوم عمل)

#### خصائص البيانات
- **نسبة الحضور**: 80% احتمال حضور عادي
- **تنوع أوقات الدخول**: بين 7:00 و 9:00 صباحاً
- **حركات إضافية**: 60% احتمال وجود خروج وعودة
- **أنواع الاستئذان**: توزيع عشوائي بين (رسمي، شخصي، عمل، صحي)
- **ساعات العمل المحفوظة**: كل سجل يحمل قيمة `emp_working_hrs = 7.0` من الإعدادات

#### تفاصيل الحركات
- **رقم الحركة**: من 1 إلى 4 حركات يومياً
- **مدة الخروج**: من 30 دقيقة إلى ساعتين
- **نسبة العودة**: 70% احتمال العودة بعد الخروج
- **المسجل بواسطة**: جميع السجلات مسجلة بواسطة موظف الأمن

### 🏖️ طلبات الإجازة
**إجمالي الطلبات**: 15 طلب

#### تنوع الطلبات
- **الفترة الزمنية**: من -30 إلى +30 يوم من اليوم الحالي
- **مدة الإجازات**: من 1 إلى 5 أيام
- **أسباب متنوعة**:
  - إجازة سنوية
  - ظروف شخصية
  - إجازة مرضية
  - سفر عائلي
  - حضور مناسبة
- **حالات الطلبات**: توزيع عشوائي بين (معلق، معتمد، مرفوض)

### 👥 طلبات الزوار
**إجمالي الطلبات**: 20 طلب

#### تفاصيل الزوار
- **أسماء واقعية**: 10 أسماء عمانية متنوعة
- **شركات متنوعة**: 7 شركات عمانية معروفة
- **أغراض الزيارة**: 9 أغراض مختلفة (اجتماعات، عروض، استشارات)
- **أوقات الزيارة**: توزيع بين 09:00 و 15:00
- **معلومات إضافية**:
  - مرافقين (30% احتمال)
  - معلومات السيارة (40% احتمال)

#### تسجيل الدخول والخروج
- **للزوار المعتمدين**: تسجيل أوقات دخول وخروج فعلية
- **مدة الزيارة**: من ساعة إلى 4 ساعات
- **نسبة الخروج**: 80% من الزوار سجلوا خروجهم

### 🌙 تصاريح العمل بعد الدوام
**إجمالي التصاريح**: 10 تصاريح

#### خصائص التصاريح
- **الفترة الزمنية**: من -10 إلى +10 يوم من اليوم الحالي
- **أوقات البداية**: بين 15:00 و 18:00
- **مدة العمل**: من 2 إلى 6 ساعات
- **أسباب متنوعة**:
  - إنهاء مشروع عاجل
  - صيانة نظام
  - اجتماع مهم
  - تدريب متقدم
  - إعداد تقرير

#### التسجيل الفعلي
- **للتصاريح المعتمدة**: تسجيل أوقات دخول وخروج فعلية
- **دقة التوقيت**: ±15 دقيقة من الوقت المخطط
- **نسبة الالتزام**: 90% سجلوا خروجهم

### 🗓️ الإجازات الرسمية
**إجمالي الإجازات**: 4 إجازات سنوية

#### الإجازات المُنشأة
1. **رأس السنة الميلادية** - 1 يناير
2. **عيد الفطر** - 21-23 أبريل  
3. **عيد الأضحى** - 28-30 يونيو
4. **العيد الوطني** - 18-19 نوفمبر

## بيانات تسجيل الدخول

### المسؤولين
```
المدير العام:
- البريد: <EMAIL>
- كلمة المرور: admin123
- الاسم: أحمد السالمي
- الرقم الوظيفي: EMP001

مدير الموارد البشرية:
- البريد: <EMAIL>
- كلمة المرور: hr123  
- الاسم: فاطمة الزدجالية
- الرقم الوظيفي: HR001

مدير تقنية المعلومات:
- البريد: <EMAIL>
- كلمة المرور: manager123
- الاسم: خالد البلوشي
- الرقم الوظيفي: IT001

موظف الأمن:
- البريد: <EMAIL>
- كلمة المرور: security123
- الاسم: محمد الفارسي
- الرقم الوظيفي: SEC001
```

### الموظفين
```
نمط البريد الإلكتروني:
- تقنية المعلومات: it.employee[1-5]@attendpro.com
- الموارد البشرية: hr.employee[1-3]@attendpro.com  
- المحاسبة: finance.employee[1-4]@attendpro.com
- التسويق: marketing.employee[1-3]@attendpro.com

كلمة المرور الموحدة: employee123

الأرقام الوظيفية:
- تقنية المعلومات: IT002-IT006
- الموارد البشرية: HR002-HR004
- المحاسبة: FIN001-FIN004
- التسويق: MKT001-MKT003
```

## تشغيل البيانات التجريبية

### المتطلبات المسبقة
```bash
# تأكد من وجود قاعدة البيانات
# تأكد من تشغيل الخادم المحلي
# تأكد من تطبيق جميع migrations
```

### خطوات التشغيل

#### 1. إعادة تعيين قاعدة البيانات
```bash
npx prisma migrate reset --force
```

#### 2. تشغيل البذر
```bash
npx prisma db seed
```

#### 3. التحقق من النتائج
```bash
# سيظهر ملخص مفصل للبيانات المُنشأة
📊 ملخص البيانات المُنشأة:
- 👥 المستخدمين: 19
- 🏢 الأقسام: 4 أقسام
- 📋 سجلات الحضور: ~360 سجل
- 🏖️ طلبات الإجازة: 15 طلب
- 👥 طلبات الزوار: 20 طلب
- 🌙 تصاريح العمل بعد الدوام: 10 تصاريح
- 🗓️ الإجازات الرسمية: 4 إجازات
```

## الميزات المطبقة

### 🔄 التحديث التلقائي لـ Prisma Client
```bash
# يتم تلقائياً بعد reset
✔ Generated Prisma Client (v6.8.2)
```

### 📊 إحصائيات ذكية
- توزيع عشوائي للحالات
- أوقات واقعية ومنطقية
- علاقات صحيحة بين الجداول
- دعم كامل لحقل `emp_working_hrs`

### 🎯 اختبار شامل للنظام
- جميع أنواع المستخدمين
- جميع حالات الحضور والانصراف
- جميع أنواع الطلبات والتصاريح
- سيناريوهات واقعية ومتنوعة

## استخدام البيانات التجريبية

### للتطوير
```typescript
// مثال: اختبار تقرير الحضور
- فلترة بقسم تقنية المعلومات
- فلترة بموظف محدد
- فلترة بفترة زمنية
- اختبار طباعة التقرير
```

### للعرض التوضيحي
```typescript
// سيناريوهات العرض
- عرض لوحة التحكم بإحصائيات متنوعة
- تصفح سجلات الحضور مع حركات متعددة
- إدارة طلبات الإجازة والموافقات
- تتبع الزوار والتصاريح
```

### للاختبار
```typescript
// اختبارات النظام
- اختبار جميع أنواع المستخدمين
- اختبار الصلاحيات والوصول
- اختبار التقارير والإحصائيات
- اختبار العمليات CRUD
```

## إرشادات الصيانة

### إضافة بيانات جديدة
```typescript
// في ملف prisma/seed.ts
// أضف بيانات جديدة في القسم المناسب
// احتفظ بالتنسيق والهيكل الموجود
```

### تحديث البيانات الموجودة
```typescript
// عدل القيم في seed.ts
// شغل prisma db seed مرة أخرى
// ستتم إعادة كتابة جميع البيانات
```

### حذف البيانات
```bash
# حذف البيانات التجريبية
npx prisma migrate reset --force

# إعادة إنشاء الجداول بدون بيانات
npx prisma migrate deploy
```

## الأمان والاعتبارات

### كلمات المرور
- جميع كلمات المرور مُشفرة بـ bcrypt
- استخدم كلمات مرور قوية في الإنتاج
- غير كلمات المرور الافتراضية بعد النشر

### البيانات الحساسة
- البيانات التجريبية آمنة للتطوير
- لا تحتوي على معلومات حقيقية
- مناسبة للعرض والاختبار

### الأداء
- البيانات محسنة للأداء
- العلاقات مُفهرسة بشكل صحيح
- حجم مناسب للتطوير (~1000 سجل إجمالي)

---

**تاريخ الإنشاء**: 29/12/2024  
**آخر تحديث**: 29/12/2024  
**الحالة**: ✅ جاهز للاستخدام

**الملفات ذات الصلة**:
- `prisma/seed.ts` - ملف البذر الرئيسي
- `prisma/schema.prisma` - نموذج قاعدة البيانات
- `docs/exit-duration-settings-sync-fix.md` - تفاصيل حقل emp_working_hrs 