# إدارة كلمات المرور

## نظرة عامة
تم إضافة ميزة تعديل كلمات المرور للمستخدمين والموظفين في نماذج التعديل.

## الميزات المضافة

### 1. تعديل كلمة المرور للمستخدمين
- **المسار**: `/dashboard/users/[id]/edit`
- **الصلاحيات**: مدير النظام (ADMIN) فقط
- **الوظائف**:
  - حقل كلمة المرور الجديدة (اختياري)
  - تشفير كلمة المرور باستخدام bcrypt
  - التحقق من أن كلمة المرور 6 أحرف على الأقل
  - رسالة تأكيد عند النجاح

### 2. تعديل كلمة المرور للموظفين
- **المسار**: `/dashboard/employees/[id]/edit`
- **الصلاحيات**: مدير النظام (ADMIN) أو الموارد البشرية (HR)
- **الوظائف**:
  - حقل كلمة المرور الجديدة (اختياري)
  - تشفير كلمة المرور باستخدام bcrypt
  - التحقق من أن كلمة المرور 6 أحرف على الأقل
  - رسالة تأكيد عند النجاح

## كيفية الاستخدام

### لتعديل كلمة مرور مستخدم:
1. انتقل إلى قائمة المستخدمين
2. اختر المستخدم المطلوب تعديله
3. انقر على "تعديل"
4. أدخل كلمة المرور الجديدة في الحقل المخصص
5. انقر على "حفظ التغييرات"

### لتعديل كلمة مرور موظف:
1. انتقل إلى قائمة الموظفين
2. اختر الموظف المطلوب تعديله
3. انقر على "تعديل"
4. أدخل كلمة المرور الجديدة في الحقل المخصص
5. انقر على "حفظ التغييرات"

## متطلبات كلمة المرور
- **الحد الأدنى**: 6 أحرف
- **التشفير**: bcrypt مع salt rounds = 12 للمستخدمين، 10 للموظفين
- **اختياري**: يمكن ترك الحقل فارغاً للاحتفاظ بكلمة المرور الحالية

## الأمان

### تشفير كلمات المرور
- يتم تشفير كلمات المرور باستخدام bcrypt
- لا يتم تخزين كلمات المرور كنص خام
- يتم التحقق من قوة كلمة المرور قبل التحديث

### التحقق من الصلاحيات
- المستخدمون: مدير النظام فقط
- الموظفون: مدير النظام أو الموارد البشرية

## التطبيق التقني

### ملفات تم تعديلها
- `src/app/dashboard/users/[id]/edit/page.tsx`
- `src/app/dashboard/employees/[id]/edit/page.tsx`
- `src/app/api/users/[id]/route.ts`
- `src/app/api/employees/[id]/route.ts`

### التغييرات في Frontend
- إضافة حقل كلمة المرور في النماذج
- إضافة التحقق من طول كلمة المرور
- إضافة رسائل النجاح والفشل

### التغييرات في Backend
- إضافة معالجة كلمة المرور في API
- تشفير كلمة المرور عند التحديث
- التحقق من متطلبات كلمة المرور

## رسائل النظام

### رسائل النجاح
- "تم تحديث المستخدم بنجاح"
- "تم تحديث المستخدم بنجاح وتم تغيير كلمة المرور"

### رسائل الخطأ
- "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
- "حدث خطأ في تحديث المستخدم"

## الملاحظات
- كلمة المرور اختيارية في التعديل
- إذا تُركت فارغة، تبقى كلمة المرور الحالية دون تغيير
- يتم عرض نص مساعد للمستخدم لتوضيح الاستخدام

## التاريخ
- **تاريخ الإضافة**: 24/05/2025
- **الإصدار**: 1.0.0
- **المطور**: تحديث النظام 