# تحسينات صفحة الحضور والانصراف

## ملخص التحسينات المطبقة

تم تطبيق ثلاث تحسينات رئيسية على صفحة الحضور والانصراف بناءً على طلب المستخدم:

### 1. إضافة نوع استئذان "صحي"
### 2. ترتيب عمود الحركات رقمياً (#1، #2، #3، إلخ)
### 3. تحسين حساب مدة الخروج

---

## التفاصيل التقنية

### 1. إضافة نوع الاستئذان "صحي"

#### الملفات المحدثة:

**1. صفحة الحضور الرئيسية** (`src/app/dashboard/attendance/page.tsx`)
- إضافة خيار "صحي" في فلتر البحث
- إضافة خيار "صحي" في نموذج الإدخال اليدوي  
- تحديث عرض الحركات لدعم اللون البنفسجي للاستئذان الصحي
- تحديث تقرير الطباعة لإضافة "صحي" في قاموس exitTypeText

**2. صفحة حضور الموظفين** (`src/app/dashboard/security/employee-attendance/page.tsx`)
- إضافة دعم عرض نوع "صحي" بلون بنفسجي

**3. صفحة تقارير الأمن** (`src/app/dashboard/security/reports/page.tsx`) 
- إضافة عرض "استئذان صحي" في جدول التقارير

#### ألوان نوع الاستئذان:
```typescript
{
  OFFICIAL: 'bg-green-100 text-green-700',    // رسمي - أخضر
  WORK: 'bg-blue-100 text-blue-700',          // عمل - أزرق  
  HEALTH: 'bg-purple-100 text-purple-700',    // صحي - بنفسجي
  PERSONAL: 'bg-orange-100 text-orange-700'   // شخصي - برتقالي
}
```

### 2. ترتيب عمود الحركات

#### المشكلة السابقة:
- الحركات كانت تُعرض بدون ترتيب محدد
- صعوبة في متابعة تسلسل دخول وخروج الموظف

#### الحل المطبق:
```typescript
// ترتيب الحركات حسب entryNumber قبل العرض
const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);
```

#### الأماكن المطبقة:
1. عرض الحركات في الجدول الرئيسي
2. عرض الحركات في نموذج التعديل  
3. تقرير الطباعة

### 3. تحسين حساب مدة الخروج

#### المنطق الجديد:

**للحركة مع حركة تالية:**
```
مدة الخروج = وقت دخول الحركة التالية - وقت خروج الحركة الحالية
```

**للحركة الأخيرة (بدون عودة):**
```
مدة الخروج = وقت نهاية العمل - وقت الخروج
```

**حالة خاصة:**
- إذا كان وقت الخروج بعد نهاية العمل، لا تُحتسب مدة خروج

#### مثال توضيحي:
```
الحركة 1: دخول 8:00 - خروج 9:00
الحركة 2: دخول 11:00 - خروج 12:00  
الحركة 3: دخول 1:00 - لم يعد

مدة الخروج:
- الحركة 1: 11:00 - 9:00 = 2 ساعة
- الحركة 2: 1:00 - 12:00 = 1 ساعة
- الحركة 3: 2:30 - 1:00 = 1.5 ساعة (حسب إعدادات نهاية العمل)
```

#### الكود المطبق:
```typescript
const calculateTotalBreakTime = (movements: Record<string, any>[], workEndTime: string = '14:30') => {
  if (!movements || movements.length === 0) return 0;

  const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);
  let totalBreakTime = 0;

  sortedMovements.forEach((movement, index) => {
    if (!movement.checkOutTime) return;

    const checkOut = new Date(movement.checkOutTime);
    const nextMovement = sortedMovements[index + 1];
    let nextCheckIn: Date;

    if (nextMovement && nextMovement.checkInTime) {
      // إذا كان هناك حركة تالية، احسب الفترة بينهما
      nextCheckIn = new Date(nextMovement.checkInTime);
    } else {
      // إذا لم يعد الموظف، احسب حتى نهاية العمل
      const [hours, minutes] = workEndTime.split(':');
      nextCheckIn = new Date(checkOut.getFullYear(), checkOut.getMonth(), checkOut.getDate(), parseInt(hours), parseInt(minutes), 0, 0);
      
      // إذا كان وقت الخروج بعد نهاية العمل، لا تحسب مدة خروج
      if (checkOut >= nextCheckIn) {
        return;
      }
    }

    if (checkOut < nextCheckIn) {
      const breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
      totalBreakTime += breakHours;
    }
  });

  return totalBreakTime;
};
```

---

## النتائج والفوائد

### 1. تحسين دقة البيانات
- حساب أدق لمدة الخروج يعكس الواقع الفعلي
- ترتيب منطقي للحركات يسهل المتابعة

### 2. تحسين تجربة المستخدم  
- إضافة نوع استئذان "صحي" يغطي المزيد من الحالات
- عرض مرتب ومنطقي للحركات

### 3. تحسين التقارير
- تقارير أكثر دقة ووضوح
- معلومات شاملة عن أنواع الاستئذان المختلفة

---

## الاختبار والتحقق

### سيناريوهات الاختبار:

**1. حركة واحدة كاملة:**
- دخول: 8:00، خروج: 14:30
- مدة الخروج: 0 (لا توجد فترة خروج)

**2. حركات متعددة:**
- الحركة 1: 8:00-9:00، الحركة 2: 11:00-14:30  
- مدة الخروج: 2 ساعة (11:00-9:00)

**3. خروج بدون عودة:**
- الحركة 1: 8:00-13:00
- مدة الخروج: 1.5 ساعة (14:30-13:00)

**4. خروج بعد انتهاء العمل:**
- الحركة 1: 8:00-15:00
- مدة الخروج: 0 (الخروج بعد انتهاء العمل)

---

## ملاحظات للمطورين

### استخدام الدوال المحسنة:
- استخدم `calculateTotalBreakTime()` لحساب إجمالي مدة الخروج
- تأكد من ترتيب الحركات باستخدام `sort()` قبل المعالجة
- استخدم الألوان المحددة لأنواع الاستئذان المختلفة

### إعدادات العمل:
- وقت نهاية العمل يُؤخذ من جدول Settings
- القيمة الافتراضية: 14:30 
- يمكن تخصيصها من صفحة الإعدادات

---

*تاريخ التطبيق: 25 مايو 2025م*
*المطور: AI Assistant*

---

## التحديثات والإصلاحات

### إصلاح حساب مدة الخروج (تحديث 2)
**التاريخ**: 25 مايو 2025م - المساء

**المشكلة**: 
كان هناك خطأ في إنشاء كائن Date لحساب نهاية العمل، مما أدى لحسابات خاطئة لمدة الخروج.

**الحل المطبق**:
```typescript
// الطريقة القديمة (خاطئة)
nextCheckIn = new Date(checkOut);
nextCheckIn.setHours(parseInt(hours), parseInt(minutes), 0, 0);

// الطريقة الجديدة (صحيحة)
nextCheckIn = new Date(checkOut.getFullYear(), checkOut.getMonth(), checkOut.getDate(), parseInt(hours), parseInt(minutes), 0, 0);
```

**الفرق**:
- الطريقة القديمة كانت تحتفظ بالثواني والميلي ثانية من وقت الخروج
- الطريقة الجديدة تضمن إنشاء وقت نهاية العمل بدقة (ثواني وميلي ثانية = 0)

**النتيجة**:
حساب دقيق 100% لمدة الخروج حسب المثال:
- الحركة 1: 8:00-9:00 → خروج 2 ساعة (9:00-11:00)  
- الحركة 2: 11:00-12:00 → خروج 1 ساعة (12:00-1:00)
- الحركة 3: 1:00-لم يعد → خروج 1.5 ساعة (1:00-2:30) 