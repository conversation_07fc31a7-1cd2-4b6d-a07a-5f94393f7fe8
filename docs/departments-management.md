# إدارة الأقسام ورؤساء الأقسام في نظام AttendPro

## نظرة عامة

هذا الدليل يوضح كيفية إدارة الأقسام وتعيين رؤساء الأقسام في نظام AttendPro، وحل مشكلة عدم ظهور رؤساء الأقسام.

## المشكلة المُحلولة

**المشكلة**: رئيس القسم لا يظهر في عمود إدارة الأقسام
**السبب**: عدم تعيين رؤساء أقسام للأقسام الجديدة
**الحل**: تحسين واجهة العرض وإضافة أدوات لتعيين رؤساء الأقسام

## ميزات النظام المحسنة

### 1. **عرض محسن لرؤساء الأقسام**

```typescript
// في src/app/dashboard/departments/page.tsx
{
  key: 'head',
  title: 'رئيس القسم',
  render: (value, row) => (
    value ? (
      <div className="flex items-start gap-3">
        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
          <FaCrown className="text-green-600 text-sm" />
        </div>
        <div>
          <div className="font-medium text-green-700 flex items-center gap-2">
            <FaUserTie className="text-sm" />
            {value.name}
          </div>
          <div className="text-sm text-gray-500">{value.email}</div>
        </div>
      </div>
    ) : (
      <div className="flex items-center gap-2 text-amber-600">
        <FaExclamationTriangle className="text-amber-500" />
        <div>
          <div className="font-medium">غير محدد</div>
          <div className="text-xs text-gray-500">يحتاج تعيين رئيس قسم</div>
        </div>
      </div>
    )
  )
}
```

### 2. **إحصائيات سريعة**

تم إضافة كروت إحصائية تظهر:
- ✅ عدد الأقسام التي لها رؤساء
- ⚠️ عدد الأقسام بدون رؤساء  
- 👥 إجمالي الموظفين

### 3. **تحسين نماذج التعديل**

```typescript
// فلترة المدراء المؤهلين لأن يكونوا رؤساء أقسام
{employees.filter(emp => 
  emp.role === 'MANAGER' || 
  emp.role === 'HR' || 
  emp.role === 'ADMIN'
).map((employee) => (
  <option key={employee.id} value={employee.id}>
    👨‍💼 {employee.name} - {employee.email} ({employee.role})
  </option>
))}
```

## كيفية إدارة رؤساء الأقسام

### إضافة رئيس قسم جديد

1. **اذهب إلى صفحة الأقسام**: `/dashboard/departments`
2. **اضغط على زر "تعديل"** بجانب القسم المطلوب
3. **اختر رئيس القسم** من القائمة المنسدلة
4. **احفظ التعديلات**

### تعيين رؤساء أقسام تلقائياً

يمكنك تشغيل السكريبت التالي لتعيين رؤساء أقسام تلقائياً:

```bash
node assign-department-heads.js
```

هذا السكريبت يقوم بـ:
- البحث عن الأقسام بدون رؤساء
- تعيين المدراء الموجودين في نفس القسم كرؤساء
- تعيين مدراء متاحين للأقسام التي لا تحتوي على مدراء

## هيكل قاعدة البيانات

### جدول الأقسام (Department)

```sql
CREATE TABLE Department (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(191) UNIQUE NOT NULL,
  headId VARCHAR(191) NULL,          -- مُعرف رئيس القسم
  createdAt DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updatedAt DATETIME(3) NOT NULL,
  
  -- علاقات خارجية
  FOREIGN KEY (headId) REFERENCES User(id)
);
```

### العلاقة مع جدول المستخدمين

```prisma
model Department {
  id        String   @id @default(cuid())
  name      String   @unique
  headId    String?                    // اختياري
  head      User?    @relation("DepartmentHead", fields: [headId], references: [id])
  users     User[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  // ... حقول أخرى
  headOfDepartment  Department[]  @relation("DepartmentHead")
  // ... باقي العلاقات
}
```

## API Endpoints

### GET `/api/departments`

**الوصف**: جلب جميع الأقسام مع معلومات رؤساء الأقسام

**Response Structure**:
```json
[
  {
    "id": "dept_id",
    "name": "اسم القسم",
    "headId": "user_id",
    "head": {
      "id": "user_id",
      "name": "اسم رئيس القسم",
      "email": "<EMAIL>"
    },
    "users": [...],
    "_count": {
      "users": 5
    }
  }
]
```

### PUT `/api/departments/{id}`

**الوصف**: تعديل قسم وتعيين رئيس قسم

**Request Body**:
```json
{
  "name": "اسم القسم الجديد",
  "headId": "user_id_for_head"  // أو null إذا لم يكن هناك رئيس قسم
}
```

## معايير اختيار رئيس القسم

### الأدوار المؤهلة:
- `ADMIN` - مدير النظام
- `HR` - موارد بشرية  
- `MANAGER` - مدير

### الشروط:
1. المستخدم يجب أن يكون نشطاً
2. يُفضل أن يكون من نفس القسم
3. لا يمكن أن يكون رئيساً لأكثر من قسم واحد (حسب تصميم النظام الحالي)

## حالات خاصة

### قسم بدون رئيس
- يظهر تحذير في واجهة المستخدم
- يُحتسب في إحصائيات "أقسام بدون رؤساء"
- يمكن تعديله في أي وقت

### تغيير رئيس القسم
1. اذهب لصفحة تعديل القسم
2. اختر رئيس قسم جديد
3. احفظ التعديلات
4. سيتم تحديث العلاقة تلقائياً

### حذف رئيس القسم
- إذا تم حذف مستخدم هو رئيس قسم، سيصبح `headId = null`
- القسم سيحتاج إلى تعيين رئيس جديد

## اختبار النظام

### التحقق من البيانات

```bash
# اختبار API الأقسام
node test-api-departments.js

# فحص بيانات قاعدة البيانات مباشرة
node test-departments.js
```

### حالات الاختبار

1. **قسم له رئيس**: يجب أن يظهر اسم وإيميل رئيس القسم
2. **قسم بدون رئيس**: يجب أن يظهر "غير محدد" مع رسالة تحذيرية
3. **تعيين رئيس قسم**: يجب حفظ التغيير وظهوره فوراً
4. **إزالة رئيس قسم**: يجب أن يعود لحالة "غير محدد"

## الصيانة والمراقبة

### مراقبة يومية:
- تأكد من أن جميع الأقسام المهمة لها رؤساء
- راجع الإحصائيات في لوحة التحكم

### صيانة دورية:
- تشغيل سكريبت تعيين رؤساء الأقسام شهرياً
- مراجعة صلاحيات رؤساء الأقسام

### تحديثات مستقبلية:
- [ ] إضافة إشعارات عند عدم وجود رئيس قسم
- [ ] تقارير أداء حسب رئيس القسم
- [ ] سماح بتعيين أكثر من رئيس قسم (نائب رئيس)

---

*تاريخ الإنشاء: 24 مايو 2025*
*آخر تحديث: 24 مايو 2025*
*حالة التطبيق: ✅ تم حل مشكلة عرض رؤساء الأقسام* 