# تحديث حساب مدة الخروج في نظام الحضور

## نظرة عامة
تم تحديث منطق حساب مدة الخروج في صفحة الحضور لتطبيق قاعدة جديدة: **إذا خرج الموظف ولم يعد، ستكون مدة الخروج = عدد ساعات العمل (من الإعدادات) - (وقت الخروج - وقت الدخول)**

## التغييرات المطبقة

### 1. تحديث واجهة الإعدادات (AttendanceSettings)
```typescript
interface AttendanceSettings {
  minWorkingHours: number;
  workStartTime: string;
  workEndTime: string;
  workingHours?: number; // إجمالي ساعات العمل اليومية - جديد
}
```

### 2. تحديث دالة حساب إجمالي مدة الخروج
**الملف:** `src/app/dashboard/attendance/page.tsx`

#### المنطق الجديد:
- **للحركات المتوسطة:** يتم حساب مدة الخروج بين الحركات كما هو (وقت الدخول التالي - وقت الخروج الحالي)
- **للحركة الأخيرة (إذا خرج ولم يعد):**
  - إذا كان هناك دخول في الحركة الأولى: `مدة الخروج = عدد ساعات العمل - (وقت الخروج - وقت الدخول)`
  - إذا لم يكن هناك دخول: يتم استخدام المنطق القديم (حتى نهاية العمل)

#### الكود المحدث:
```typescript
const calculateTotalBreakTime = (movements: Record<string, any>[], workEndTime: string = '14:30', workingHours: number = 7) => {
  // ... منطق حساب مدة الخروج بين الحركات

  // للحركة الأخيرة: إذا خرج الموظف ولم يعد
  const lastMovement = sortedMovements[sortedMovements.length - 1];
  if (lastMovement.checkOutTime) {
    const firstMovement = sortedMovements[0];
    
    // إذا كان هناك دخول في الحركة الأولى وخروج في الحركة الأخيرة ولم يعد
    if (firstMovement.checkInTime) {
      const checkIn = new Date(firstMovement.checkInTime);
      const checkOut = new Date(lastMovement.checkOutTime);
      
      // حساب الوقت الفعلي الذي قضاه الموظف في العمل
      const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      
      // إذا كان الوقت الفعلي أقل من عدد ساعات العمل المطلوبة
      if (actualWorkHours < workingHours) {
        // مدة الخروج = عدد ساعات العمل - الوقت الفعلي
        const breakTime = workingHours - actualWorkHours;
        if (breakTime > 0) {
          totalBreakTime += breakTime;
        }
      }
    } else {
      // إذا لم يكن هناك دخول، استخدم المنطق القديم
      // ... المنطق القديم
    }
  }
}
```

### 3. تحديث عرض الحركات
تم تحديث منطق عرض مدة الخروج لكل حركة فردية لتطبيق نفس القاعدة الجديدة للحركة الأخيرة.

### 4. تحديث تقرير الطباعة
تم تحديث دالة `calculateTotalBreakForRecord` في تقرير الطباعة لتطبيق نفس المنطق الجديد.

### 5. تحديث الإعدادات الافتراضية
```typescript
setSettings(data.settings || {
  minWorkingHours: 8,
  workStartTime: '08:00',
  workEndTime: '17:00',
  workingHours: 7 // القيمة الافتراضية الجديدة
});
```

## أمثلة على الحساب

### مثال 1: موظف دخل الساعة 8:00 وخرج الساعة 12:00 ولم يعد
- **عدد ساعات العمل المطلوبة:** 7 ساعات
- **الوقت الفعلي في العمل:** 4 ساعات (12:00 - 8:00)
- **مدة الخروج المحسوبة:** 3 ساعات (7 - 4)

### مثال 2: موظف دخل الساعة 8:00 وخرج الساعة 15:00 ولم يعد
- **عدد ساعات العمل المطلوبة:** 7 ساعات
- **الوقت الفعلي في العمل:** 7 ساعات (15:00 - 8:00)
- **مدة الخروج المحسوبة:** 0 ساعة (لأن الموظف أكمل ساعات العمل المطلوبة)

### مثال 3: موظف دخل الساعة 8:00 وخرج الساعة 18:00 ولم يعد
- **عدد ساعات العمل المطلوبة:** 7 ساعات
- **الوقت الفعلي في العمل:** 10 ساعات (18:00 - 8:00)
- **مدة الخروج المحسوبة:** 0 ساعة (لأن الموظف تجاوز ساعات العمل المطلوبة)

## الفوائد

### 1. حساب أكثر دقة
- يأخذ في الاعتبار الوقت الفعلي الذي قضاه الموظف في العمل
- يعكس مدة الخروج الحقيقية بناءً على ساعات العمل المطلوبة

### 2. مرونة في الإعدادات
- يمكن تعديل عدد ساعات العمل من صفحة الإعدادات
- يدعم القيم العشرية (مثل 7.5 ساعة)

### 3. التوافق مع الأنظمة المختلفة
- يدعم أنظمة العمل المختلفة (7 ساعات، 8 ساعات، إلخ)
- يحافظ على المنطق القديم للحالات الاستثنائية

## الملفات المتأثرة

1. **src/app/dashboard/attendance/page.tsx**
   - تحديث interface AttendanceSettings
   - تحديث دالة calculateTotalBreakTime
   - تحديث منطق عرض الحركات
   - تحديث دالة calculateTotalBreakForRecord في تقرير الطباعة

2. **src/app/api/settings/route.ts**
   - دعم حقل workingHours (تم إضافته مسبقاً)

3. **prisma/schema.prisma**
   - إضافة حقل workingHours (تم إضافته مسبقاً)

## التوافق مع النسخة السابقة
- التحديث متوافق مع النسخة السابقة
- إذا لم يتم تعيين workingHours، يتم استخدام القيمة الافتراضية 7
- المنطق القديم يُستخدم للحالات التي لا يوجد فيها دخول

## الاختبار
تم اختبار التحديث مع:
- ✅ البناء الناجح للتطبيق
- ✅ عدم وجود أخطاء في TypeScript
- ✅ التوافق مع قاعدة البيانات الحالية
- ✅ عمل تقرير الطباعة بشكل صحيح

## التاريخ
- **تاريخ التحديث:** ديسمبر 2024
- **المطور:** نظام إدارة الحضور
- **النسخة:** 1.1.0 