# تصفية كشف الحضور في بوابة الأمن لاستبعاد الموظفين في الإجازات المعتمدة

## نظرة عامة
تم تطوير نظام تصفية متقدم لكشف الحضور في بوابة الأمن لاستبعاد الموظفين الذين في إجازة معتمدة من قائمة الموظفين المعروضة.

## المشكلة
كان الموظفون في الإجازات المعتمدة يظهرون في كشف الحضور في بوابة الأمن، مما يسبب التباس ومحاولات غير ضرورية لتسجيل حضورهم.

## الحل المطبق

### 1. إنشاء API Endpoint جديد
**الملف**: `src/app/api/security/employees-available/route.ts`

```typescript
// جلب الموظفين المتاحين للحضور (باستثناء الذين في إجازة معتمدة)
export async function GET() {
  const employees = await prisma.user.findMany({
    where: {
      role: {
        not: "ADMIN" // استثناء المدراء من كشف الحضور
      },
      leaveRequests: {
        none: {
          status: "APPROVED",
          startDate: {
            lte: tomorrow
          },
          endDate: {
            gte: today
          }
        }
      }
    },
    // ... باقي الإعدادات
  });
}
```

### 2. تحديث صفحة كشف الحضور
**الملف**: `src/app/dashboard/security/employee-attendance/page.tsx`

#### التغييرات المطبقة:
- تغيير استدعاء API من `/api/employees` إلى `/api/security/employees-available`
- إضافة ملاحظة توضيحية في الواجهة

```typescript
// جلب الموظفين المتاحين (باستثناء الذين في إجازة معتمدة)
const employeesRes = await fetch("/api/security/employees-available");
```

### 3. منطق التصفية

#### معايير الاستبعاد:
1. **الإجازات المعتمدة**: الموظفون الذين لديهم إجازة معتمدة (APPROVED) تتداخل مع تاريخ اليوم
2. **المدراء**: استبعاد المدراء (ADMIN) من كشف الحضور

#### التحقق من التواريخ:
```sql
WHERE leaveRequests.none({
  status: "APPROVED",
  startDate: { lte: tomorrow },
  endDate: { gte: today }
})
```

## الميزات الجديدة

### 1. تصفية ذكية
- استبعاد تلقائي للموظفين في الإجازة المعتمدة
- دعم الإجازات متعددة الأيام
- فحص تلقائي يومي

### 2. واجهة محدثة
- ملاحظة توضيحية للمستخدم
- عرض أوضح للموظفين المتاحين فقط

### 3. أمان محسن
- التحقق من صلاحيات الأمن
- حماية البيانات الحساسة

## الفوائد

### 1. تحسين تجربة المستخدم
- عدم عرض موظفين غير متوقع حضورهم
- تقليل الأخطاء في التسجيل
- واجهة أوضح وأكثر دقة

### 2. دقة البيانات
- منع محاولات تسجيل حضور للموظفين في إجازة
- ضمان صحة سجلات الحضور
- تحسين التقارير

### 3. كفاءة العملية
- توفير وقت موظف الأمن
- تقليل الالتباس
- تحسين سير العمل

## التأثير على النظام

### 1. قاعدة البيانات
- استعلامات محسنة مع تصفية الإجازات
- فهرسة محسنة لجدول LeaveRequest

### 2. الأداء
- استعلام واحد مجمع بدلاً من متعدد
- تحسين سرعة تحميل الصفحة

### 3. التوافق
- متوافق مع النظام الحالي
- لا يؤثر على وظائف أخرى

## الاختبار والتحقق

### 1. سيناريوهات الاختبار
- موظف في إجازة معتمدة ليوم واحد
- موظف في إجازة معتمدة لعدة أيام
- موظف بدون إجازات
- إجازة معتمدة تبدأ أو تنتهي في نفس اليوم

### 2. التحقق من النتائج
- عدم ظهور الموظفين في الإجازة المعتمدة
- ظهور الموظفين المتاحين فقط
- دقة التواريخ والحالات

## إعدادات الـ Timezone
- دعم المنطقة الزمنية `Asia/Muscat`
- تنسيق التاريخ `dd/mm/yyyy`
- دعم RTL للعربية

## متطلبات النظام

### قاعدة البيانات
- جدول LeaveRequest مع الفهارس المناسبة
- علاقات صحيحة بين User و LeaveRequest

### الصلاحيات
- صلاحية SECURITY للوصول للـ endpoint
- فحص الجلسة والمصادقة

## الصيانة والمراقبة

### 1. المراقبة المطلوبة
- مراقبة أداء الاستعلامات
- تتبع استخدام الـ endpoint الجديد
- مراقبة أخطاء التصفية

### 2. الصيانة الدورية
- تحديث الفهارس حسب الحاجة
- مراجعة منطق التصفية
- تحسين الأداء المستمر

## التطوير المستقبلي

### 1. تحسينات محتملة
- إضافة تصفية للإجازات المرضية
- دعم أنواع إجازات إضافية
- تقارير تفصيلية للموظفين المستبعدين

### 2. ميزات إضافية
- إشعارات للموظفين في الإجازة
- تكامل مع نظام الإشعارات
- تقارير إحصائية للإجازات

---
**تاريخ التنفيذ**: ديسمبر 2024  
**الحالة**: مُنفَّذ ✅  
**النسخة**: 1.0 