# منطق حساب مدة الخروج

## نظرة عامة

يحسب النظام مدة خروج الموظفين من العمل بناءً على منطق محدد يأخذ في الاعتبار الحركات المتعددة للموظف خلال اليوم.

## قواعد الحساب

### 1. الحركات الوسطية
- **إذا كانت هناك حركة تالية**: لا تحسب مدة الخروج للحركة الحالية
- **إذا لم تكن هناك حركة تالية**: تحسب المدة من وقت الخروج حتى نهاية الدوام الرسمي

### 2. حساب المدة بين الحركات
- تحسب المدة من وقت الخروج في الحركة الأولى إلى وقت الدخول في الحركة التالية
- مثال: 
  - الحركة 1: من 8:00-9:00
  - الحركة 2: من 11:00-12:00
  - **مدة الخروج**: 2 ساعة (من 9:00 إلى 11:00)

### 3. الحركة الأخيرة
- إذا خرج الموظف ولم يعد: تحسب المدة من وقت الخروج حتى نهاية الدوام الرسمي
- مثال:
  - الحركة الأخيرة: من 1:00-لم يعد
  - نهاية الدوام: 2:30
  - **مدة الخروج**: 1.5 ساعة

## أمثلة عملية

### مثال 1: حركات متعددة مع عودة
```
الحركة 1: 8:00 - 9:00  → لا تحسب مدة خروج (هناك حركة تالية)
الحركة 2: 11:00 - 12:00  → مدة خروج = 2 ساعة (9:00 إلى 11:00)
الحركة 3: 1:00 - لم يعد  → مدة خروج = 1.5 ساعة (1:00 إلى 2:30)

إجمالي مدة الخروج = 2 + 1.5 = 3.5 ساعة
```

### مثال 2: حركة واحدة مع خروج مبكر
```
الحركة 1: 8:00 - 11:00  → مدة خروج = 3.5 ساعة (11:00 إلى 2:30)

إجمالي مدة الخروج = 3.5 ساعة
```

### مثال 3: حركة واحدة كاملة
```
الحركة 1: 8:00 - 2:30  → لا توجد مدة خروج

إجمالي مدة الخروج = 0 ساعة
```

## التنفيذ في الكود

### الملفات المتأثرة
- `src/app/api/attendance/report/route.ts`
- `src/app/api/attendance/range-report/route.ts`
- `src/app/dashboard/attendance/page.tsx`

### الدالة الأساسية
```javascript
function calculateBreakTime(movements, settings) {
  // ترتيب الحركات حسب entryNumber
  const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);
  let totalBreakTime = 0;

  // حساب مدة الخروج بين الحركات
  for (let i = 0; i < sortedMovements.length - 1; i++) {
    const current = sortedMovements[i];
    const next = sortedMovements[i + 1];

    if (current.checkOutTime && next.checkInTime) {
      const breakDuration = (nextCheckIn - checkOut) / (1000 * 60 * 60);
      if (breakDuration > 0) totalBreakTime += breakDuration;
    }
  }

  // للحركة الأخيرة
  const lastMovement = sortedMovements[sortedMovements.length - 1];
  if (lastMovement.checkOutTime) {
    // حساب المدة حتى نهاية العمل
    if (checkOut < workEndTime) {
      totalBreakTime += (workEndTime - checkOut) / (1000 * 60 * 60);
    }
  }

  return totalBreakTime;
}
```

## اعتبارات مهمة

1. **ترتيب الحركات**: يجب ترتيب الحركات حسب `entryNumber` قبل الحساب
2. **التحقق من صحة البيانات**: التأكد من وجود أوقات الدخول والخروج
3. **وقت انتهاء العمل**: يؤخذ من إعدادات النظام (`workEndTime`)
4. **عدم الحساب السلبي**: لا تحسب مدة خروج إذا كان الخروج بعد انتهاء العمل

## التحديثات الأخيرة

- **تاريخ التحديث**: 24/12/2024
- **نوع التحديث**: تصحيح منطق الحساب وعرض مدة الخروج
- **التغييرات الرئيسية**:
  - عدم حساب مدة الخروج للحركات الوسطية إذا كانت هناك حركة تالية ✅
  - حساب المدة فقط بين الحركات أو للحركة الأخيرة إذا لم يعد الموظف ✅
  - تحسين ترتيب الحركات والتحقق من صحة البيانات ✅
  - **إصلاح جديد**: عرض مدة الخروج لجميع الحركات التي لها خروج في الواجهة
    - للحركات الوسطية: يظهر "حتى العودة" مع مدة الخروج للحركة التالية
    - للحركة الأخيرة: يظهر "حتى نهاية العمل" إذا لم يعد الموظف

## عرض مدة الخروج في الواجهة

### للحركات الوسطية
- **مثال**: الحركة #1: 8:00 ص - 9:00 ص (2.0س حتى العودة)
- يظهر المدة من الخروج حتى الدخول في الحركة التالية

### للحركة الأخيرة
- **مثال**: الحركة #3: 1:00 م - 1:30 م (1.0س حتى نهاية العمل)
- يظهر المدة من الخروج حتى انتهاء الدوام الرسمي إذا لم يعد 