# OCA Dashboard Foundation - Refactoring Plan

## Executive Summary
This document outlines the refactoring strategy to transform the AttendPro application into a reusable dashboard foundation for the OCA application. The goal is to preserve core infrastructure while removing domain-specific features.

---

## 🎯 Refactoring Categories

### ✅ KEEP - Core Foundation Components

#### 1. **Authentication & Security**
```
src/lib/auth.ts                              # NextAuth configuration
src/providers/auth-provider.tsx              # Session management provider
src/app/api/auth/[...nextauth]/route.ts     # Auth API routes
src/app/api/auth/session-check/route.ts     # Session validation
src/app/api/auth/logout/route.ts            # Logout endpoint
src/app/login/page.tsx                      # Login page (simplify)
src/types/next-auth.d.ts                    # Auth type definitions
middleware.ts                                # Route protection
```

#### 2. **User Management (Core)**
```
src/app/api/users/route.ts                  # User CRUD API
src/app/api/users/[id]/route.ts            # User detail API
src/app/api/users/[id]/reset-password/route.ts  # Password reset
src/app/dashboard/users/page.tsx           # User list page
src/app/dashboard/users/[id]/page.tsx      # User detail page
src/app/dashboard/users/[id]/edit/page.tsx # User edit page
src/app/dashboard/users/new/page.tsx       # Create user page
src/app/dashboard/users/roles/page.tsx     # Role management
```

#### 3. **Layout & UI Framework**
```
src/app/layout.tsx                          # Root layout
src/app/dashboard/layout.tsx               # Dashboard layout
src/components/dashboard/Header.tsx        # Dashboard header
src/components/dashboard/Sidebar.tsx       # Navigation sidebar (modify menu)
src/components/Logo.tsx                    # Logo component
src/components/DynamicFavicon.tsx         # Dynamic favicon
src/components/ClientBodyClass.tsx        # Body class manager
src/components/LoadingSpinner.tsx         # Loading indicator
src/components/ErrorBoundary.tsx          # Error boundary
src/components/ui/DataTable.tsx           # Reusable data table
```

#### 4. **Settings System**
```
src/app/dashboard/settings/page.tsx       # Settings page (simplify)
src/app/api/settings/route.ts            # Settings API
src/app/api/settings/public/route.ts     # Public settings
src/providers/settings-provider.tsx      # Settings context
src/hooks/useWorkingHours.ts            # Working hours hook (generalize)
```

#### 5. **Database & ORM**
```
src/lib/prisma.ts                        # Prisma client
prisma/schema.prisma                     # Database schema (modify)
```

#### 6. **Core Utilities**
```
src/lib/dateUtils.ts                     # Date utilities
src/lib/utils.ts                        # General utilities
src/providers/theme-provider.tsx        # Theme management
```

#### 7. **Error & Loading States**
```
src/app/error.tsx                       # Error page
src/app/global-error.tsx               # Global error
src/app/not-found.tsx                  # 404 page
src/app/loading.tsx                    # Loading page
src/app/dashboard/error.tsx            # Dashboard error
```

#### 8. **Configuration Files**
```
package.json                            # Dependencies
tsconfig.json                          # TypeScript config
next.config.mjs                        # Next.js config
tailwind.config.ts                     # Tailwind CSS
postcss.config.mjs                     # PostCSS config
.env.example                           # Environment variables
```

#### 9. **Upload & File Management**
```
src/app/api/upload/route.ts           # File upload API
public/uploads/                       # Upload directory
```

#### 10. **WhatsApp Integration**
```
src/app/api/whatsapp/                # WhatsApp APIs
src/lib/services/whatsappService.ts  # WhatsApp service
src/app/api/auth/otp/                # OTP authentication
```

---

### ❌ REMOVE - Application-Specific Components

#### 1. **Attendance Features**
```
src/app/dashboard/attendance/              # All attendance pages
src/app/api/attendance/                   # All attendance APIs
src/modules/attendance/                   # Attendance module
```

#### 2. **Leave Management**
```
src/app/dashboard/leaves/                 # Leave pages
src/app/api/leaves/                      # Leave APIs
src/modules/leaves/                      # Leave module
```

#### 3. **Visitor Management**
```
src/app/dashboard/visitors/              # Visitor pages
src/app/api/visitors/                   # Visitor APIs
src/modules/visitors/                   # Visitor module
```

#### 4. **After-Hours Permits**
```
src/app/dashboard/after-hours/          # After-hours pages
src/app/api/after-hours/               # After-hours APIs
```

#### 5. **Security Operations**
```
src/app/dashboard/security/            # Security pages
src/app/api/security/                  # Security APIs
```

#### 6. **Department Management**
```
src/app/dashboard/departments/         # Department pages
src/app/api/departments/              # Department APIs
```

#### 7. **Employee Management (HR-specific)**
```
src/app/dashboard/employees/          # Employee pages
src/app/api/employees/                # Employee APIs
```

#### 8. **Official Holidays**
```
src/app/dashboard/official-holidays/  # Holiday pages
src/app/api/official-holidays/       # Holiday APIs
```

#### 9. **WhatsApp Integration**
```
src/app/api/whatsapp/                # WhatsApp APIs
src/lib/services/whatsappService.ts  # WhatsApp service
src/app/api/auth/otp/                # OTP authentication
```

#### 10. **Domain-Specific Components**
```
src/components/dashboard/AttendanceChart.tsx
src/components/dashboard/RecentActivity.tsx
src/components/dashboard/QuickActions.tsx
src/components/dashboard/SystemStatus.tsx
src/components/reports/EmployeesPrintReport.tsx
```

#### 11. **Manager/Employee Dashboards**
```
src/app/dashboard/manager/           # Manager dashboard
src/app/dashboard/employee/         # Employee dashboard
src/app/api/dashboard/manager-stats/
src/app/api/dashboard/employee-stats/
```

---

## 📝 MODIFY - Files Requiring Changes

### 1. **Database Schema** (`prisma/schema.prisma`)
**Keep:**
- User model (basic fields)
- Settings model (core settings only)
- Role enum (simplify to ADMIN, USER)

**Remove:**
- Department, AttendanceRecord, LeaveRequest, VisitorRequest
- AfterHoursPermit, OfficialHoliday, OTP models
- All HR-specific fields

**New Schema:**
```prisma
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String
  password    String
  role        Role     @default(USER)
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

model Settings {
  id            String   @id @default(cuid())
  appName       String   @default("OCA Dashboard")
  logo          String?
  favicon       String?
  primaryColor  String   @default("#1e40af")
  secondaryColor String  @default("#3730A3")
  accentColor   String   @default("#10b981")
  timezone      String   @default("Asia/Muscat")
  language      String   @default("ar")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}
```

### 2. **Authentication** (`src/lib/auth.ts`)
- Remove OTP strategy
- Implement simple email/password authentication
- Keep session management
- Simplify role checks

### 3. **Dashboard Page** (`src/app/dashboard/page.tsx`)
- Remove AttendPro-specific statistics
- Add placeholder welcome content
- Keep layout structure
- Add slots for future widgets

### 4. **Sidebar Navigation** (`src/components/dashboard/Sidebar.tsx`)
**New Menu Structure:**
```typescript
const menuItems = [
  { icon: Home, label: 'الرئيسية', href: '/dashboard' },
  { icon: Users, label: 'المستخدمين', href: '/dashboard/users', role: 'ADMIN' },
  { icon: Settings, label: 'الإعدادات', href: '/dashboard/settings', role: 'ADMIN' },
];
```

### 5. **Settings Page** (`src/app/dashboard/settings/page.tsx`)
**Keep:**
- App name, logo, favicon
- Theme colors
- Language and timezone

**Remove:**
- Working hours settings
- WhatsApp configuration
- Message templates
- HR-specific settings

### 6. **Environment Variables** (`.env.example`)
```env
# Database
DATABASE_URL="mysql://user:password@localhost:3306/oca"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Application
APP_NAME="OCA Dashboard"
APP_URL="http://localhost:3000"
```

---

## 🚀 Implementation Strategy

### Phase 1: Backup & Setup (Day 1)
1. Create full backup of current codebase
2. Create new branch `refactor/oca-foundation`
3. Set up new database `oca_dev`

### Phase 2: Remove Domain-Specific Code (Day 2-3)
1. Delete all files in REMOVE category
2. Remove related imports and references
3. Clean up package.json dependencies
4. Update TypeScript paths

### Phase 3: Modify Core Components (Day 4-5)
1. Simplify database schema
2. Update authentication system
3. Modify dashboard and sidebar
4. Simplify settings system
5. Update user management

### Phase 4: Testing & Cleanup (Day 6)
1. Test authentication flow
2. Verify user management
3. Test settings functionality
4. Remove unused code and assets
5. Update documentation

### Phase 5: Finalization (Day 7)
1. Create new README.md
2. Update environment variables
3. Final testing
4. Create deployment guide

---

## 📦 Final Structure

```
oca-dashboard/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── auth/         # Authentication APIs
│   │   │   ├── users/        # User management APIs
│   │   │   ├── settings/     # Settings APIs
│   │   │   └── upload/       # File upload API
│   │   ├── dashboard/
│   │   │   ├── users/        # User management pages
│   │   │   ├── settings/     # Settings page
│   │   │   ├── layout.tsx    # Dashboard layout
│   │   │   └── page.tsx      # Dashboard home
│   │   ├── login/            # Login page
│   │   └── layout.tsx        # Root layout
│   ├── components/
│   │   ├── dashboard/        # Dashboard components
│   │   └── ui/              # UI components
│   ├── lib/                 # Utilities
│   ├── providers/           # Context providers
│   └── types/              # TypeScript types
├── prisma/
│   └── schema.prisma       # Simplified schema
├── public/                 # Static assets
└── docs/                   # Documentation
```

---

## ✅ Success Criteria

1. **Clean Foundation**: No AttendPro-specific code remains
2. **Working Auth**: Email/password authentication functional
3. **User Management**: CRUD operations for users
4. **Settings System**: Basic app configuration
5. **Responsive UI**: RTL-ready dashboard shell
6. **Documentation**: Clear setup and usage guides
7. **Type Safety**: Full TypeScript coverage
8. **No Broken Links**: All routes functional
9. **Clean Dependencies**: No unused packages
10. **Ready for Extension**: Easy to add new features

---

## 🎯 Expected Deliverables

1. **Core Dashboard Foundation**
   - Authentication system
   - User management
   - Settings management
   - Dashboard shell with navigation

2. **Documentation**
   - Setup guide
   - API documentation
   - Extension guide
   - Migration notes

3. **Development Tools**
   - TypeScript definitions
   - Lint configuration
   - Build scripts
   - Development seeds

---

## 📊 Risk Mitigation

| Risk | Mitigation Strategy |
|------|-------------------|
| Breaking core functionality | Comprehensive testing at each phase |
| Missing dependencies | Careful tracking of removed imports |
| Database migration issues | Backup and separate dev database |
| Authentication problems | Test auth flow early and often |
| UI/UX degradation | Preserve layout components intact |

---

## 🔄 Post-Refactoring Steps

1. **Validation**
   - Run full test suite
   - Manual testing of all features
   - Performance benchmarking

2. **Documentation**
   - Update all documentation
   - Create migration guide
   - Document new structure

3. **Preparation for OCA**
   - Create feature branches
   - Set up CI/CD pipeline
   - Prepare deployment scripts

---

*This plan ensures a clean, maintainable foundation ready for OCA-specific features while preserving all valuable infrastructure from the original application.*