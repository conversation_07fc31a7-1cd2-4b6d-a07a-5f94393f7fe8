# إصلاح مشكلة طباعة التقرير في صفحة الحضور

## نظرة عامة
تم إصلاح مشكلة الصفحة الفارغة (`about:blank`) عند طباعة تقرير الحضور والانصراف في `/dashboard/attendance`.

## المشكلة المحددة

### الخطأ
- عند الضغط على زر "طباعة التقرير" تفتح نافذة جديدة فارغة (`about:blank`)
- لا يظهر أي محتوى في نافذة الطباعة
- التقرير لا يتم إنشاؤه أو طباعته

### السبب الجذري
كانت المشكلة في دالة `handlePrintReport` بسبب:

1. **Template Literals معقدة ومتداخلة**: 
   - استخدام template literals طويلة ومعقدة مع دوال JavaScript متداخلة
   - مشاكل في escape characters والاقتباسات
   - أخطاء في بناء HTML بسبب التعقيد

2. **معالجة أخطاء غير كافية**:
   - عدم وجود try-catch للتعامل مع الأخطاء
   - عدم التحقق من وجود البيانات قبل الطباعة
   - عدم التحقق من نجاح فتح النافذة الجديدة

3. **بناء HTML غير موثوق**:
   - استخدام template literals معقدة لبناء HTML طويل
   - مشاكل في تنسيق البيانات والتواريخ
   - عدم معالجة القيم الفارغة أو undefined

## الحل المطبق

### إعادة كتابة دالة handlePrintReport
**الملف**: `src/app/dashboard/attendance/page.tsx`

```typescript
const handlePrintReport = () => {
  try {
    // التحقق من وجود بيانات للطباعة
    if (!attendanceData || attendanceData.length === 0) {
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // فتح نافذة جديدة للطباعة مع تحديد الأبعاد
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) {
      alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
      return;
    }

    // دوال مساعدة لتنسيق البيانات
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
      const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
      return weekdays[date.getDay()] + ' ' + date.getDate() + ' ' + months[date.getMonth()] + ' ' + date.getFullYear() + 'م';
    };

    const formatTime = (timeString: string) => {
      try {
        return new Date(timeString).toLocaleTimeString('ar-SA', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
      } catch {
        return '-';
      }
    };

    // بناء HTML بطريقة تدريجية وآمنة
    let htmlContent = '<!DOCTYPE html>';
    htmlContent += '<html dir="rtl" lang="ar">';
    // ... باقي المحتوى

    // كتابة المحتوى بطريقة آمنة
    printWindow.document.open();
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
    }, 1000);

  } catch (error) {
    console.error('خطأ في إنشاء تقرير الطباعة:', error);
    alert('حدث خطأ في إنشاء تقرير الطباعة: ' + (error as Error).message);
  }
};
```

## التحسينات المطبقة

### 1. معالجة أخطاء شاملة
- إضافة `try-catch` للتعامل مع جميع الأخطاء المحتملة
- التحقق من وجود البيانات قبل الطباعة
- التحقق من نجاح فتح النافذة الجديدة
- رسائل خطأ واضحة للمستخدم

### 2. بناء HTML مبسط وموثوق
- استبدال template literals المعقدة ببناء تدريجي للـ HTML
- استخدام string concatenation بدلاً من template literals
- تجنب الدوال المتداخلة داخل HTML
- معالجة آمنة للقيم الفارغة

### 3. دوال مساعدة منفصلة
- فصل منطق تنسيق التاريخ والوقت
- دوال مساعدة لمعالجة البيانات
- كود أكثر قابلية للقراءة والصيانة

### 4. تحسين عملية الطباعة
- تحديد أبعاد النافذة الجديدة
- انتظار تحميل المحتوى قبل الطباعة
- تحسين timing للطباعة

## الميزات الجديدة

### 1. التحقق من البيانات
```typescript
if (!attendanceData || attendanceData.length === 0) {
  alert('لا توجد بيانات للطباعة');
  return;
}
```

### 2. معالجة النوافذ المحجوبة
```typescript
const printWindow = window.open('', '_blank', 'width=800,height=600');
if (!printWindow) {
  alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
  return;
}
```

### 3. تنسيق آمن للبيانات
```typescript
const formatTime = (timeString: string) => {
  try {
    return new Date(timeString).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } catch {
    return '-';
  }
};
```

### 4. بناء HTML تدريجي
```typescript
let htmlContent = '<!DOCTYPE html>';
htmlContent += '<html dir="rtl" lang="ar">';
htmlContent += '<head>';
// ... إضافة المحتوى تدريجياً
```

## محتوى التقرير المطبوع

### العناصر المتضمنة
1. **رأس التقرير**:
   - عنوان التقرير
   - فترة التقرير (من - إلى)
   - تاريخ ووقت الطباعة

2. **ملخص الحضور**:
   - عدد الحاضرين
   - عدد الغائبين
   - عدد الحضور الجزئي
   - عدد الموظفين في إجازة

3. **تفاصيل يومية**:
   - تجميع البيانات حسب التاريخ
   - معلومات كل موظف (الاسم، الرقم، القسم)
   - حالة الحضور
   - تفاصيل الحركات (دخول/خروج)
   - ساعات العمل

### تنسيق الطباعة
- دعم RTL للعربية
- خط Arial مناسب للطباعة
- جداول منظمة مع حدود
- ألوان مناسبة للطباعة
- تقسيم الصفحات المناسب

## الاختبار والتحقق

### سيناريوهات الاختبار
1. ✅ طباعة تقرير مع بيانات موجودة
2. ✅ محاولة طباعة بدون بيانات
3. ✅ طباعة مع فلاتر مختلفة
4. ✅ طباعة مع بيانات كبيرة
5. ✅ التعامل مع النوافذ المحجوبة

### النتائج
- ✅ تفتح نافذة الطباعة بنجاح
- ✅ يظهر المحتوى كاملاً ومنسقاً
- ✅ تعمل الطباعة بدون أخطاء
- ✅ رسائل خطأ واضحة عند المشاكل
- ✅ تنسيق مناسب للطباعة

## أفضل الممارسات المطبقة

### 1. معالجة الأخطاء
- استخدام try-catch شامل
- التحقق من الشروط المسبقة
- رسائل خطأ واضحة للمستخدم

### 2. بناء HTML
- تجنب template literals المعقدة
- بناء تدريجي للمحتوى
- معالجة آمنة للبيانات

### 3. تجربة المستخدم
- رسائل تأكيد واضحة
- معالجة النوافذ المحجوبة
- تنسيق مناسب للطباعة

### 4. الأداء
- تحميل البيانات مرة واحدة
- بناء HTML فعال
- timing مناسب للطباعة

## الصيانة المستقبلية

### نقاط المراقبة
- مراقبة أخطاء الطباعة في Console
- تجربة الطباعة مع متصفحات مختلفة
- اختبار مع أحجام بيانات مختلفة

### تحسينات محتملة
- إضافة خيارات تخصيص التقرير
- دعم تصدير PDF
- إضافة معاينة قبل الطباعة
- تحسين تنسيق الطباعة

## إعدادات النظام

### المتطلبات
- React 18+
- Next.js 14+
- TypeScript
- دعم window.open في المتصفح

### التوافق
- دعم RTL للعربية
- خط Tajawal في الواجهة
- تنسيق التاريخ العربي
- المنطقة الزمنية: Asia/Muscat

## التحديث الأخير - 25 مايو 2025

### تحسين عرض الحركات في التقرير المطبوع
تم تحسين دالة الطباعة لتظهر تفاصيل أكثر شمولية للحركات مثل ما هو موجود في العرض الأساسي:

#### الميزات المضافة:
1. **عرض نوع الاستئذان** لكل حركة خروج
2. **حساب وعرض مدة الخروج** لكل حركة
3. **إضافة عمود "إجمالي الخروج"** في الجدول
4. **تنسيق محسن للحركات** مع ألوان مميزة

#### التفاصيل التقنية:
```typescript
// عرض الحركات مع التفاصيل الكاملة
movementsText = sortedMovements.map((movement: any, index: number) => {
  const checkIn = movement.checkInTime ? formatTime(movement.checkInTime) : '-';
  const checkOut = movement.checkOutTime ? formatTime(movement.checkOutTime) : 'مفتوح';
  
  // نوع الاستئذان
  const exitTypeLabel = movement.exitType ? exitTypeText[movement.exitType] : '';
  
  // حساب مدة الخروج
  let breakDuration = '';
  if (movement.checkOutTime) {
    // منطق حساب مدة الخروج...
  }
  
  // تنسيق HTML مع الألوان والتفاصيل
  let movementText = '<div style="margin: 2px 0; padding: 2px 4px; background-color: #f9f9f9;">';
  movementText += '#' + movement.entryNumber + ': ' + checkIn + ' - ' + checkOut;
  
  if (breakDuration) {
    movementText += '<span style="color: #ff9800; font-weight: bold;">' + breakDuration + '</span>';
  }
  
  if (exitTypeLabel) {
    movementText += ' <span style="background-color: #e3f2fd; color: #1976d2;">(' + exitTypeLabel + ')</span>';
  }
  
  return movementText + '</div>';
}).join('');
```

#### محتوى التقرير المحدث:
- **رقم الحركة**: #1, #2, إلخ
- **أوقات الدخول والخروج**: بالتنسيق العربي
- **مدة الخروج**: بالساعات مع توضيح (حتى العودة/حتى نهاية العمل)
- **نوع الاستئذان**: رسمي، شخصي، عمل، صحي
- **إجمالي مدة الخروج**: مجموع جميع فترات الخروج

---

# إصلاح مشكلة طباعة التقرير في صفحة الحضور

---
**تاريخ التنفيذ**: 25 مايو 2025  
**الحالة**: مُنفَّذ ✅  
**النسخة**: 1.0 