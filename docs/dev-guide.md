# دليل التطوير - نظام AttendPro

## فهرس الوثائق

### إدارة المستخدمين
- [إدارة كلمات المرور](./password-management.md) - ميزة تعديل كلمات المرور للمستخدمين والموظفين
- [إدارة الأقسام ورؤساء الأقسام](./departments-management.md) - حل مشكلة عدم ظهور رؤساء الأقسام وتحسين واجهة إدارة الأقسام
- [إضافة عمود المدير المباشر في قائمة الموظفين](./employees-manager-column-addition.md) - تحسين عرض الموظفين لإظهار المدير المباشر لكل موظف

### نظام المصادقة والأمان
- [نظام المصادقة المحسن](./enhanced-authentication-system.md) - إدارة الجلسات الآمنة مع انتهاء صلاحية تلقائي وحماية شاملة

### نظام الحضور والانصراف
- [مزامنة ساعات العمل المرنة في سجلات الحضور](./attendance-working-hours-sync.md) - نظام مرن لحساب وقت انتهاء العمل بناءً على وقت الدخول الفعلي + ساعات العمل من الإعدادات (النسخة 2.0)
- [تحديث منطق حساب الحضور في الداشبورد](./attendance-calculation-update.md) - توحيد منطق حساب الحضور ليعتمد بالكامل على بيانات نموذج الحضور والانصراف
- [تحسين تجميع بيانات الحضور حسب الموظف](./attendance-grouping-enhancement.md) - تجميع البيانات حسب اسم الموظف عند الفلترة بموظف محدد
- [تصفية كشف الحضور لاستبعاد الإجازات المعتمدة](./security-attendance-filter-approved-leaves.md) - تحسين كشف الحضور في بوابة الأمن لاستبعاد الموظفين في الإجازات المعتمدة

### نظام الإعدادات والتكوين
- [نظام الإعدادات المركزي](./centralized-settings-system.md) - استبدال localStorage و sessionStorage بنظام إعدادات مركزي آمن ومحسن للأداء
- [إصلاح نظام الإعدادات لضمان التخزين في قاعدة البيانات](./settings-database-fix.md) - حل مشكلة عدم ظهور البيانات المحفوظة وضمان التخزين في قاعدة البيانات فقط

### الأداء والتحسينات
- [الخطوط المحلية](./local-fonts.md) - تحويل خط Tajawal إلى نسخة محلية لتحسين الأداء
- [توحيد تنسيق التاريخ](./date-format-standardization.md) - تحويل من التاريخ الهجري إلى الميلادي بالصيغة العربية
- [تطبيق نظام الألوان الثابت](./static-theme-implementation.md) - إزالة الألوان الديناميكية وتطبيق نظام ألوان ثابت لتحسين الأداء
- [إصلاح أخطاء Chunk Loading](./chunk-loading-error-fix.md) - حل مشاكل ChunkLoadError وتحسين استقرار webpack
- [إصلاح خطأ تحميل وحدات Webpack](./webpack-module-loading-fix.md) - حل خطأ "Cannot read properties of undefined (reading 'call')" وتحسين استقرار النظام
- [إزالة العطل والإجازات من كشف الحضور](./remove-holidays-leaves-attendance.md) - تبسيط كشف الحضور والتركيز على أيام العمل الفعلية

### البيانات والتطوير
- [البيانات التجريبية](./sample-data-seeding.md) - دليل شامل لبذر البيانات التجريبية لجميع وحدات النظام
- [إصلاح مزامنة ساعات العمل من الإعدادات](./exit-duration-settings-sync-fix.md) - حل مشكلة عدم مزامنة ساعات العمل من الإعدادات في حساب مدة الخروج

### إصلاح المشاكل
- [تحديث صلاحيات الوصول إلى سجل الحضور والانصراف](./attendance-permissions-update.md) - تحديث الصلاحيات للسماح للموظفين والمدراء بمشاهدة السجلات مع تقييد التعديل للإدارة والموارد البشرية

### إصلاح مشكلة طباعة التقرير في صفحة الحضور
- [إصلاح خطأ "Cannot access before initialization" في صفحة الحضور](./attendance-function-hoisting-fix.md) - حل مشكلة ترتيب الدوال وJavaScript Hoisting
- [إصلاح قائمة المدير المباشر في نموذج الموظفين](./employee-manager-dropdown-fix.md) - حل مشكلة عدم ظهور قائمة المدير المباشر في نموذج تعديل الموظفين
- [إصلاح صفحة الحضور البيضاء ومشكلة الـ Favicon](./attendance-page-favicon-fixes.md) - حل مشكلة الصفحة البيضاء في كشف الحضور وإصلاح عدم ظهور الـ favicon
- [إصلاح شامل لعرض الـ Favicon](./favicon-implementation-fix.md) - حل مشكلة ظهور واختفاء الفافيكون وضمان عرضه بشكل مستقر
- [إصلاح مشكلة OTP Login Loop](./otp-login-fix.md) - حل مشكلة التكرار اللانهائي في تسجيل الدخول بـ OTP
- [إصلاح خطأ التحقق من OTP](./otp-validation-fix.md) - حل مشكلة validation error في رقم الهاتف
- [إصلاح مشكلة الأداء في OTP](./otp-performance-fix.md) - حل مشكلة البطء الشديد في تسجيل الدخول
- [إصلاح نموذج إضافة الزوار](./visitors-form-fix.md) - حل مشكلة الصفحة البيضاء وعدم التنقل بعد إضافة زائر
- [إصلاح صفحة security/after-hours](./security-after-hours-fix.md) - حل مشكلة الصفحة البيضاء عند وجود تصاريح
- [إزالة قيود الوقت في العمل بعد الدوام](./after-hours-time-restriction-removal.md) - السماح بالتسجيل في أي وقت
- [إصلاح مشكلة بطء تسجيل الخروج](./logout-performance-fix.md) - تحسين سرعة وموثوقية تسجيل الخروج
- [إصلاح مشكلة بطء التحميل الأولي](./initial-loading-performance-fix.md) - تحسين سرعة التحميل عند تغيير البورت والتشغيل الأولي
- [إصلاح Next.js 15 Route Parameters](./fixes/nextjs-15-route-params-fix.md) - حل مشاكل TypeScript في معاملات المسارات
- [إصلاح عرض الإجازات والعطل الرسمية](./holidays-leaves-attendance-fix.md) - حل مشكلة ظهور العطل الرسمية وإضافة عرض الإجازات المعتمدة

## التحديثات الأخيرة

- 📅 **30 ديسمبر 2024**: [مزامنة ساعات العمل المرنة في سجلات الحضور](./attendance-working-hours-sync.md) - نظام مرن لحساب وقت انتهاء العمل بناءً على وقت الدخول الفعلي + ساعات العمل من الإعدادات (النسخة 2.0)
- 📅 **29 ديسمبر 2024**: [تحسين عرض القائمة الجانبية للشاشات الكبيرة](./attendance-permissions-update.md#29-ديسمبر-2024---تحسين-عرض-القائمة-الجانبية-للشاشات-الكبيرة)
- 📅 **29 ديسمبر 2024**: [إضافة سجل الحضور في الإجراءات السريعة للمدراء](./attendance-permissions-update.md#29-ديسمبر-2024---إضافة-سجل-الحضور-في-الإجراءات-السريعة-للمدراء)
- 📅 **29 ديسمبر 2024**: [إضافة الحضور والانصراف للموظفين في القائمة الجانبية](./attendance-permissions-update.md#29-ديسمبر-2024---إضافة-الحضور-والانصراف-للموظفين-في-القائمة-الجانبية)
- 📅 **29 ديسمبر 2024**: [تقييد عرض البيانات للمدراء حسب الموظفين التابعين](./attendance-permissions-update.md#29-ديسمبر-2024---تقييد-عرض-البيانات-للمدراء-حسب-الموظفين-التابعين)
- 📅 **29 ديسمبر 2024**: [تحديثات القائمة الجانبية ولوحة التحكم للمدراء](./attendance-permissions-update.md#29-ديسمبر-2024---تحديثات-القائمة-الجانبية-ولوحة-التحكم-للمدراء)
- 📅 **28 ديسمبر 2024**: [إخفاء إحصائية الاستئذان الرسمي وإعادة تصميم لوحة التحكم](./attendance-permissions-update.md)
- **اليوم**: إخفاء إحصائية الاستئذان الرسمي وإعادة تصميم لوحة التحكم بشكل حديث وجميل مع تحسينات بصرية وتفاعلية
- **اليوم**: تحديث صلاحيات الوصول إلى سجل الحضور والانصراف - السماح للموظفين والمدراء بمشاهدة السجلات مع تقييد التعديل للإدارة والموارد البشرية فقط
- **25/05/2025**: تحسين تجميع بيانات الحضور حسب الموظف - تجميع البيانات حسب اسم الموظف عند الفلترة بموظف محدد لتقليل التكرار
- **25/05/2025**: إضافة عمود المدير المباشر في قائمة الموظفين - تحسين عرض الموظفين لإظهار المدير المباشر لكل موظف
- **25/05/2025**: تحسين عرض الحركات في طباعة التقرير - إضافة نوع الاستئذان ومدة الخروج وإجمالي الخروج مثل العرض الأساسي
- **25/05/2025**: إصلاح مشكلة طباعة التقرير في صفحة الحضور - حل مشكلة الصفحة الفارغة (about:blank) وتحسين دالة الطباعة
- **25/05/2025**: إصلاح خطأ "Cannot access before initialization" في صفحة الحضور - حل مشكلة ترتيب الدوال وJavaScript Hoisting
- **25/05/2025**: إصلاح قائمة المدير المباشر في نموذج الموظفين - حل مشكلة عدم ظهور قائمة المدراء في نموذج تعديل الموظفين
- **25/05/2025**: إصلاح صفحة الحضور البيضاء ومشكلة الـ Favicon - حل مشكلة الصفحة البيضاء في كشف الحضور وتحسين عرض الـ favicon
- **25/05/2025**: تصفية كشف الحضور لاستبعاد الإجازات المعتمدة - تحسين بوابة الأمن لعدم عرض الموظفين في الإجازات المعتمدة
- **24/05/2025**: إصلاح عرض الإجازات والعطل الرسمية - حل مشكلة ظهور العطل الرسمية وإضافة عرض الإجازات المعتمدة مع أسبابها
- **24/05/2025**: إصلاح Next.js 15 Route Parameters - حل مشاكل TypeScript في معاملات المسارات وتحديث جميع API routes
- **24/05/2025**: إدارة الأقسام ورؤساء الأقسام - حل مشكلة عدم ظهور رؤساء الأقسام وإضافة واجهة محسنة
- **24/05/2025**: إصلاح مشكلة بطء التحميل الأولي - تحسين Next.js config، caching، وwebpack optimization (تحسين 70-80%)
- **24/05/2025**: إصلاح مشكلة بطء تسجيل الخروج - تحسين AuthProvider وإضافة API مخصص للخروج السريع
- **24/05/2025**: إزالة قيود الوقت من تسجيل الدخول والخروج في العمل بعد الدوام - السماح بالتسجيل في أي وقت
- **24/05/2025**: إصلاح صفحة security/after-hours - حل مشكلة الصفحة البيضاء عند وجود تصاريح عمل
- **24/05/2025**: إصلاح نموذج إضافة الزوار - حل مشكلة الصفحة البيضاء وتحسين معالجة الأخطاء
- **24/05/2025**: توحيد تنسيق التاريخ من الهجري إلى الميلادي (24 مايو 2025 م)
- **24/05/2025**: إصلاح مشكلة الأداء في OTP authentication (من 510 ثانية إلى <1 ثانية)
- **24/05/2025**: إصلاح خطأ validation في OTP login
- **24/05/2025**: إصلاح مشكلة OTP login loop باستخدام NextAuth.js
- **24/05/2025**: تحويل خط Tajawal إلى نسخة محلية لتحسين سرعة التحميل
- **24/05/2025**: إضافة ميزة تعديل كلمات المرور في نماذج تعديل المستخدمين والموظفين

## هيكل التوثيق

```
docs/
├── dev-guide.md                         # فهرس التوثيق (هذا الملف)
├── password-management.md               # إدارة كلمات المرور
├── local-fonts.md                      # الخطوط المحلية
├── date-format-standardization.md      # توحيد تنسيق التاريخ
├── webpack-module-loading-fix.md       # إصلاح خطأ تحميل وحدات Webpack
├── otp-login-fix.md                    # إصلاح مشكلة OTP login loop
├── otp-validation-fix.md               # إصلاح خطأ التحقق من OTP
├── otp-performance-fix.md              # إصلاح مشكلة الأداء في OTP
├── visitors-form-fix.md                # إصلاح نموذج إضافة الزوار
├── security-after-hours-fix.md         # إصلاح صفحة security/after-hours
├── after-hours-time-restriction-removal.md # إزالة قيود الوقت
├── logout-performance-fix.md           # إصلاح مشكلة بطء تسجيل الخروج
└── templates/                           # قوالب التوثيق
    ├── feature.md
    ├── bugfix.md
    └── spec.md
```

## إرشادات التوثيق

### للميزات الجديدة
- استخدم قالب feature.md
- وثق جميع التغييرات في Frontend و Backend
- أضف أمثلة للاستخدام
- اربط الوثيقة بهذا الفهرس

### للإصلاحات
- استخدم قالب bugfix.md للإصلاحات الكبيرة
- أضف تعليقات في الكود للإصلاحات الصغيرة

### للمواصفات التقنية
- استخدم قالب spec.md
- وثق معمارية النظام والقرارات التقنية

## معايير التوثيق

### تسميات الملفات
- استخدم kebab-case (مثل: password-management.md)
- اختر أسماء وصفية وواضحة

### تنسيق المحتوى
- استخدم العناوين بالتسلسل الهرمي (##، ###)
- أضف أمثلة كود مع تحديد اللغة
- استخدم القوائم المرقمة للخطوات
- استخدم النص العريض للمصطلحات المهمة

### التحديث
- حدث التاريخ في كل تعديل
- أضف رقم إصدار للميزات الكبيرة
- اربط التحديثات بهذا الفهرس

## الأولويات

### مسار حرج (Critical Path)
- أمان النظام
- قاعدة البيانات
- المصادقة

### ميزات رئيسية (Major Features)
- إدارة المستخدمين
- نظام الحضور
- التقارير

### تغييرات بسيطة (Minor Changes)
- تحسينات واجهة المستخدم
- رسائل النظام

---

*آخر تحديث: 24/05/2025* 

# دليل التطوير - نظام حضور الموظفين

## فهرس الملفات التقنية

- [إعداد قاعدة البيانات](database-setup.md)
- [مصادقة المستخدم](authentication.md)
- [إدارة أدوار المستخدمين](user-roles.md)
- [نظام إدارة الحضور](attendance-management.md)
- [منطق حساب مدة الخروج](exit-duration-calculation.md)
- [API التقارير](reports-api.md)
- [واجهة المستخدم](user-interface.md)
- [أمان النظام](security.md)
- [النشر والإعداد](deployment.md)

## ملاحظات التطوير

- استخدم التوثيق الموجود قبل إجراء أي تغييرات
- اتبع نمط الأكواد الموجود في المشروع
- اختبر جميع التغييرات قبل الدفع
- استخدم Google Font Tajawal للعربية مع دعم RTL
- المنطقة الزمنية: Asia/Muscat
- تنسيق التاريخ: dd/mm/yyyy 

### نظام الأمن والحضور
- [تصفية كشف الحضور لاستبعاد الإجازات المعتمدة](./security-attendance-filter-approved-leaves.md) - تحسين كشف الحضور في بوابة الأمن لاستبعاد الموظفين في الإجازات المعتمدة

## التحسينات والميزات
- [تحسين تجميع بيانات الحضور](./attendance-grouping-enhancement.md) - تجميع ذكي للبيانات وتحسين عرض مدة الخروج
- [إضافة حقل عدد ساعات العمل](./working-hours-setting.md) - إضافة إعداد جديد لتحديد ساعات العمل اليومية

## القوالب
- [قالب الميزات](./templates/feature.md)
- [قالب إصلاح الأخطاء](./templates/bugfix.md)
- [قالب المواصفات](./templates/spec.md)

## إرشادات التطوير

### متطلبات التوثيق
- **المسار الحرج**: اتباع عملية التوثيق الكاملة دائماً
- **الميزات الرئيسية**: توثيق كامل مطلوب
- **التغييرات الطفيفة**: تحديثات توثيق مبسطة
- **إصلاح الأخطاء**: توثيق أدنى (تعليقات مضمنة كافية)
- **النماذج الأولية**: توثيق خفيف مع تسمية "PROTOTYPE" واضحة

### عملية التوثيق
- **فحص التوثيق الإجباري**: فحص docs/dev-guide.md دائماً قبل بدء أي مهمة
- **التنفيذ أولاً للتوثيق**: تحديث/إنشاء الوثائق قبل كتابة الكود
- **تخزين الوثائق التقنية**: في مجلد /docs
- **تسمية خاصة بالملف**: docs/features/<feature-name>.md

### تنسيق التوثيق الصديق للذكاء الاصطناعي
- استخدام تسلسل هرمي واضح للعناوين
- خطوات مرقمة للعمليات
- كتل كود مع علامات اللغة
- مصطلحات المسرد بخط **عريض**
- تجنب القوائم المتداخلة أعمق من 3 مستويات

### بروتوكول الاتساق
- عكس أنماط الوثائق الموجودة
- استخدام القالب القياسي

### التحقق بعد التنفيذ
- التحقق من مطابقة الوثائق للتنفيذ
- إزالة المحتوى المتقادم
- تضمين فحص الوثائق الآلي أثناء CI/CD

## الإعدادات التقنية

### الخط المستخدم
- Google Font Tajawal

### الدعم اللغوي
- دعم العربية و RTL لجميع الأدوات المستخدمة

### الإعدادات الإقليمية
- المنطقة الزمنية: Asia/Muscat
- تنسيق التاريخ: dd/mm/yyyy

---

**آخر تحديث**: 25/05/2025  
**الإصدار**: 1.2  
**المطور**: فريق تطوير AttendPro 

# دليل تطوير نظام الحضور AttendPro

## فهرس الوثائق

### الميزات الأساسية
- [working-hours-setting.md](./working-hours-setting.md) - إضافة حقل عدد ساعات العمل
- [attendance-grouping-enhancement.md](./attendance-grouping-enhancement.md) - تحسين عرض بيانات الحضور
- [working-hours-calculation-update.md](./working-hours-calculation-update.md) - تحديث حساب مدة الخروج

### القوالب
- [templates/feature.md](./templates/feature.md) - قالب توثيق الميزات الجديدة
- [templates/bugfix.md](./templates/bugfix.md) - قالب توثيق إصلاح الأخطاء
- [templates/spec.md](./templates/spec.md) - قالب المواصفات التقنية

## إرشادات التطوير

### 1. التوثيق الإجباري
- تحقق من هذا الملف قبل بدء أي مهمة
- اتبع عملية التوثيق المحددة
- استخدم القوالب المناسبة

### 2. الأولويات
عند ضيق الموارد، ركز على:
- وثائق بنية النظام
- مواصفات الاتصال بقاعدة البيانات  
- المكونات الحرجة للأمان

### 3. تصنيف الوثائق
- **المسار الحرج**: توثيق كامل مطلوب
- **الميزات الرئيسية**: توثيق مفصل
- **التغييرات الصغيرة**: تحديث مبسط
- **إصلاح الأخطاء**: تعليقات داخلية كافية

## معايير التوثيق

### التنسيق الودود للذكاء الاصطناعي
```markdown
## العنوان الرئيسي
### العنوان الفرعي
#### تفاصيل التنفيذ

1. خطوات مرقمة
2. للعمليات

```code
كتل الكود مع تسميات اللغة
```

**المصطلحات الهامة** بخط عريض
```

### متطلبات المحتوى
- نظرة عامة للتنفيذ
- أمثلة على API/هيكل البيانات
- مخططات التكامل
- اعتبارات الأمان

## الإعدادات العامة

### الخط
- استخدم خط Google Font Tajawal

### اللغة والاتجاه
- دعم العربية و RTL لجميع الأدوات
- المنطقة الزمنية: Asia/Muscat
- تنسيق التاريخ: dd/mm/yyyy

## التحقق بعد التنفيذ
- تطابق الوثائق مع التنفيذ
- إزالة المحتوى المتقادم
- تضمين فحص الوثائق في CI/CD