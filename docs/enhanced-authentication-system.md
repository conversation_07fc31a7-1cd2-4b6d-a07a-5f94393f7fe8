# نظام المصادقة المحسن - إدارة الجلسات الآمنة

## نظرة عامة

تم تطوير نظام مصادقة محسن يوفر إدارة آمنة للجلسات مع الميزات التالية:
- **انتهاء الجلسة التلقائي**: بعد 4 ساعات من عدم النشاط
- **تسجيل خروج عند إغلاق المتصفح**: تنظيف الجلسة عند إغلاق التبويب/المتصفح
- **مراقبة النشاط**: تتبع نشاط المستخدم وتحديث الجلسة
- **فحص دوري**: التحقق من صحة الجلسة كل ساعة
- **حماية المسارات**: middleware لحماية جميع المسارات

## الميزات الرئيسية

### 🔒 إدارة الجلسات الآمنة
- مدة الجلسة: 4 ساعات كحد أقصى
- انتهاء تلقائي عند عدم النشاط
- تنظيف البيانات عند إغلاق المتصفح

### 📊 مراقبة النشاط
- تتبع أحداث المستخدم (النقر، الحركة، الكتابة)
- تحديث timestamp آخر نشاط
- فحص دوري كل دقيقة

### 🛡️ حماية المسارات
- Middleware لحماية جميع المسارات
- إعادة توجيه تلقائية لصفحة تسجيل الدخول
- استثناءات للمسارات العامة

### ⚡ أداء محسن
- فحص الجلسة كل ساعة (بدلاً من كل طلب)
- تخزين محلي لتتبع النشاط
- تنظيف تلقائي للبيانات

## إحصائيات لوحة التحكم المحسنة

### نظرة عامة
تم تطوير نظام إحصائيات متقدم يوفر بيانات دقيقة في الوقت الفعلي مع التصنيفات التالية:
- **حضور كامل**: موظف دخل ولم يخرج أو خرج بعد انتهاء الدوام الرسمي
- **حضور جزئي**: موظف دخل وخرج قبل انتهاء الدوام الرسمي (exitType: PERSONAL)
- **في إجازة**: موظف لديه إجازة معتمدة اليوم
- **غياب**: موظف لم يسجل أي حضور ولا يوجد لديه إجازة معتمدة

### طريقة حساب كل تصنيف

#### 1. الحضور الكامل (Full Attendance)
```typescript
// الشروط:
// 1. لديه سجل حضور اليوم (checkInTime موجود)
// 2. إما لم يخرج بعد (checkOutTime = null)
// 3. أو خرج بعد انتهاء الدوام الرسمي (exitType = OFFICIAL)

const fullAttendanceRecords = await prisma.attendanceRecord.findMany({
  where: {
    userId: { in: teamMemberIds },
    date: { gte: startOfDay, lt: endOfDay },
    OR: [
      { checkOutTime: null }, // لا يزال في المكتب
      { 
        checkOutTime: { not: null },
        exitType: { in: ['OFFICIAL', 'WORK', 'HEALTH'] }
      }
    ]
  }
});
```

#### 2. الحضور الجزئي (Partial Attendance)
```typescript
// الشروط:
// 1. لديه سجل حضور اليوم (checkInTime موجود)
// 2. خرج قبل انتهاء الدوام الرسمي (exitType = PERSONAL)

const partialAttendanceRecords = await prisma.attendanceRecord.findMany({
  where: {
    userId: { in: teamMemberIds },
    date: { gte: startOfDay, lt: endOfDay },
    checkOutTime: { not: null },
    exitType: 'PERSONAL'
  }
});
```

#### 3. في إجازة (On Leave)
```typescript
// الشروط:
// 1. لديه إجازة معتمدة تشمل اليوم الحالي
// 2. تاريخ بداية الإجازة <= اليوم <= تاريخ نهاية الإجازة

const employeesOnLeave = await prisma.leaveRequest.findMany({
  where: {
    userId: { in: teamMemberIds },
    status: 'APPROVED',
    startDate: { lte: today },
    endDate: { gte: today }
  }
});
```

#### 4. الغياب (Absent)
```typescript
// الشروط:
// 1. ليس لديه أي سجل حضور اليوم
// 2. ليس لديه إجازة معتمدة اليوم
// 3. ليس عطلة رسمية أو نهاية أسبوع

const absentEmployees = teamMembers.filter(member => 
  !employeesWithAttendanceToday.has(member.id) && 
  !employeesOnLeaveToday.has(member.id)
);
```

### الميزات الجديدة

#### 1. إحصائيات المدير المحسنة
```typescript
interface ManagerDashboardStats {
  teamOverview: {
    totalEmployees: number;
    fullAttendanceToday: number;     // حضور كامل
    partialAttendanceToday: number;  // حضور جزئي
    onLeaveToday: number;            // في إجازة معتمدة
    absentToday: number;             // غياب بدون إذن
    lateToday: number;               // متأخرون
    isWeekend: boolean;
    isOfficialHoliday: boolean;
    holidayName: string | null;
  };
  details: {
    fullAttendanceEmployees: Array<Employee>;
    partialAttendanceEmployees: Array<Employee & { exitTime: string; exitType: string }>;
    employeesOnLeave: Array<Employee & { leaveReason: string }>;
    absentEmployees: Array<Employee>;
    lateEmployees: Array<Employee & { checkInTime: string; minutesLate: number }>;
  };
}
```

#### 2. إحصائيات الموظف المحسنة
```typescript
interface EmployeeDashboardStats {
  todayAttendance: {
    status: 'FULL_ATTENDANCE' | 'PARTIAL_ATTENDANCE' | 'ON_LEAVE' | 'ABSENT';
    checkInTime: string | null;
    checkOutTime: string | null;
    exitType: ExitType | null;
    isOnLeave: boolean;
    leaveReason: string | null;
    workingHours: number;           // ساعات العمل الفعلية
    expectedWorkingHours: number;   // ساعات العمل المطلوبة
    isWeekend: boolean;
    isOfficialHoliday: boolean;
    holidayName: string | null;
  };
  thisMonthStats: {
    totalWorkDays: number;
    fullAttendanceDays: number;     // أيام الحضور الكامل
    partialAttendanceDays: number;  // أيام الحضور الجزئي
    leaveDays: number;              // أيام الإجازة المعتمدة
    absentDays: number;             // أيام الغياب
    lateDays: number;               // أيام التأخير
    attendanceRate: number;         // معدل الحضور الإجمالي
    fullAttendanceRate: number;     // معدل الحضور الكامل
  };
}
```

#### 3. حساب دقيق للإحصائيات
- **استثناء العطل الرسمية**: من حساب أيام العمل
- **استثناء أيام الإجازة**: من حساب معدل الحضور
- **التمييز بين أنواع الحضور**: حضور كامل vs جزئي
- **تتبع أسباب الخروج**: OFFICIAL, PERSONAL, WORK, HEALTH

#### 4. مؤشرات الأداء
```typescript
// حساب معدل الحضور الكامل
const fullAttendanceRate = effectiveWorkDays > 0 ? 
  Math.round((fullAttendanceDays / effectiveWorkDays) * 100) : 100;

// حساب معدل الحضور الإجمالي (كامل + جزئي)
const totalAttendanceRate = effectiveWorkDays > 0 ? 
  Math.round(((fullAttendanceDays + partialAttendanceDays) / effectiveWorkDays) * 100) : 100;

// حساب متوسط ساعات العمل اليومية
const avgDailyWorkingHours = attendanceDays > 0 ? 
  Math.round((totalWorkingHours / attendanceDays) * 10) / 10 : 0;
```

### التحسينات التقنية

#### 1. استعلامات قاعدة البيانات المحسنة
```sql
-- جلب جميع سجلات الحضور اليوم مع تفاصيل الخروج
SELECT ar.*, u.name, u.employeeNumber 
FROM AttendanceRecord ar
JOIN User u ON ar.userId = u.id
WHERE ar.date >= ? AND ar.date < ?
AND ar.userId IN (?)

-- تصنيف الموظفين حسب نوع الحضور
CASE 
  WHEN ar.checkOutTime IS NULL THEN 'FULL_ATTENDANCE'
  WHEN ar.exitType = 'PERSONAL' THEN 'PARTIAL_ATTENDANCE'
  WHEN ar.exitType IN ('OFFICIAL', 'WORK', 'HEALTH') THEN 'FULL_ATTENDANCE'
  ELSE 'UNKNOWN'
END as attendanceType
```

#### 2. معالجة أوقات العمل
```typescript
// حساب ساعات العمل الفعلية
function calculateWorkingHours(checkIn: Date, checkOut: Date | null): number {
  if (!checkOut) {
    // إذا لم يخرج بعد، احسب حتى الآن أو نهاية الدوام
    const now = new Date();
    const workEndTime = getWorkEndTime(checkIn);
    checkOut = now < workEndTime ? now : workEndTime;
  }
  
  const diffMs = checkOut.getTime() - checkIn.getTime();
  return Math.round((diffMs / (1000 * 60 * 60)) * 10) / 10; // ساعات بدقة عشرية
}

// تحديد نوع الحضور بناءً على وقت الخروج
function determineAttendanceType(
  checkIn: Date, 
  checkOut: Date | null, 
  exitType: ExitType | null
): AttendanceType {
  if (!checkOut) return 'FULL_ATTENDANCE'; // لا يزال في المكتب
  
  if (exitType === 'PERSONAL') return 'PARTIAL_ATTENDANCE';
  
  return 'FULL_ATTENDANCE'; // OFFICIAL, WORK, HEALTH
}
```

#### 3. واجهة المستخدم المحسنة
- **مؤشرات ملونة متقدمة**: لكل نوع حضور
- **تفاصيل شاملة**: أوقات الدخول والخروج وأسباب الخروج
- **رسوم بيانية**: لتوزيع أنواع الحضور
- **تحديث فوري**: عند تغيير حالة الحضور

### قواعد العمل

#### 1. أولوية التصنيف
1. **إجازة معتمدة** - أعلى أولوية
2. **عطلة رسمية/نهاية أسبوع** - لا تحسب في الإحصائيات
3. **حضور جزئي** - خروج مبكر شخصي
4. **حضور كامل** - حضور طبيعي أو خروج رسمي
5. **غياب** - لا يوجد سجل حضور ولا إجازة

#### 2. معايير التأخير
```typescript
// تحديد التأخير (بعد 8:30 صباحاً افتراضياً)
const lateThreshold = new Date(startOfDay);
const [hours, minutes] = (settings?.workStartTime || '08:30').split(':');
lateThreshold.setHours(parseInt(hours), parseInt(minutes), 0, 0);

const isLate = checkInTime > lateThreshold;
const minutesLate = isLate ? 
  Math.round((checkInTime.getTime() - lateThreshold.getTime()) / (1000 * 60)) : 0;
```

#### 3. معايير الخروج المبكر
```typescript
// تحديد الخروج المبكر (قبل 2:30 مساءً افتراضياً)
const earlyThreshold = new Date(startOfDay);
const [hours, minutes] = (settings?.workEndTime || '14:30').split(':');
earlyThreshold.setHours(parseInt(hours), parseInt(minutes), 0, 0);

const isEarlyExit = checkOutTime < earlyThreshold;
const exitType = isEarlyExit ? 'PERSONAL' : 'OFFICIAL';
```

## الملفات المحدثة

### إحصائيات لوحة التحكم
- `src/app/api/dashboard/manager-stats/route.ts` - إحصائيات المدير المحسنة
- `src/app/api/dashboard/employee-stats/route.ts` - إحصائيات الموظف المحسنة
- `src/app/dashboard/manager/page.tsx` - واجهة لوحة تحكم المدير
- `src/app/dashboard/employee/page.tsx` - واجهة لوحة تحكم الموظف

### نظام المصادقة
- `middleware.ts` - حماية المسارات المحسنة
- `src/lib/auth.ts` - إعدادات NextAuth محسنة
- `src/providers/auth-provider.tsx` - مراقبة الجلسة والنشاط
- `src/app/api/auth/session-check/route.ts` - فحص صحة الجلسة
- `src/app/api/auth/logout/route.ts` - تسجيل الخروج المحسن
- `src/app/login/page.tsx` - صفحة تسجيل الدخول المحسنة
- `src/types/next-auth.d.ts` - تعريفات الأنواع

## الاستخدام

### للمطورين
```typescript
// جلب إحصائيات المدير
const response = await fetch('/api/dashboard/manager-stats');
const stats = await response.json();

// التحقق من حالة الموظف
if (stats.teamOverview.isOfficialHoliday) {
  // عطلة رسمية
} else if (stats.teamOverview.isWeekend) {
  // عطلة نهاية أسبوع
}

// عرض الموظفين في إجازة
stats.details.employeesOnLeave.forEach(emp => {
  console.log(`${emp.name} في إجازة`);
});
```

### للمستخدمين
1. **لوحة تحكم المدير**:
   - عرض إحصائيات الفريق في الوقت الفعلي
   - التمييز بين الغياب والإجازة
   - مؤشرات العطل والإجازات

2. **لوحة تحكم الموظف**:
   - عرض حالة الحضور الشخصية
   - معدل الحضور الفعلي
   - تفاصيل الإجازات المعتمدة

## الأمان والأداء

### الأمان
- **فحص الصلاحيات**: على مستوى API
- **تشفير الجلسات**: باستخدام JWT
- **تنظيف البيانات**: عند انتهاء الجلسة
- **حماية من CSRF**: مع NextAuth

### الأداء
- **استعلامات محسنة**: تجميع البيانات في استعلام واحد
- **تخزين مؤقت**: للبيانات الثابتة
- **تحديث ذكي**: فقط عند الحاجة
- **ضغط البيانات**: في الاستجابات

## استكشاف الأخطاء

### مشاكل شائعة
1. **انتهاء الجلسة المبكر**: تحقق من إعدادات النشاط
2. **عدم تحديث الإحصائيات**: تحقق من الاتصال بقاعدة البيانات
3. **مشاكل العطل**: تحقق من جدول `OfficialHoliday`

### سجلات النظام
```javascript
// تفعيل سجلات التطوير
if (process.env.NODE_ENV === "development") {
  console.log("نشاط الجلسة:", message.session?.user?.name);
}
```

## التطوير المستقبلي

### ميزات مخططة
- **إشعارات فورية**: للتغييرات المهمة
- **تقارير متقدمة**: تحليل الحضور والغياب
- **تكامل الهاتف المحمول**: تطبيق مخصص
- **ذكاء اصطناعي**: توقع أنماط الحضور

### تحسينات تقنية
- **WebSocket**: للتحديثات الفورية
- **Service Workers**: للعمل دون اتصال
- **تحليلات متقدمة**: لأداء النظام
- **اختبارات تلقائية**: لضمان الجودة

## ملخص التحسينات الجديدة

### 🎯 التصنيفات المحسنة للحضور

تم تطوير نظام تصنيف متقدم يقسم حالات الحضور إلى أربع فئات دقيقة:

#### 1. حضور كامل (Full Attendance)
- **التعريف**: موظف دخل ولم يخرج أو خرج بعد انتهاء الدوام الرسمي
- **الشروط**: 
  - لديه سجل دخول اليوم
  - إما لا يزال في المكتب (checkOutTime = null)
  - أو خرج بنوع رسمي (exitType: OFFICIAL, WORK, HEALTH)
- **اللون**: أخضر 🟢

#### 2. حضور جزئي (Partial Attendance)  
- **التعريف**: موظف دخل وخرج قبل انتهاء الدوام الرسمي
- **الشروط**:
  - لديه سجل دخول اليوم
  - خرج مبكراً بنوع شخصي (exitType: PERSONAL)
- **اللون**: برتقالي 🟠

#### 3. في إجازة (On Leave)
- **التعريف**: موظف لديه إجازة معتمدة تشمل اليوم الحالي
- **الشروط**:
  - إجازة معتمدة (status: APPROVED)
  - تاريخ بداية الإجازة ≤ اليوم ≤ تاريخ نهاية الإجازة
- **اللون**: أزرق 🔵

#### 4. غياب (Absent)
- **التعريف**: موظف لم يسجل أي حضور ولا يوجد لديه إجازة معتمدة
- **الشروط**:
  - لا يوجد سجل حضور اليوم
  - لا يوجد إجازة معتمدة اليوم
  - ليس عطلة رسمية أو نهاية أسبوع
- **اللون**: أحمر 🔴

### 📊 الإحصائيات المحسنة

#### إحصائيات المدير
```typescript
interface ManagerDashboardStats {
  teamOverview: {
    totalEmployees: number;
    fullAttendanceToday: number;     // حضور كامل
    partialAttendanceToday: number;  // حضور جزئي  
    onLeaveToday: number;            // في إجازة
    absentToday: number;             // غياب
    lateToday: number;               // متأخرون
  };
  details: {
    fullAttendanceEmployees: Employee[];      // تفاصيل الحضور الكامل
    partialAttendanceEmployees: Employee[];   // تفاصيل الحضور الجزئي
    employeesOnLeave: Employee[];             // تفاصيل الإجازات
    absentEmployees: Employee[];              // تفاصيل الغياب
    lateEmployees: Employee[];                // تفاصيل المتأخرين
  };
}
```

#### إحصائيات الموظف
```typescript
interface EmployeeDashboardStats {
  todayAttendance: {
    status: 'FULL_ATTENDANCE' | 'PARTIAL_ATTENDANCE' | 'ON_LEAVE' | 'ABSENT';
    workingHours: number;           // ساعات العمل الفعلية
    expectedWorkingHours: number;   // ساعات العمل المطلوبة
  };
  thisMonthStats: {
    fullAttendanceDays: number;     // أيام الحضور الكامل
    partialAttendanceDays: number;  // أيام الحضور الجزئي
    leaveDays: number;              // أيام الإجازة
    absentDays: number;             // أيام الغياب
    attendanceRate: number;         // معدل الحضور الإجمالي
    fullAttendanceRate: number;     // معدل الحضور الكامل
  };
}
```

### 🎨 واجهة المستخدم المحسنة

#### لوحة تحكم المدير
- **عرض شامل**: 6 بطاقات إحصائية (إجمالي، حضور كامل، حضور جزئي، إجازة، غياب، متأخرون)
- **تفاصيل تفاعلية**: قوائم قابلة للتمرير تعرض أسماء الموظفين في كل فئة
- **أوقات دقيقة**: عرض أوقات الدخول والخروج لكل موظف
- **مؤشرات ملونة**: ألوان مميزة لكل نوع حضور

#### لوحة تحكم الموظف  
- **حالة اليوم**: بطاقة كبيرة تعرض حالة الحضور الحالية
- **ساعات العمل**: شريط تقدم يعرض ساعات العمل الفعلية مقابل المطلوبة
- **إحصائيات شهرية**: 4 بطاقات تعرض توزيع أيام الحضور
- **تفاصيل الأوقات**: عرض أوقات الدخول والخروج مع نوع الخروج

### ⚡ التحسينات التقنية

#### 1. استعلامات محسنة
```sql
-- جلب جميع سجلات الحضور مع التفاصيل
SELECT ar.*, u.name, u.employeeNumber 
FROM AttendanceRecord ar
JOIN User u ON ar.userId = u.id
WHERE ar.date >= ? AND ar.date < ?
ORDER BY ar.checkInTime DESC

-- تصنيف نوع الحضور
CASE 
  WHEN ar.checkOutTime IS NULL THEN 'FULL_ATTENDANCE'
  WHEN ar.exitType = 'PERSONAL' THEN 'PARTIAL_ATTENDANCE'  
  WHEN ar.exitType IN ('OFFICIAL', 'WORK', 'HEALTH') THEN 'FULL_ATTENDANCE'
END as attendanceType
```

#### 2. حساب ذكي للإحصائيات
```typescript
// حساب ساعات العمل الفعلية
function calculateWorkingHours(checkIn: Date, checkOut: Date | null): number {
  if (!checkOut) {
    const now = new Date();
    const workEndTime = getWorkEndTime(checkIn);
    checkOut = now < workEndTime ? now : workEndTime;
  }
  return Math.round(((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60)) * 10) / 10;
}

// تحديد نوع الحضور
function determineAttendanceType(exitType: ExitType | null): AttendanceType {
  if (!exitType) return 'FULL_ATTENDANCE'; // لا يزال في المكتب
  return exitType === 'PERSONAL' ? 'PARTIAL_ATTENDANCE' : 'FULL_ATTENDANCE';
}
```

#### 3. معالجة البيانات المتقدمة
- **تجميع السجلات**: أحدث سجل لكل موظف في اليوم
- **حساب التأخير**: مقارنة وقت الدخول مع وقت العمل المحدد
- **استثناء العطل**: عدم احتساب العطل الرسمية ونهاية الأسبوع
- **معدلات دقيقة**: حساب معدلات الحضور مع استثناء أيام الإجازة

### 🔄 التحديث الفوري

#### مراقبة النشاط
- **تحديث تلقائي**: كل 5 دقائق لجميع الإحصائيات
- **حالة فورية**: تحديث حالة الحضور عند تسجيل الدخول/الخروج
- **مؤشرات حية**: عرض الموظفين الموجودين حالياً في المكتب

#### تجربة المستخدم
- **تحميل سلس**: مؤشرات تحميل أثناء جلب البيانات
- **ألوان متسقة**: نظام ألوان موحد عبر التطبيق
- **استجابة سريعة**: واجهة متجاوبة على جميع الأجهزة

### 📈 فوائد النظام الجديد

#### للإدارة
- **رؤية شاملة**: فهم دقيق لحالة الفريق في الوقت الفعلي
- **تقارير دقيقة**: تمييز واضح بين أنواع الحضور المختلفة
- **اتخاذ قرارات**: بيانات موثوقة لاتخاذ قرارات إدارية

#### للموظفين  
- **شفافية كاملة**: رؤية واضحة لحالة الحضور الشخصية
- **تتبع الأداء**: مراقبة معدلات الحضور والالتزام
- **تحفيز الانضباط**: مؤشرات واضحة للحضور والغياب

#### للنظام
- **أداء محسن**: استعلامات محسنة وتخزين مؤقت ذكي
- **دقة عالية**: حسابات دقيقة مع معالجة الحالات الاستثنائية
- **قابلية التوسع**: بنية قابلة للتطوير والتحسين المستمر

---

*تاريخ التحديث: 30 ديسمبر 2024*  
*الإصدار: 2.0 - التصنيفات المحسنة* 