// استخدام fetch المدمج في Node.js 18+

async function testOTP() {
  try {
    // Debug: removed console.log
// اختبار إرسال OTP
    const response = await fetch('http://localhost:3000/api/auth/otp/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: '99474767' // رقم أحمد محمد
      }),
    });

    const result = await response.json();

    // Debug: removed console.log
// Debug: removed console.log
// Debug: removed console.log
);

    if (result.success) {
      // Debug: removed console.log
// Debug: removed console.log
// Debug: removed console.log
} else {
      // Debug: removed console.log
// Debug: removed console.log
}

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

testOTP();
