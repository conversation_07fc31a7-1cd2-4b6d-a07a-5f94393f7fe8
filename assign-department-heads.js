const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function assignDepartmentHeads() {
  try {
    console.log('🎯 تعيين رؤساء الأقسام...\n');
    
    // البحث عن الأقسام التي ليس لها رؤساء
    const departmentsWithoutHeads = await prisma.department.findMany({
      where: {
        headId: null
      },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    console.log(`📊 عدد الأقسام بدون رئيس: ${departmentsWithoutHeads.length}\n`);

    for (const dept of departmentsWithoutHeads) {
      console.log(`📁 القسم: ${dept.name}`);
      console.log(`   عدد الموظفين: ${dept.users.length}`);
      
      // البحث عن مدير في هذا القسم
      const managerInDept = dept.users.find(user => 
        user.role === 'MANAGER' || user.role === 'HR' || user.role === 'ADMIN'
      );

      if (managerInDept) {
        console.log(`   👨‍💼 تم العثور على مدير في القسم: ${managerInDept.name} (${managerInDept.role})`);
        
        // تعيين المدير كرئيس قسم
        await prisma.department.update({
          where: { id: dept.id },
          data: { headId: managerInDept.id }
        });

        console.log(`   ✅ تم تعيين ${managerInDept.name} كرئيس لقسم ${dept.name}`);
      } else {
        console.log(`   ❌ لا يوجد مدير في هذا القسم`);
        
        // البحث عن أي مدير متاح في النظام
        const availableManager = await prisma.user.findFirst({
          where: {
            role: {
              in: ['MANAGER', 'HR', 'ADMIN']
            },
            // استبعاد من هم رؤساء أقسام بالفعل
            NOT: {
              headOfDepartment: {
                some: {}
              }
            }
          }
        });

        if (availableManager) {
          console.log(`   🔄 تم العثور على مدير متاح: ${availableManager.name} (${availableManager.role})`);
          
          await prisma.department.update({
            where: { id: dept.id },
            data: { headId: availableManager.id }
          });

          console.log(`   ✅ تم تعيين ${availableManager.name} كرئيس لقسم ${dept.name}`);
        } else {
          console.log(`   ⚠️  لا يوجد مدراء متاحون في النظام`);
        }
      }
      
      console.log('');
    }

    // عرض النتيجة النهائية
    console.log('\n📋 حالة الأقسام بعد التحديث:');
    const allDepartments = await prisma.department.findMany({
      include: {
        users: true,
      },
    });

    for (const dept of allDepartments) {
      let headInfo = 'غير محدد';
      if (dept.headId) {
        const head = await prisma.user.findUnique({
          where: { id: dept.headId },
          select: { name: true, email: true, role: true }
        });
        if (head) {
          headInfo = `${head.name} (${head.role})`;
        }
      }
      
      console.log(`📁 ${dept.name}: ${headInfo}`);
    }

  } catch (error) {
    console.error('❌ خطأ في تعيين رؤساء الأقسام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

assignDepartmentHeads(); 