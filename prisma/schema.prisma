// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model with roles
model User {
  id                String              @id @default(cuid())
  employeeNumber    String?             @unique
  name              String
  email             String              @unique
  password          String
  phone             String              @default("")
  position          String?
  role              Role                @default(EMPLOYEE)
  departmentId      String?
  department        Department?         @relation(fields: [departmentId], references: [id])
  managerId         String?
  manager           User?               @relation("ManagerToEmployee", fields: [managerId], references: [id])
  employees         User[]              @relation("ManagerToEmployee")
  headOfDepartment  Department[]        @relation("DepartmentHead")
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]
  visitorRequests   VisitorRequest[]
  afterHoursPermits AfterHoursPermit[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Role enum
enum Role {
  ADMIN
  HR
  MANAGER
  EMPLOYEE
  SECURITY
}

// Department model
model Department {
  id        String   @id @default(cuid())
  name      String   @unique
  headId    String?
  head      User?    @relation("DepartmentHead", fields: [headId], references: [id])
  users     User[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Attendance record model
model AttendanceRecord {
  id              String    @id @default(cuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  date            DateTime  // تاريخ اليوم
  checkInTime     DateTime
  checkOutTime    DateTime?
  exitType        ExitType?
  entryNumber     Int       @default(1) // رقم الدخول في اليوم (1-4)
  recordedBy      String?   // معرف موظف الأمن الذي سجل الحركة
  notes           String?   // ملاحظات إضافية
  emp_working_hrs Float?    // وقت انتهاء العمل المتوقع (timestamp) = وقت الدخول + ساعات العمل
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Exit type enum
enum ExitType {
  OFFICIAL
  PERSONAL
  WORK
  HEALTH
}

// Leave request model
model LeaveRequest {
  id          String      @id @default(cuid())
  userId      String
  user        User        @relation(fields: [userId], references: [id])
  startDate   DateTime
  endDate     DateTime
  reason      String
  status      RequestStatus @default(PENDING)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

// Visitor request model
model VisitorRequest {
  id              String      @id @default(cuid())
  userId          String
  user            User        @relation(fields: [userId], references: [id])
  visitorName     String
  visitorCompany  String?
  purpose         String
  visitDate       DateTime
  visitTime       String?     // وقت الزيارة المتوقع
  companions      String?     // المرافقون
  vehicleInfo     String?     // بيانات السيارة
  checkInTime     DateTime?
  checkOutTime    DateTime?
  status          RequestStatus @default(PENDING)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
}

// After hours work permit model
model AfterHoursPermit {
  id          String      @id @default(cuid())
  userId      String
  user        User        @relation(fields: [userId], references: [id])
  date        DateTime
  startTime   DateTime
  endTime     DateTime
  reason      String
  checkInTime  DateTime?
  checkOutTime DateTime?
  status      RequestStatus @default(PENDING)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

// Request status enum
enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

// System settings model
model Settings {
  id                String   @id @default(cuid())
  companyName       String   @default("شركتي")
  logo              String?
  favicon           String?
  primaryColor      String   @default("#1e40af")
  secondaryColor    String   @default("#3730A3")
  accentColor       String   @default("#10b981")
  workStartTime     String   @default("07:30")
  workEndTime       String   @default("14:30")
  workHoursRequired Float    @default(4.0)
  workingHours      Float    @default(7.0)
  timezone          String   @default("Asia/Muscat")

  // إعدادات WhatsApp
  whatsappApiUrl       String?  @default("https://w.gcccons.org/api")
  whatsappApiSecret    String?
  whatsappAccountKey   String?
  whatsappEnabled      Boolean  @default(false)

  // قوالب الرسائل
  otpTemplate              String? @default("رمز التحقق الخاص بك هو: {otp}")
  earlyExitTemplate        String? @default("الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.")
  visitorArrivalTemplate   String? @default("وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}")
  permitApprovalTemplate   String? @default("تم الموافقة على طلبك. التفاصيل: {details}")

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

// Official holidays model
model OfficialHoliday {
  id          String   @id @default(cuid())
  name        String   // اسم الإجازة الرسمية
  startDate   DateTime // تاريخ بداية الإجازة
  endDate     DateTime // تاريخ نهاية الإجازة (يمكن أن يكون نفس تاريخ البداية لإجازة يوم واحد)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// OTP model for login verification
model OTP {
  id        String   @id @default(cuid())
  phone     String
  code      String
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  @@index([phone])
  @@index([code])
}
