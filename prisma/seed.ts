import { PrismaClient, Role, ExitType, RequestStatus } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 بدء إنشاء البيانات التجريبية...");

  // 1. إنشاء الإعدادات الافتراضية
  console.log("⚙️  إنشاء الإعدادات...");
  const settings = await prisma.settings.upsert({
    where: { id: "default" },
    update: {},
    create: {
      id: "default",
      companyName: "شركة الحضور المتقدم",
      workStartTime: "07:30",
      workEndTime: "14:30",
      workHoursRequired: 4.0,
      workingHours: 7.0,
      timezone: "Asia/Muscat",
      primaryColor: "#1e40af",
      secondaryColor: "#3b82f6",
      accentColor: "#10b981",
      whatsappEnabled: false,
    },
  });

  // 2. إنشاء الأقسام
  console.log("🏢 إنشاء الأقسام...");
  const itDepartment = await prisma.department.create({
    data: {
      name: "قسم تقنية المعلومات",
    },
  });

  const hrDepartment = await prisma.department.create({
    data: {
      name: "قسم الموارد البشرية",
    },
  });

  const financeDepartment = await prisma.department.create({
    data: {
      name: "قسم المحاسبة",
    },
  });

  const marketingDepartment = await prisma.department.create({
    data: {
      name: "قسم التسويق",
    },
  });

  // 3. إنشاء المستخدمين
  console.log("👥 إنشاء المستخدمين...");
  
  // مدير النظام
  const adminPassword = await bcrypt.hash("admin123", 10);
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      employeeNumber: "EMP001",
      name: "أحمد السالمي",
      email: "<EMAIL>",
      phone: "+968-1111-1111",
      position: "مدير عام",
      password: adminPassword,
      role: Role.ADMIN,
      departmentId: itDepartment.id,
    },
  });

  // موظف أمن
  const securityPassword = await bcrypt.hash("security123", 10);
  const security = await prisma.user.create({
    data: {
      employeeNumber: "SEC001",
      name: "محمد الفارسي",
      email: "<EMAIL>",
      phone: "+968-2222-2222",
      position: "موظف أمن",
      password: securityPassword,
      role: Role.SECURITY,
    },
  });

  // مدير الموارد البشرية
  const hrPassword = await bcrypt.hash("hr123", 10);
  const hrManager = await prisma.user.create({
    data: {
      employeeNumber: "HR001",
      name: "فاطمة الزدجالية",
      email: "<EMAIL>",
      phone: "+968-3333-3333",
      position: "مدير الموارد البشرية",
      password: hrPassword,
      role: Role.HR,
      departmentId: hrDepartment.id,
    },
  });

  // مدير قسم تقنية المعلومات
  const itManagerPassword = await bcrypt.hash("manager123", 10);
  const itManager = await prisma.user.create({
    data: {
      employeeNumber: "IT001",
      name: "خالد البلوشي",
      email: "<EMAIL>",
      phone: "+968-4444-4444",
      position: "مدير تقنية المعلومات",
      password: itManagerPassword,
      role: Role.MANAGER,
      departmentId: itDepartment.id,
    },
  });

  // تحديث الأقسام لإضافة رؤساء الأقسام
  await prisma.department.update({
    where: { id: hrDepartment.id },
    data: { headId: hrManager.id },
  });

  await prisma.department.update({
    where: { id: itDepartment.id },
    data: { headId: itManager.id },
  });

  // موظفين عاديين
  const employees = [];
  const employeePassword = await bcrypt.hash("employee123", 10);

  // موظفي تقنية المعلومات
  for (let i = 1; i <= 5; i++) {
    const employee = await prisma.user.create({
      data: {
        employeeNumber: `IT00${i + 1}`,
        name: `موظف تقنية ${i}`,
        email: `it.employee${i}@attendpro.com`,
        phone: `+968-500${i}-000${i}`,
        position: i <= 2 ? "مطور برمجيات" : "محلل أنظمة",
        password: employeePassword,
        role: Role.EMPLOYEE,
        departmentId: itDepartment.id,
        managerId: itManager.id,
      },
    });
    employees.push(employee);
  }

  // موظفي الموارد البشرية
  for (let i = 1; i <= 3; i++) {
    const employee = await prisma.user.create({
      data: {
        employeeNumber: `HR00${i + 1}`,
        name: `موظف موارد بشرية ${i}`,
        email: `hr.employee${i}@attendpro.com`,
        phone: `+968-600${i}-000${i}`,
        position: "أخصائي موارد بشرية",
        password: employeePassword,
        role: Role.EMPLOYEE,
        departmentId: hrDepartment.id,
        managerId: hrManager.id,
      },
    });
    employees.push(employee);
  }

  // موظفي المحاسبة
  for (let i = 1; i <= 4; i++) {
    const employee = await prisma.user.create({
      data: {
        employeeNumber: `FIN00${i}`,
        name: `محاسب ${i}`,
        email: `finance.employee${i}@attendpro.com`,
        phone: `+968-700${i}-000${i}`,
        position: "محاسب",
        password: employeePassword,
        role: Role.EMPLOYEE,
        departmentId: financeDepartment.id,
      },
    });
    employees.push(employee);
  }

  // موظفي التسويق
  for (let i = 1; i <= 3; i++) {
    const employee = await prisma.user.create({
      data: {
        employeeNumber: `MKT00${i}`,
        name: `موظف تسويق ${i}`,
        email: `marketing.employee${i}@attendpro.com`,
        phone: `+968-800${i}-000${i}`,
        position: "أخصائي تسويق",
        password: employeePassword,
        role: Role.EMPLOYEE,
        departmentId: marketingDepartment.id,
      },
    });
    employees.push(employee);
  }

  // 4. إنشاء الإجازات الرسمية
  console.log("🗓️  إنشاء الإجازات الرسمية...");
  const currentYear = new Date().getFullYear();
  
  await prisma.officialHoliday.createMany({
    data: [
      {
        name: "رأس السنة الميلادية",
        startDate: new Date(`${currentYear}-01-01`),
        endDate: new Date(`${currentYear}-01-01`),
      },
      {
        name: "العيد الوطني",
        startDate: new Date(`${currentYear}-11-18`),
        endDate: new Date(`${currentYear}-11-19`),
      },
      {
        name: "عيد الفطر",
        startDate: new Date(`${currentYear}-04-21`),
        endDate: new Date(`${currentYear}-04-23`),
      },
      {
        name: "عيد الأضحى",
        startDate: new Date(`${currentYear}-06-28`),
        endDate: new Date(`${currentYear}-06-30`),
      },
    ],
  });

  // 5. إنشاء سجلات الحضور
  console.log("📋 إنشاء سجلات الحضور...");
  const today = new Date();
  const allUsers = [admin, hrManager, itManager, ...employees];

  // إنشاء سجلات لآخر 30 يوم
  for (let dayOffset = 0; dayOffset < 30; dayOffset++) {
    const recordDate = new Date(today);
    recordDate.setDate(today.getDate() - dayOffset);
    
    // تخطي أيام الجمعة والسبت
    if (recordDate.getDay() === 5 || recordDate.getDay() === 6) continue;

    const startOfDay = new Date(recordDate.getFullYear(), recordDate.getMonth(), recordDate.getDate());

    for (const user of allUsers) {
      // 80% احتمال الحضور العادي
      if (Math.random() < 0.8) {
        const checkInHour = 7 + Math.random() * 2; // بين 7:00 و 9:00
        const checkInMinute = Math.floor(Math.random() * 60);
        const checkInTime = new Date(startOfDay);
        checkInTime.setHours(checkInHour, checkInMinute);

        // السجل الأساسي
        const baseRecord = await prisma.attendanceRecord.create({
          data: {
            userId: user.id,
            date: startOfDay,
            checkInTime: checkInTime,
            entryNumber: 1,
            recordedBy: security.id,
            emp_working_hrs: settings.workingHours,
          },
        });

        // 60% احتمال وجود حركات إضافية (خروج وعودة)
        if (Math.random() < 0.6) {
          const exitTypes: ExitType[] = ["OFFICIAL", "PERSONAL", "WORK", "HEALTH"];
          const randomExitType = exitTypes[Math.floor(Math.random() * exitTypes.length)];
          
          const exitHour = 10 + Math.random() * 3; // بين 10:00 و 13:00
          const exitTime = new Date(startOfDay);
          exitTime.setHours(exitHour, Math.floor(Math.random() * 60));

          await prisma.attendanceRecord.update({
            where: { id: baseRecord.id },
            data: {
              checkOutTime: exitTime,
              exitType: randomExitType,
            },
          });

          // 70% احتمال العودة
          if (Math.random() < 0.7) {
            const returnHour = exitHour + 0.5 + Math.random() * 2; // عودة بعد 30 دقيقة إلى ساعتين
            const returnTime = new Date(startOfDay);
            returnTime.setHours(Math.floor(returnHour), (returnHour % 1) * 60);

            await prisma.attendanceRecord.create({
              data: {
                userId: user.id,
                date: startOfDay,
                checkInTime: returnTime,
                entryNumber: 2,
                recordedBy: security.id,
                emp_working_hrs: settings.workingHours,
              },
            });
          }
        }
      }
    }
  }

  // 6. إنشاء طلبات الإجازة
  console.log("🏖️  إنشاء طلبات الإجازة...");
  const statuses: RequestStatus[] = ["PENDING", "APPROVED", "REJECTED"];
  
  for (let i = 0; i < 15; i++) {
    const randomUser = employees[Math.floor(Math.random() * employees.length)];
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + Math.floor(Math.random() * 60) - 30); // من -30 إلى +30 يوم
    
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + Math.floor(Math.random() * 5) + 1); // من 1 إلى 5 أيام

    await prisma.leaveRequest.create({
      data: {
        userId: randomUser.id,
        startDate: startDate,
        endDate: endDate,
        reason: [
          "إجازة سنوية",
          "ظروف شخصية",
          "إجازة مرضية",
          "سفر عائلي",
          "حضور مناسبة",
        ][Math.floor(Math.random() * 5)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
      },
    });
  }

  // 7. إنشاء طلبات الزوار
  console.log("👥 إنشاء طلبات الزوار...");
  const visitorNames = [
    "أحمد الكندي", "سعد البلوشي", "محمد الفارسي", "علي السالمي", "خالد الزدجالي",
    "فاطمة الغافرية", "عائشة الرواحية", "مريم العبرية", "نورا الشامسية", "هدى الهنائية"
  ];
  
  const companies = [
    "شركة النفط العمانية", "بنك مسقط", "شركة الاتصالات العمانية", "مجموعة السالم",
    "شركة التجارة والصناعة", "مؤسسة الخليج", "شركة التطوير العقاري"
  ];

  const purposes = [
    "اجتماع عمل", "عرض تقديمي", "توقيع عقد", "مراجعة مشروع", "استشارة فنية",
    "زيارة تفقدية", "مقابلة شخصية", "تدريب", "ورشة عمل"
  ];

  for (let i = 0; i < 20; i++) {
    const randomUser = employees[Math.floor(Math.random() * employees.length)];
    const visitDate = new Date(today);
    visitDate.setDate(today.getDate() + Math.floor(Math.random() * 30) - 15); // من -15 إلى +15 يوم

    const visitor = await prisma.visitorRequest.create({
      data: {
        userId: randomUser.id,
        visitorName: visitorNames[Math.floor(Math.random() * visitorNames.length)],
        visitorCompany: Math.random() < 0.7 ? companies[Math.floor(Math.random() * companies.length)] : null,
        purpose: purposes[Math.floor(Math.random() * purposes.length)],
        visitDate: visitDate,
        visitTime: ["09:00", "10:00", "11:00", "14:00", "15:00"][Math.floor(Math.random() * 5)],
        companions: Math.random() < 0.3 ? "مرافق واحد" : null,
        vehicleInfo: Math.random() < 0.4 ? `${Math.floor(Math.random() * 9999)} - ع ك ل` : null,
        status: statuses[Math.floor(Math.random() * statuses.length)],
      },
    });

    // إضافة أوقات دخول وخروج للزوار المعتمدين
    if (visitor.status === "APPROVED" && visitDate <= today) {
      const checkInTime = new Date(visitDate);
      checkInTime.setHours(9 + Math.floor(Math.random() * 6), Math.floor(Math.random() * 60));
      
      const checkOutTime = new Date(checkInTime);
      checkOutTime.setHours(checkInTime.getHours() + 1 + Math.floor(Math.random() * 3));

      await prisma.visitorRequest.update({
        where: { id: visitor.id },
        data: {
          checkInTime: checkInTime,
          checkOutTime: Math.random() < 0.8 ? checkOutTime : null,
        },
      });
    }
  }

  // 8. إنشاء تصاريح العمل بعد الدوام
  console.log("🌙 إنشاء تصاريح العمل بعد الدوام...");
  for (let i = 0; i < 10; i++) {
    const randomUser = employees[Math.floor(Math.random() * employees.length)];
    const workDate = new Date(today);
    workDate.setDate(today.getDate() + Math.floor(Math.random() * 20) - 10); // من -10 إلى +10 يوم

    const startTime = new Date(workDate);
    startTime.setHours(15 + Math.floor(Math.random() * 3), 0, 0); // بين 15:00 و 18:00

    const endTime = new Date(startTime);
    endTime.setHours(startTime.getHours() + 2 + Math.floor(Math.random() * 4)); // 2-6 ساعات

    const permit = await prisma.afterHoursPermit.create({
      data: {
        userId: randomUser.id,
        date: workDate,
        startTime: startTime,
        endTime: endTime,
        reason: [
          "إنهاء مشروع عاجل",
          "صيانة نظام",
          "اجتماع مهم",
          "تدريب متقدم",
          "إعداد تقرير",
        ][Math.floor(Math.random() * 5)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
      },
    });

    // إضافة أوقات دخول وخروج للتصاريح المعتمدة
    if (permit.status === "APPROVED" && workDate <= today) {
      const actualCheckIn = new Date(startTime);
      actualCheckIn.setMinutes(startTime.getMinutes() + Math.floor(Math.random() * 30) - 15);

      const actualCheckOut = new Date(endTime);
      actualCheckOut.setMinutes(endTime.getMinutes() + Math.floor(Math.random() * 60) - 30);

      await prisma.afterHoursPermit.update({
        where: { id: permit.id },
        data: {
          checkInTime: actualCheckIn,
          checkOutTime: Math.random() < 0.9 ? actualCheckOut : null,
        },
      });
    }
  }

  console.log("✅ تم إنشاء البيانات التجريبية بنجاح!");
  console.log(`
📊 ملخص البيانات المُنشأة:
- 👥 المستخدمين: ${allUsers.length + 1} (مدير + موارد بشرية + مدراء أقسام + موظفين + أمن)
- 🏢 الأقسام: 4 أقسام
- 📋 سجلات الحضور: ~${allUsers.length * 20} سجل (آخر 30 يوم)
- 🏖️  طلبات الإجازة: 15 طلب
- 👥 طلبات الزوار: 20 طلب  
- 🌙 تصاريح العمل بعد الدوام: 10 تصاريح
- 🗓️  الإجازات الرسمية: 4 إجازات

🔐 بيانات تسجيل الدخول:
- المدير: <EMAIL> / admin123
- الموارد البشرية: <EMAIL> / hr123  
- مدير تقنية المعلومات: <EMAIL> / manager123
- الأمن: <EMAIL> / security123
- الموظفين: employee[1-15]@attendpro.com / employee123
`);
}

main()
  .catch((e) => {
    console.error("❌ خطأ في إنشاء البيانات التجريبية:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
