"use client";

import { usePublicSettings, useFullSettings } from '@/providers/settings-provider';
import { useWorkingHours } from '@/hooks/useWorkingHours';
import { useSession } from 'next-auth/react';

/**
 * مكون توضيحي لاستخدام نظام الإعدادات المركزي
 * يوضح كيفية استخدام الخطافات المختلفة للإعدادات
 */
export default function SettingsExample() {
  const { data: session } = useSession();
  const { settings, loading: publicLoading, error: publicError } = usePublicSettings();
  const { fullSettings, loading: fullLoading, updateSettings } = useFullSettings();
  const { startTime, endTime, isWorkingTime, formatTime } = useWorkingHours();

  // مثال على استخدام الإعدادات العامة
  const PublicSettingsExample = () => (
    <div className="bg-blue-50 p-4 rounded-lg mb-4">
      <h3 className="font-bold text-blue-800 mb-2">الإعدادات العامة (متاحة للجميع)</h3>
      
      {publicLoading ? (
        <p className="text-blue-600">جاري تحميل الإعدادات...</p>
      ) : publicError ? (
        <p className="text-red-600">خطأ: {publicError}</p>
      ) : (
        <div className="space-y-2">
          <p><strong>اسم الشركة:</strong> {settings?.companyName}</p>
          <p><strong>المنطقة الزمنية:</strong> {settings?.timezone}</p>
          <p><strong>ساعات العمل:</strong> {formatTime(startTime)} - {formatTime(endTime)}</p>
          <p><strong>حالة العمل الحالية:</strong> 
            <span className={`ml-2 px-2 py-1 rounded text-sm ${
              isWorkingTime() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {isWorkingTime() ? 'وقت العمل' : 'خارج وقت العمل'}
            </span>
          </p>
          
          {settings?.logo && (
            <div>
              <strong>الشعار:</strong>
              <img src={settings.logo} alt="شعار الشركة" className="h-12 w-auto mt-1" />
            </div>
          )}
          
          <div className="flex gap-2 items-center">
            <strong>الألوان:</strong>
            <div 
              className="w-6 h-6 rounded border"
              style={{ backgroundColor: settings?.primaryColor }}
              title={`اللون الأساسي: ${settings?.primaryColor}`}
            />
            <div 
              className="w-6 h-6 rounded border"
              style={{ backgroundColor: settings?.secondaryColor }}
              title={`اللون الثانوي: ${settings?.secondaryColor}`}
            />
            <div 
              className="w-6 h-6 rounded border"
              style={{ backgroundColor: settings?.accentColor }}
              title={`لون المؤشر: ${settings?.accentColor}`}
            />
          </div>
        </div>
      )}
    </div>
  );

  // مثال على استخدام الإعدادات الكاملة (للمدراء فقط)
  const FullSettingsExample = () => {
    const isAdmin = session?.user?.role === 'ADMIN';
    
    if (!isAdmin) {
      return (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <h3 className="font-bold text-gray-600 mb-2">الإعدادات الكاملة</h3>
          <p className="text-gray-500">متاحة للمدراء فقط</p>
        </div>
      );
    }

    return (
      <div className="bg-green-50 p-4 rounded-lg mb-4">
        <h3 className="font-bold text-green-800 mb-2">الإعدادات الكاملة (للمدراء)</h3>
        
        {fullLoading ? (
          <p className="text-green-600">جاري تحميل الإعدادات الكاملة...</p>
        ) : (
          <div className="space-y-2">
            <p><strong>ساعات العمل المطلوبة:</strong> {fullSettings?.workHoursRequired} ساعة</p>
            <p><strong>إجمالي ساعات العمل:</strong> {fullSettings?.workingHours} ساعة</p>
            <p><strong>WhatsApp مفعل:</strong> {fullSettings?.whatsappEnabled ? 'نعم' : 'لا'}</p>
            
            {fullSettings?.whatsappApiUrl && (
              <p><strong>رابط WhatsApp API:</strong> {fullSettings.whatsappApiUrl}</p>
            )}
            
            <div className="mt-3">
              <button
                onClick={async () => {
                  const success = await updateSettings({
                    companyName: fullSettings?.companyName + ' (محدث)',
                  });
                  if (success) {
                    alert('تم التحديث بنجاح!');
                  }
                }}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                مثال على التحديث
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // مثال على استخدام ساعات العمل
  const WorkingHoursExample = () => {
    // Get working hours for today outside of any callback
    const today = new Date();
    const { start, end } = useWorkingHours().getWorkingHoursForDate(today);
    
    return (
      <div className="bg-yellow-50 p-4 rounded-lg mb-4">
        <h3 className="font-bold text-yellow-800 mb-2">خطاف ساعات العمل</h3>
        
        <div className="space-y-2">
          <p><strong>وقت البداية:</strong> {formatTime(startTime)}</p>
          <p><strong>وقت النهاية:</strong> {formatTime(endTime)}</p>
          
          <div>
            <strong>ساعات العمل لليوم:</strong>
            <div className="mt-1 text-sm">
              {`${start.toLocaleTimeString('ar-SA')} - ${end.toLocaleTimeString('ar-SA')}`}
            </div>
          </div>
          
          <div className="mt-3 p-2 bg-yellow-100 rounded">
            <p className="text-sm">
              💡 <strong>نصيحة:</strong> يمكن استخدام هذا الخطاف في أي مكون لفحص أوقات العمل
              دون الحاجة لجلب الإعدادات مباشرة.
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6 text-center">
        أمثلة على استخدام نظام الإعدادات المركزي
      </h1>
      
      <div className="mb-6 p-4 bg-blue-100 rounded-lg">
        <h2 className="font-bold text-blue-800 mb-2">معلومات المستخدم الحالي</h2>
        <p><strong>الاسم:</strong> {session?.user?.name || 'غير مسجل'}</p>
        <p><strong>الدور:</strong> {session?.user?.role || 'غير محدد'}</p>
      </div>

      <PublicSettingsExample />
      <FullSettingsExample />
      <WorkingHoursExample />

      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-bold text-gray-800 mb-2">كيفية الاستخدام في مكوناتك</h3>
        
        <div className="bg-white p-3 rounded border mt-2">
          <pre className="text-sm overflow-x-auto">
{`// للإعدادات العامة
import { usePublicSettings } from '@/providers/settings-provider';

function MyComponent() {
  const { settings, loading } = usePublicSettings();
  
  if (loading) return <div>جاري التحميل...</div>;
  
  return <h1>{settings?.companyName}</h1>;
}

// لساعات العمل
import { useWorkingHours } from '@/hooks/useWorkingHours';

function AttendanceComponent() {
  const { isWorkingTime, startTime, endTime } = useWorkingHours();
  
  return (
    <div>
      <p>ساعات العمل: {startTime} - {endTime}</p>
      <p>الحالة: {isWorkingTime() ? 'وقت عمل' : 'خارج العمل'}</p>
    </div>
  );
}`}
          </pre>
        </div>
      </div>
    </div>
  );
} 