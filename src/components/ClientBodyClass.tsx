"use client";

import { useEffect } from "react";

export default function ClientBodyClass() {
  useEffect(() => {
    // تطبيق classes على body بعد hydration
    document.body.className = "font-tajawal antialiased";

    // فرض تطبيق خط Tajawal
    document.body.style.fontFamily = "'Tajawal', 'Arial', '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif";

    // تطبيق الخط على html أيضاً
    document.documentElement.style.fontFamily = "'Tajawal', 'Arial', 'Tahoma', 'Segoe UI', sans-serif";
  }, []);

  return null;
}
