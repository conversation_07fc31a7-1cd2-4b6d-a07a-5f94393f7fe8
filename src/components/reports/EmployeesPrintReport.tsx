"use client";

import { useEffect, useState } from "react";
import { FaTimes } from "react-icons/fa";

interface Employee {
  id: string;
  employeeNumber: string | null;
  name: string;
  email: string;
  phone: string;
  position: string | null;
  role: string;
  departmentId: string | null;
  managerId: string | null;
  createdAt: string;
  updatedAt: string;
  department: {
    id: string;
    name: string;
  } | null;
  manager: {
    id: string;
    name: string;
  } | null;
  _count: {
    employees: number;
  };
}

interface EmployeesPrintReportProps {
  employees: Employee[];
  onClose: () => void;
}

const roleLabels = {
  ADMIN: "مدير النظام",
  HR: "موارد بشرية",
  MANAGER: "مدير",
  EMPLOYEE: "موظف",
  SECURITY: "أمن",
};

export function EmployeesPrintReport({ employees, onClose }: EmployeesPrintReportProps) {
  const [settings, setSettings] = useState<any>(null);

  useEffect(() => {
    // جلب إعدادات الشركة
    const fetchSettings = async () => {
      try {
        const response = await fetch("/api/settings");
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error("خطأ في جلب الإعدادات:", error);
      }
    };

    fetchSettings();
  }, []);

  const handlePrint = () => {
    window.print();
  };

  const currentDate = new Date().toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }) + ' م';

  // تجميع الموظفين حسب القسم
  const employeesByDepartment = employees.reduce((acc, employee) => {
    const deptName = employee.department?.name || 'بدون قسم';
    if (!acc[deptName]) {
      acc[deptName] = [];
    }
    acc[deptName].push(employee);
    return acc;
  }, {} as Record<string, Employee[]>);

  // إحصائيات
  const totalEmployees = employees.length;
  const roleStats = employees.reduce((acc, emp) => {
    acc[emp.role] = (acc[emp.role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header - لا يُطبع */}
        <div className="flex justify-between items-center p-4 border-b print:hidden">
          <h2 className="text-xl font-bold">تقرير الموظفين</h2>
          <div className="flex gap-2">
            <button
              onClick={handlePrint}
              className="btn-primary"
            >
              طباعة
            </button>
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* محتوى التقرير */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)] print:overflow-visible print:max-h-none">
          {/* رأس التقرير */}
          <div className="text-center mb-8 print:mb-6">
            <div className="flex items-center justify-center gap-4 mb-4">
              {settings?.companyLogo && (
                <img 
                  src={settings.companyLogo} 
                  alt="شعار الشركة" 
                  className="h-16 w-16 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-800 print:text-xl">
                  {settings?.companyName || 'اسم الشركة'}
                </h1>
                <h2 className="text-xl font-semibold text-blue-600 print:text-lg">
                  تقرير الموظفين
                </h2>
              </div>
            </div>
            <p className="text-gray-600 print:text-sm">
              تاريخ التقرير: {currentDate}
            </p>
          </div>

          {/* الإحصائيات */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 print:mb-6 print:grid-cols-4">
            <div className="bg-blue-50 p-4 rounded-lg text-center print:border print:bg-transparent">
              <div className="text-2xl font-bold text-blue-600 print:text-lg">{totalEmployees}</div>
              <div className="text-sm text-gray-600">إجمالي الموظفين</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg text-center print:border print:bg-transparent">
              <div className="text-2xl font-bold text-green-600 print:text-lg">{Object.keys(employeesByDepartment).length}</div>
              <div className="text-sm text-gray-600">عدد الأقسام</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg text-center print:border print:bg-transparent">
              <div className="text-2xl font-bold text-purple-600 print:text-lg">{roleStats.MANAGER || 0}</div>
              <div className="text-sm text-gray-600">المديرين</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg text-center print:border print:bg-transparent">
              <div className="text-2xl font-bold text-orange-600 print:text-lg">{roleStats.EMPLOYEE || 0}</div>
              <div className="text-sm text-gray-600">الموظفين</div>
            </div>
          </div>

          {/* جدول الموظفين مجمعين حسب القسم */}
          {Object.entries(employeesByDepartment).map(([deptName, deptEmployees]) => (
            <div key={deptName} className="mb-8 print:mb-6 print:break-inside-avoid">
              <h3 className="text-lg font-bold text-gray-800 mb-4 print:text-base bg-gray-100 p-2 rounded print:bg-transparent print:border-b">
                {deptName} ({deptEmployees.length} موظف)
              </h3>
              
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300 print:text-sm">
                  <thead>
                    <tr className="bg-gray-50 print:bg-transparent">
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">#</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">الاسم</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">رقم الموظف</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">المنصب</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">الدور</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">رقم الهاتف</th>
                      <th className="border border-gray-300 px-3 py-2 text-right font-semibold">البريد الإلكتروني</th>
                    </tr>
                  </thead>
                  <tbody>
                    {deptEmployees.map((employee, index) => (
                      <tr key={employee.id} className="hover:bg-gray-50 print:hover:bg-transparent">
                        <td className="border border-gray-300 px-3 py-2">{index + 1}</td>
                        <td className="border border-gray-300 px-3 py-2 font-medium">{employee.name}</td>
                        <td className="border border-gray-300 px-3 py-2">{employee.employeeNumber || '-'}</td>
                        <td className="border border-gray-300 px-3 py-2">{employee.position || '-'}</td>
                        <td className="border border-gray-300 px-3 py-2">{roleLabels[employee.role as keyof typeof roleLabels]}</td>
                        <td className="border border-gray-300 px-3 py-2">{employee.phone}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{employee.email}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}

          {/* تذييل التقرير */}
          <div className="mt-8 pt-4 border-t border-gray-300 text-center text-sm text-gray-600 print:mt-6">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الحضور</p>
                          <p>تاريخ الطباعة: {new Date().toLocaleDateString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })} م</p>
          </div>
        </div>
      </div>

      {/* أنماط الطباعة */}
      <style jsx>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print\\:block {
            display: block !important;
          }
          .print\\:hidden {
            display: none !important;
          }
          .fixed {
            position: static !important;
          }
          .bg-black {
            background: transparent !important;
          }
          .bg-white {
            background: white !important;
          }
          .shadow-xl {
            box-shadow: none !important;
          }
          .rounded-lg {
            border-radius: 0 !important;
          }
          .max-w-4xl {
            max-width: none !important;
          }
          .max-h-\\[90vh\\] {
            max-height: none !important;
          }
          .overflow-hidden {
            overflow: visible !important;
          }
          .p-4, .p-6 {
            padding: 0 !important;
          }
          table {
            page-break-inside: auto;
          }
          tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }
          thead {
            display: table-header-group;
          }
          tfoot {
            display: table-footer-group;
          }
        }
      `}</style>
    </div>
  );
}
