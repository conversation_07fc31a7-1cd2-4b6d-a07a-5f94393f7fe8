"use client";

import { useState } from "react";
import { signOut } from "next-auth/react";
import { FaBars, FaUserCircle, FaSignOutAlt, FaUser } from "react-icons/fa";
import { format } from "date-fns";

interface HeaderProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  user: any;
  hideSidebarToggle?: boolean;
}

export default function Header({ sidebarOpen, setSidebarOpen, user, hideSidebarToggle = false }: HeaderProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const today = format(new Date(), "dd/MM/yyyy");

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      setDropdownOpen(false);

      // تسجيل الخروج السريع باستخدام API مخصص أولاً
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } catch (logoutError) {
        console.log("تم تجاهل خطأ logout API:", logoutError);
      }

      // NextAuth signOut بدون redirect
      signOut({ 
        redirect: false,
        callbackUrl: "/login" 
      }).finally(() => {
        // إعادة التوجيه الفورية
        window.location.replace("/login");
      });
      
    } catch (error) {
      console.error("خطأ في تسجيل الخروج:", error);
      // في حالة الخطأ، إعادة التوجيه مباشرة
      window.location.replace("/login");
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <header className="bg-white shadow-sm h-16 flex items-center justify-between px-6">
      <div className="flex items-center">
        {!hideSidebarToggle && (
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <FaBars size={20} />
          </button>
        )}

        <div className="mr-4 text-gray-600">
          <span>{today}</span>
        </div>
      </div>

      <div className="relative">
        <button
          onClick={() => setDropdownOpen(!dropdownOpen)}
          className="flex items-center focus:outline-none"
        >
          <FaUserCircle size={24} className="text-gray-600" />
          <span className="mr-2 text-gray-700">{user?.name || "المستخدم"}</span>
        </button>

        {dropdownOpen && (
          <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
            <a
              href="/dashboard/profile"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            >
              <FaUser className="ml-2" />
              الملف الشخصي
            </a>
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center disabled:opacity-50"
            >
              <FaSignOutAlt className="ml-2" />
              {isLoggingOut ? "جاري تسجيل الخروج..." : "تسجيل الخروج"}
            </button>
          </div>
        )}
      </div>
    </header>
  );
}
