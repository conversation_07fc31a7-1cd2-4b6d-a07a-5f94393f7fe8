"use client";

import { useState, useEffect } from "react";

// بيانات وهمية للمخطط
const generateWeeklyData = () => {
  const days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
  return days.map(day => ({
    day,
    present: Math.floor(Math.random() * 50) + 30,
    absent: Math.floor(Math.random() * 15) + 5,
    late: Math.floor(Math.random() * 10) + 2
  }));
};

export default function AttendanceChart() {
  const [data, setData] = useState(generateWeeklyData());
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const maxValue = Math.max(...data.map(d => d.present + d.absent + d.late));

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900">إحصائيات الحضور</h2>
            <p className="text-sm text-gray-600 mt-1">نظرة عامة على حضور الموظفين</p>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => setSelectedPeriod('week')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedPeriod === 'week'
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              أسبوعي
            </button>
            <button
              onClick={() => setSelectedPeriod('month')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedPeriod === 'month'
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              شهري
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* المؤشرات */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">حاضر</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-sm text-gray-600">غائب</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span className="text-sm text-gray-600">متأخر</span>
          </div>
        </div>

        {/* المخطط */}
        <div className="space-y-4">
          {data.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">{item.day}</span>
                <span className="text-xs text-gray-500">
                  {item.present + item.absent + item.late} إجمالي
                </span>
              </div>
              
              <div className="relative h-6 bg-gray-100 rounded-full overflow-hidden">
                <div className="absolute inset-0 flex">
                  {/* حاضر */}
                  <div
                    className="bg-green-500 transition-all duration-500"
                    style={{ width: `${(item.present / maxValue) * 100}%` }}
                  />
                  {/* متأخر */}
                  <div
                    className="bg-yellow-500 transition-all duration-500"
                    style={{ width: `${(item.late / maxValue) * 100}%` }}
                  />
                  {/* غائب */}
                  <div
                    className="bg-red-500 transition-all duration-500"
                    style={{ width: `${(item.absent / maxValue) * 100}%` }}
                  />
                </div>
              </div>
              
              <div className="flex justify-between text-xs text-gray-500">
                <span>حاضر: {item.present}</span>
                <span>متأخر: {item.late}</span>
                <span>غائب: {item.absent}</span>
              </div>
            </div>
          ))}
        </div>

        {/* ملخص */}
        <div className="mt-6 pt-6 border-t border-gray-100">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {data.reduce((sum, item) => sum + item.present, 0)}
              </div>
              <div className="text-xs text-gray-500">إجمالي الحضور</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {data.reduce((sum, item) => sum + item.late, 0)}
              </div>
              <div className="text-xs text-gray-500">إجمالي التأخير</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {data.reduce((sum, item) => sum + item.absent, 0)}
              </div>
              <div className="text-xs text-gray-500">إجمالي الغياب</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
