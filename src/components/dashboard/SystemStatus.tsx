"use client";

import { useState, useEffect } from "react";
import { 
  FaDatabase, 
  FaServer, 
  FaWifi, 
  FaWhatsapp,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimesCircle,
  FaCog
} from "react-icons/fa";

interface SystemService {
  name: string;
  status: 'online' | 'warning' | 'offline';
  description: string;
  icon: React.ReactNode;
  lastCheck: string;
  uptime?: string;
}

export default function SystemStatus() {
  const [services, setServices] = useState<SystemService[]>([
    {
      name: 'قاعدة البيانات',
      status: 'online',
      description: 'MySQL متصل وجاهز',
      icon: <FaDatabase size={16} />,
      lastCheck: 'منذ دقيقة',
      uptime: '99.9%'
    },
    {
      name: 'الخادم',
      status: 'online',
      description: 'الخادم يعمل بشكل طبيعي',
      icon: <FaServer size={16} />,
      lastCheck: 'منذ دقيقة',
      uptime: '99.8%'
    },
    {
      name: 'الاتصال',
      status: 'online',
      description: 'الاتصال بالإنترنت مستقر',
      icon: <FaWifi size={16} />,
      lastCheck: 'منذ 30 ثانية',
      uptime: '99.5%'
    },
    {
      name: 'WhatsApp API',
      status: 'warning',
      description: 'يحتاج إعداد المفاتيح',
      icon: <FaWhatsapp size={16} />,
      lastCheck: 'منذ 5 دقائق',
      uptime: '85.2%'
    }
  ]);

  const [systemHealth, setSystemHealth] = useState({
    overall: 'good',
    cpu: 45,
    memory: 62,
    disk: 38
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <FaCheckCircle className="text-green-500" size={16} />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" size={16} />;
      case 'offline':
        return <FaTimesCircle className="text-red-500" size={16} />;
      default:
        return <FaCog className="text-gray-500" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-700';
      case 'warning':
        return 'bg-yellow-100 text-yellow-700';
      case 'offline':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getProgressColor = (value: number) => {
    if (value < 50) return 'bg-green-500';
    if (value < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <div className="p-6 border-b border-gray-100">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-gray-900">حالة النظام</h2>
            <p className="text-sm text-gray-600 mt-1">مراقبة أداء النظام والخدمات</p>
          </div>
          
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            systemHealth.overall === 'good' 
              ? 'bg-green-100 text-green-700'
              : systemHealth.overall === 'warning'
              ? 'bg-yellow-100 text-yellow-700'
              : 'bg-red-100 text-red-700'
          }`}>
            {systemHealth.overall === 'good' ? 'ممتاز' : 
             systemHealth.overall === 'warning' ? 'تحذير' : 'خطأ'}
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* حالة الخدمات */}
        <div className="space-y-4 mb-6">
          <h3 className="text-sm font-medium text-gray-700">الخدمات</h3>
          {services.map((service, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
              <div className="flex items-center gap-3">
                <div className="text-gray-600">
                  {service.icon}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900">
                      {service.name}
                    </span>
                    {getStatusIcon(service.status)}
                  </div>
                  <p className="text-xs text-gray-500">
                    {service.description}
                  </p>
                </div>
              </div>
              
              <div className="text-left">
                <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(service.status)}`}>
                  {service.status === 'online' ? 'متصل' :
                   service.status === 'warning' ? 'تحذير' : 'منقطع'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {service.lastCheck}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* أداء النظام */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-700">أداء النظام</h3>
          
          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-600">المعالج (CPU)</span>
                <span className="text-xs font-medium text-gray-900">{systemHealth.cpu}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(systemHealth.cpu)}`}
                  style={{ width: `${systemHealth.cpu}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-600">الذاكرة (RAM)</span>
                <span className="text-xs font-medium text-gray-900">{systemHealth.memory}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(systemHealth.memory)}`}
                  style={{ width: `${systemHealth.memory}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-600">التخزين</span>
                <span className="text-xs font-medium text-gray-900">{systemHealth.disk}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(systemHealth.disk)}`}
                  style={{ width: `${systemHealth.disk}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-4 border-t border-gray-100">
        <button className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors">
          عرض تفاصيل أكثر
        </button>
      </div>
    </div>
  );
}
