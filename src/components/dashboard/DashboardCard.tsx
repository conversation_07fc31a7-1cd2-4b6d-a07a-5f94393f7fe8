import Link from "next/link";
import { ReactNode } from "react";
import { FaArrowUp, FaArrowDown } from "react-icons/fa";

interface DashboardCardProps {
  title: string;
  value: string;
  icon: ReactNode;
  link: string;
  color?: string;
  subtitle?: string;
  trend?: string;
}

export default function DashboardCard({
  title,
  value,
  icon,
  link,
  color = "blue",
  subtitle,
  trend
}: DashboardCardProps) {
  const colorClasses = {
    blue: "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",
    green: "from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",
    purple: "from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",
    orange: "from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700",
    indigo: "from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700",
    red: "from-red-500 to-red-600 hover:from-red-600 hover:to-red-700",
    teal: "from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700",
  };

  const isPositiveTrend = trend && trend.startsWith('+');

  return (
    <Link href={link}>
      <div className="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
        {/* خلفية متدرجة */}
        <div className={`absolute inset-0 bg-gradient-to-br ${colorClasses[color as keyof typeof colorClasses]} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />

        <div className="relative p-6">
          {/* الرأس */}
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-lg bg-gradient-to-br ${colorClasses[color as keyof typeof colorClasses]} shadow-lg`}>
              <div className="text-white">
                {icon}
              </div>
            </div>

            {trend && (
              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                isPositiveTrend
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                {isPositiveTrend ? <FaArrowUp size={10} /> : <FaArrowDown size={10} />}
                {trend}
              </div>
            )}
          </div>

          {/* المحتوى */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors">
              {title}
            </h3>

            <div className="flex items-baseline gap-2">
              <span className="text-3xl font-bold text-gray-900 group-hover:text-gray-800 transition-colors">
                {value}
              </span>
              {subtitle && (
                <span className="text-sm text-gray-500">
                  {subtitle}
                </span>
              )}
            </div>
          </div>

          {/* مؤشر التفاعل */}
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gray-200 to-transparent group-hover:via-gray-300 transition-colors duration-300" />
        </div>
      </div>
    </Link>
  );
}
