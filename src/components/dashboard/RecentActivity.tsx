"use client";

import { useState, useEffect } from "react";
import { 
  FaUserPlus, 
  FaCalendarCheck, 
  FaUserClock, 
  FaUserFriends,
  FaClock,
  FaSignInAlt,
  FaSignOutAlt
} from "react-icons/fa";

// بيانات وهمية للنشاط الأخير
const generateRecentActivity = () => {
  const activities = [
    {
      type: 'checkin',
      user: 'أحمد محمد',
      action: 'تسجيل دخول',
      time: '8:30 ص',
      icon: <FaSignInAlt className="text-green-500" size={16} />,
      color: 'green'
    },
    {
      type: 'checkout',
      user: 'فاطمة علي',
      action: 'تسجيل خروج',
      time: '5:15 م',
      icon: <FaSignOutAlt className="text-blue-500" size={16} />,
      color: 'blue'
    },
    {
      type: 'leave',
      user: 'محمد سالم',
      action: 'طلب إجازة',
      time: '2:45 م',
      icon: <FaUserClock className="text-orange-500" size={16} />,
      color: 'orange'
    },
    {
      type: 'visitor',
      user: 'زائر جديد',
      action: 'تسجيل زيارة',
      time: '1:20 م',
      icon: <FaUserFriends className="text-purple-500" size={16} />,
      color: 'purple'
    },
    {
      type: 'overtime',
      user: 'سارة أحمد',
      action: 'عمل إضافي',
      time: '12:30 م',
      icon: <FaClock className="text-red-500" size={16} />,
      color: 'red'
    },
    {
      type: 'employee',
      user: 'موظف جديد',
      action: 'إضافة موظف',
      time: '11:15 ص',
      icon: <FaUserPlus className="text-indigo-500" size={16} />,
      color: 'indigo'
    }
  ];

  return activities.map((activity, index) => ({
    ...activity,
    id: index + 1,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
  }));
};

export default function RecentActivity() {
  const [activities, setActivities] = useState(generateRecentActivity());
  const [filter, setFilter] = useState('all');

  const filteredActivities = activities.filter(activity => 
    filter === 'all' || activity.type === filter
  );

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `منذ ${hours} ساعة`;
    } else if (minutes > 0) {
      return `منذ ${minutes} دقيقة`;
    } else {
      return 'الآن';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900">النشاط الأخير</h2>
            <p className="text-sm text-gray-600 mt-1">آخر الأنشطة في النظام</p>
          </div>
          
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 text-xs border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">جميع الأنشطة</option>
            <option value="checkin">تسجيل دخول</option>
            <option value="checkout">تسجيل خروج</option>
            <option value="leave">الإجازات</option>
            <option value="visitor">الزوار</option>
            <option value="overtime">العمل الإضافي</option>
          </select>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {filteredActivities.map((activity) => (
            <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div className={`p-2 rounded-full bg-${activity.color}-100`}>
                {activity.icon}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900">
                    {activity.user}
                  </span>
                  <span className="text-sm text-gray-600">
                    {activity.action}
                  </span>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-500">
                    {activity.time}
                  </span>
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-500">
                    {getTimeAgo(activity.timestamp)}
                  </span>
                </div>
              </div>
              
              <div className="text-gray-400">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          ))}
        </div>

        {filteredActivities.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <FaCalendarCheck size={48} className="mx-auto" />
            </div>
            <p className="text-gray-500">لا توجد أنشطة حديثة</p>
          </div>
        )}
      </div>

      <div className="px-6 py-4 border-t border-gray-100">
        <button className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors">
          عرض جميع الأنشطة
        </button>
      </div>
    </div>
  );
}
