"use client";

import Link from "next/link";
import { 
  FaUserPlus, 
  FaCalendarPlus, 
  FaFileAlt, 
  FaCog,
  FaUserFriends,
  FaClock
} from "react-icons/fa";

const quickActions = [
  {
    title: "إضافة موظف",
    description: "إضافة موظف جديد للنظام",
    icon: <FaUserPlus className="text-blue-500" size={20} />,
    link: "/dashboard/employees/new",
    color: "blue"
  },
  {
    title: "تسجيل إجازة",
    description: "طلب إجازة جديدة",
    icon: <FaCalendarPlus className="text-green-500" size={20} />,
    link: "/dashboard/leaves/new",
    color: "green"
  },
  {
    title: "تسجيل زائر",
    description: "إضافة زائر جديد",
    icon: <FaUserFriends className="text-purple-500" size={20} />,
    link: "/dashboard/visitors/new",
    color: "purple"
  },
  {
    title: "العمل الإضافي",
    description: "تسجيل ساعات إضافية",
    icon: <FaClock className="text-orange-500" size={20} />,
    link: "/dashboard/overtime/new",
    color: "orange"
  },
  {
    title: "إنشاء تقرير",
    description: "إنشاء تقرير جديد",
    icon: <FaFileAlt className="text-indigo-500" size={20} />,
    link: "/dashboard/reports/new",
    color: "indigo"
  },
  {
    title: "الإعدادات",
    description: "إعدادات النظام",
    icon: <FaCog className="text-gray-500" size={20} />,
    link: "/dashboard/settings",
    color: "gray"
  }
];

export default function QuickActions() {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <div className="p-6 border-b border-gray-100">
        <h2 className="text-xl font-bold text-gray-900">الإجراءات السريعة</h2>
        <p className="text-sm text-gray-600 mt-1">الوصول السريع للمهام الشائعة</p>
      </div>
      
      <div className="p-6">
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.link}>
              <div className="group flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                <div className={`p-2 rounded-lg bg-${action.color}-100 group-hover:bg-${action.color}-200 transition-colors`}>
                  {action.icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                    {action.title}
                  </h3>
                  <p className="text-xs text-gray-500 truncate">
                    {action.description}
                  </p>
                </div>
                
                <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
