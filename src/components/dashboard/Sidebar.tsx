"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  FaHome,
  FaUsersCog,
  FaCalendarCheck,
  FaUser<PERSON>lock,
  FaUserTie,
  FaUserFriends,
  FaMoon,
  FaCog,
  FaUserShield,
  FaCalendarAlt,
  FaShieldAlt
} from "react-icons/fa";
import Logo from "@/components/Logo";

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export default function Sidebar({ isOpen, setIsOpen }: SidebarProps) {
  const pathname = usePathname();
  const { data: session } = useSession();
  const role = session?.user?.role;

  const isAdmin = role === "ADMIN";
  const isHR = role === "HR" || isAdmin;
  const isManager = role === "MANAGER" || isHR;
  const isSecurity = role === "SECURITY" || isAdmin;

  const menuItems = [
    {
      title: "لوحة التحكم",
      icon: <FaHome size={20} />,
      href: "/dashboard",
      show: true,
    },
    // قائمة خاصة بموظف الأمن
    {
      title: "لوحة تحكم الأمن",
      icon: <FaShieldAlt size={20} />,
      href: "/dashboard/security",
      show: role === "SECURITY",
    },
    {
      title: "الموظفين",
      icon: <FaUsersCog size={20} />,
      href: "/dashboard/employees",
      show: isHR,
    },
    {
      title: "الأقسام",
      icon: <FaUserTie size={20} />,
      href: "/dashboard/departments",
      show: isHR,
    },
    {
      title: "الحضور والانصراف",
      icon: <FaCalendarCheck size={20} />,
      href: "/dashboard/attendance",
      show: isAdmin || isHR || role === "MANAGER" || role === "EMPLOYEE",
    },
    {
      title: "الإجازات",
      icon: <FaUserClock size={20} />,
      href: "/dashboard/leaves",
      show: !isSecurity || isAdmin, // إخفاء عن موظف الأمن العادي
    },
    {
      title: "الزوار",
      icon: <FaUserFriends size={20} />,
      href: "/dashboard/visitors",
      show: !isSecurity || isAdmin, // إخفاء عن موظف الأمن العادي
    },
    {
      title: "العمل بعد الدوام",
      icon: <FaMoon size={20} />,
      href: "/dashboard/after-hours",
      show: !isSecurity || isAdmin, // إخفاء عن موظف الأمن العادي
    },
    {
      title: "الإجازات الرسمية",
      icon: <FaCalendarAlt size={20} />,
      href: "/dashboard/official-holidays",
      show: !isSecurity || isAdmin, // إخفاء عن موظف الأمن العادي
    },
    {
      title: "الإعدادات",
      icon: <FaCog size={20} />,
      href: "/dashboard/settings",
      show: isAdmin,
    },
    {
      title: "المستخدمين والصلاحيات",
      icon: <FaUserShield size={20} />,
      href: "/dashboard/users",
      show: isAdmin,
    },
  ];

  return (
    <aside
      className={`fixed top-0 right-0 h-full sidebar-bg text-white transition-all duration-300 z-10 ${
        isOpen ? "w-64" : "w-20"
      }`}
    >
      <div className="p-4 flex justify-center items-center h-16 border-b border-blue-700">
        {isOpen ? (
          <Logo size="md" showText={true} className="text-white" />
        ) : (
          <Logo size="sm" showText={false} />
        )}
      </div>

      <nav className="mt-6">
        <ul>
          {menuItems
            .filter((item) => item.show)
            .map((item, index) => (
              <li key={index}>
                <Link
                  href={item.href}
                  className={`flex items-center py-3 px-4 hover:bg-blue-700 transition-colors ${
                    pathname === item.href ? "bg-blue-700" : ""
                  } ${isOpen ? "justify-start" : "justify-center"}`}
                >
                  <span className="mr-2">{item.icon}</span>
                  {isOpen && <span>{item.title}</span>}
                </Link>
              </li>
            ))}
        </ul>
      </nav>
    </aside>
  );
}
