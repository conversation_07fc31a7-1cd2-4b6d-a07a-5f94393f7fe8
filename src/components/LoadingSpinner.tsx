"use client";

import { useEffect, useState } from "react";

interface LoadingSpinnerProps {
  message?: string;
  fullScreen?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function LoadingSpinner({ 
  message = "جاري التحميل...", 
  fullScreen = false,
  size = 'md'
}: LoadingSpinnerProps) {
  const [showMessage, setShowMessage] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowMessage(true);
    }, 500); // عرض الرسالة بعد 500ms

    return () => clearTimeout(timer);
  }, []);

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16'
  };

  const containerClasses = fullScreen 
    ? 'fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm'
    : 'flex items-center justify-center p-4';

  return (
    <div className={containerClasses}>
      <div className="text-center">
        {/* Spinner محسن */}
        <div className={`mx-auto ${sizeClasses[size]} relative`}>
          {/* الدائرة الخارجية */}
          <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
          {/* الدائرة المتحركة */}
          <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>
          {/* النقطة المركزية */}
          <div className="absolute inset-2 bg-blue-100 rounded-full opacity-20 animate-pulse"></div>
        </div>
        
        {/* الرسالة */}
        {showMessage && (
          <div className="mt-4 space-y-2">
            <p className="text-gray-600 text-sm font-medium animate-fade-in">
              {message}
            </p>
            {/* شريط التقدم */}
            <div className="w-32 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
              <div className="h-full bg-blue-600 rounded-full animate-progress"></div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes progress {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
        
        .animate-progress {
          animation: progress 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
}

// Component للتحميل الأولي للتطبيق
export function InitialLoader() {
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState("تهيئة التطبيق...");

  useEffect(() => {
    const messages = [
      "تهيئة التطبيق...",
      "تحميل الإعدادات...",
      "تحضير الواجهة...",
      "جاري الانتهاء..."
    ];

    let currentIndex = 0;
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + 25, 100);
        
        if (currentIndex < messages.length) {
          setMessage(messages[currentIndex]);
          currentIndex++;
        }
        
        if (newProgress >= 100) {
          clearInterval(interval);
        }
        
        return newProgress;
      });
    }, 800);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center space-y-6 p-8">
        {/* شعار أو أيقونة */}
        <div className="w-20 h-20 mx-auto bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
          <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
        
        {/* الرسالة */}
        <div className="space-y-3">
          <h2 className="text-2xl font-bold text-gray-800">نظام الموارد البشرية</h2>
          <p className="text-gray-600 text-lg">{message}</p>
        </div>
        
        {/* شريط التقدم */}
        <div className="w-64 mx-auto">
          <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>التقدم</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-500 ease-out shadow-sm"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
} 