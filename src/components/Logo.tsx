"use client";

import { usePublicSettings } from "@/providers/settings-provider";
import Image from "next/image";

interface LogoProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function Logo({ className = '', showText = true, size = 'md' }: LogoProps) {
  const { settings, loading } = usePublicSettings();

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl'
  };

  if (loading) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        <div className={`${sizeClasses[size]} bg-gray-200 animate-pulse rounded`}></div>
        {showText && (
          <div className={`h-6 bg-gray-200 animate-pulse rounded w-32`}></div>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {settings?.logo ? (
        <Image
          src={settings.logo}
          alt={settings.companyName || 'شعار النظام'}
          width={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
          height={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
          className={`${sizeClasses[size]} object-contain`}
        />
      ) : (
        <div className={`${sizeClasses[size]} bg-blue-600 rounded flex items-center justify-center text-white font-bold`}>
          {settings?.companyName?.charAt(0) || 'ن'}
        </div>
      )}
      
      {showText && (
        <span className={`font-bold text-white ${textSizeClasses[size]}`}>
          {settings?.companyName || 'النظام الذكي للحضور والانصراف'}
        </span>
      )}
    </div>
  );
}
