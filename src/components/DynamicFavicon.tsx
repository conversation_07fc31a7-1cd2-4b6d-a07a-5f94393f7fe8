"use client";

import { useEffect, useState } from 'react';
import { usePublicSettings } from '@/providers/settings-provider';

export default function DynamicFavicon() {
  const [mounted, setMounted] = useState(false);
  const [faviconApplied, setFaviconApplied] = useState(false);
  const { settings, loading } = usePublicSettings();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const applyFavicon = () => {
      try {
        // Set a default favicon immediately if not already applied
        if (!faviconApplied) {
          updateFavicon('/favicon.ico');
          setFaviconApplied(true);
        }

        // Apply custom favicon from settings if available
        if (!loading && settings?.favicon && settings.favicon !== '/favicon.ico') {
          updateFavicon(settings.favicon);
        }
      } catch (error) {
        console.error('Error applying favicon:', error);
        // Ensure default favicon is applied even on error
        if (!faviconApplied) {
          updateFavicon('/favicon.ico');
          setFaviconApplied(true);
        }
      }
    };

    applyFavicon();
  }, [mounted, loading, settings, faviconApplied]);

  const updateFavicon = (href: string) => {
    try {
      const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = href;
      
      if (!document.querySelector("link[rel*='icon']")) {
        document.getElementsByTagName('head')[0].appendChild(link);
      }
    } catch (error) {
      console.error('Error updating favicon:', error);
    }
  };

  return null; // This component doesn't render anything
}
