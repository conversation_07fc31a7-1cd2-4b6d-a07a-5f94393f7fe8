import bcrypt from "bcryptjs";
import { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { prisma } from "@/lib/prisma";

export const authOptions: AuthOptions = {
  providers: [
    // تسجيل الدخول بكلمة المرور
    CredentialsProvider({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "البريد الإلكتروني", type: "email" },
        password: { label: "كلمة المرور", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("يرجى إدخال البريد الإلكتروني وكلمة المرور");
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
        });

        if (!user || !user?.password) {
          throw new Error("البريد الإلكتروني غير مسجل");
        }

        const isCorrectPassword = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isCorrectPassword) {
          throw new Error("كلمة المرور غير صحيحة");
        }

        return user;
      },
    }),
    // تسجيل الدخول بـ OTP
    CredentialsProvider({
      id: "otp",
      name: "otp",
      credentials: {
        phone: { label: "رقم الهاتف", type: "text" },
        code: { label: "رمز التحقق", type: "text" },
      },
      async authorize(credentials) {

        
        if (!credentials?.phone || !credentials?.code) {
          throw new Error("يرجى إدخال رقم الهاتف ورمز التحقق");
        }

        try {
          // التحقق من OTP مباشرة بدلاً من fetch
          const { phone, code } = credentials;
          
          // تنسيق رقم الهاتف
          function formatPhoneNumber(phone: string): { formatted: string; isValid: boolean } {
            const cleanPhone = phone.replace(/[^\d]/g, '');
            let formattedPhone = cleanPhone;
            
            if (phone.startsWith('+')) {
              formattedPhone = cleanPhone;
            }
            
            if (!formattedPhone.startsWith('968') && formattedPhone.startsWith('0')) {
              formattedPhone = '968' + formattedPhone.slice(1);
            } else if (!formattedPhone.startsWith('968') && formattedPhone.length === 8) {
              formattedPhone = '968' + formattedPhone;
            }
            
            const isValid = formattedPhone.startsWith('968') && formattedPhone.length === 11;
            return { formatted: formattedPhone, isValid };
          }
          
          const { formatted: formattedPhone, isValid } = formatPhoneNumber(phone);
          
          if (!isValid) {
            throw new Error("رقم الهاتف غير صالح");
          }
          

          
          // البحث عن آخر رمز تحقق غير مستخدم للرقم
          const otp = await prisma.oTP.findFirst({
            where: {
              phone: formattedPhone,
              code: code,
              used: false,
              expiresAt: {
                gt: new Date() // لم تنته صلاحيته بعد
              }
            },
            orderBy: {
              createdAt: 'desc' // أحدث OTP
            }
          });
          

          
          if (!otp) {
            throw new Error("رمز التحقق غير صالح أو منتهي الصلاحية");
          }
          
          // تحديث حالة الرمز ليصبح مستخدماً
          await prisma.oTP.update({
            where: {
              id: otp.id
            },
            data: {
              used: true
            }
          });
          

          
          // البحث عن المستخدم برقم الهاتف (البحث في آخر 8 أرقام)
          const searchPhone = formattedPhone.slice(-8);
          const user = await prisma.user.findFirst({
            where: {
              phone: {
                contains: searchPhone
              }
            },
            include: {
              department: true
            }
          });
          

          
          if (!user) {
            throw new Error("المستخدم غير موجود");
          }
          
          // إرجاع بيانات المستخدم للمصادقة
          const userData = {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            phone: user.phone,
            employeeNumber: user.employeeNumber,
            position: user.position,
            department: user.department ? {
              id: user.department.id,
              name: user.department.name
            } : null
          };
          

          return userData;
          
        } catch (error) {
          throw new Error(error instanceof Error ? error.message : "حدث خطأ في التحقق من الرمز");
        }
      }
    }),
  ],
  pages: {
    signIn: "/login",
    signOut: "/login", // إعادة التوجيه إلى صفحة تسجيل الدخول عند الخروج
  },
  debug: process.env.NODE_ENV === "development",
  session: {
    strategy: "jwt",
    maxAge: 4 * 60 * 60, // مدة الجلسة 4 ساعات (بدلاً من 30 يوم)
    updateAge: 60 * 60, // تحديث الجلسة كل ساعة (بدلاً من 24 ساعة)
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        // إضافة timestamp للجلسة الجديدة
        return {
          ...token,
          id: user.id,
          role: user.role,
          lastActivity: Date.now(), // تتبع آخر نشاط
        };
      }
      
      // تحديث آخر نشاط في كل استدعاء
      return {
        ...token,
        lastActivity: Date.now(),
      };
    },
    async session({ session, token }) {
      // التحقق من انتهاء صلاحية الجلسة (4 ساعات)
      const now = Date.now();
      const lastActivity = token.lastActivity as number || 0;
      const maxInactivity = 4 * 60 * 60 * 1000; // 4 ساعات بالميلي ثانية
      
      if (now - lastActivity > maxInactivity) {
        // الجلسة منتهية الصلاحية
        throw new Error("انتهت صلاحية الجلسة");
      }
      
      return {
        ...session,
        user: {
          ...session.user,
          id: token.id,
          role: token.role,
        },
        lastActivity: token.lastActivity,
      };
    },
  },
  events: {
    async signOut(message) {
      // تسجيل عملية تسجيل الخروج
      console.log("تم تسجيل الخروج:", message);
    },
    async session(message) {
      // تسجيل نشاط الجلسة (للمراقبة)
      if (process.env.NODE_ENV === "development") {
        console.log("نشاط الجلسة:", message.session?.user?.name);
      }
    },
  },
}; 