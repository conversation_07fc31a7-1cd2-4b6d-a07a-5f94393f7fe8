import axios from 'axios';
import { prisma } from '@/lib/prisma';

interface WhatsAppMessage {
  to: string;
  message: string;
}

interface CloudTextResponse {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  details?: any;
}

class WhatsAppService {
  private baseUrl = 'https://w.gcccons.org/api';
  private apiSecret: string | null = null;
  private accountKey: string | null = null;
  private isEnabled = false;

  constructor() {
    this.loadSettings();
  }

  // تحميل الإعدادات من قاعدة البيانات
  private async loadSettings() {
    try {
      const settings = await prisma.settings.findFirst();
      if (settings) {
        // استخدام الرابط كما هو من الإعدادات (يجب أن يكون مثل: https://w.gcccons.org/api)
        let apiUrl = settings.whatsappApiUrl || 'https://w.gcccons.org/api';

        // التأكد من عدم انتهاء الرابط بـ /
        apiUrl = apiUrl.replace(/\/$/, '');

        this.baseUrl = apiUrl;
        this.apiSecret = settings.whatsappApiSecret;
        this.accountKey = settings.whatsappAccountKey;
        this.isEnabled = settings.whatsappEnabled;

        // Debug: WhatsApp settings loaded
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات WhatsApp:', error);
    }
  }

  // التحقق من الرصيد
  async getCredits(): Promise<CloudTextResponse> {
    try {
      await this.loadSettings();

      if (!this.apiSecret) {
        return {
          success: false,
          error: 'المفتاح السري غير محدد'
        };
      }

      // Debug: Fetching CloudText credits

      // استخدام GET مع المعاملات في params حسب whatsapp.txt
      const response = await axios.get(`${this.baseUrl}/get/credits`, {
        params: {
          secret: this.apiSecret
        },
        timeout: 10000,
        headers: {
          'Accept': 'application/json'
        }
      });

      // Debug: CloudText credits response

      return {
        success: true,
        data: response.data,
        message: 'تم جلب الرصيد بنجاح'
      };
    } catch (error: any) {
      console.error('خطأ في جلب الرصيد:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        url: error.config?.url
      });

      let errorMessage = 'حدث خطأ في جلب الرصيد';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'انتهت مهلة الاتصال';
      } else if (error.response?.status === 401) {
        errorMessage = 'خطأ في المصادقة - تحقق من المفتاح السري';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return {
        success: false,
        error: errorMessage,
        details: error.response?.data
      };
    }
  }

  // تنسيق رقم الهاتف العماني (مثل Python)
  private formatPhoneNumber(phoneNumber: string): string {
    // Debug: Original phone number

    // تنظيف رقم الهاتف (إزالة الرموز والمسافات)
    let cleanPhone = phoneNumber.replace(/[^\d]/g, '');

    // إزالة الرمز + إذا كان موجوداً في البداية (مثل Python)
    if (phoneNumber.startsWith('+')) {
      cleanPhone = phoneNumber.slice(1).replace(/[^\d]/g, '');
    }

    // التحقق من أن الرقم يبدأ برمز الدولة (968 للعمان)
    if (!cleanPhone.startsWith('968') && cleanPhone.startsWith('0')) {
      cleanPhone = '968' + cleanPhone.slice(1);
    } else if (!cleanPhone.startsWith('968') && cleanPhone.length === 8) {
      cleanPhone = '968' + cleanPhone;
    }

    // Debug: Formatted phone number

    return cleanPhone;
  }

  // إرسال رسالة WhatsApp (محسن مثل Python)
  async sendMessage({ to, message }: WhatsAppMessage): Promise<CloudTextResponse> {
    try {
      // تحميل الإعدادات أولاً
      await this.loadSettings();

      if (!this.isEnabled) {
        // Debug: WhatsApp service not enabled
        return {
          success: false,
          error: 'خدمة WhatsApp غير مفعلة'
        };
      }

      if (!this.apiSecret || !this.accountKey) {
      // Debug: removed console.log statement
        return {
          success: false,
          error: 'إعدادات WhatsApp غير مكتملة'
        };
      }

      // تنسيق رقم الهاتف (مثل Python)
      const formattedPhone = this.formatPhoneNumber(to);

      // Debug: Sending WhatsApp message

      // استخدام POST مع form data (مثل Python requests.post)
      const formData = new URLSearchParams();
      formData.append('secret', this.apiSecret);
      formData.append('account', this.accountKey);
      formData.append('recipient', formattedPhone);
      formData.append('type', 'text');
      formData.append('message', message);

      // Debug: Request data

      const response = await axios.post(`${this.baseUrl}/send/whatsapp`, formData, {
        timeout: 15000, // 15 seconds timeout
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      // Debug: removed console.log statement
      // التحقق من نجاح الإرسال
      if (response.status === 200) {
      // Debug: removed console.log statement
        return {
          success: true,
          data: response.data,
          message: 'تم إرسال الرسالة بنجاح'
        };
      } else {
      // Debug: removed console.log statement
        return {
          success: false,
          error: `فشل في إرسال الرسالة. كود الحالة: ${response.status}`,
          details: response.data
        };
      }
    } catch (error: any) {
      console.error('❌ خطأ في إرسال رسالة WhatsApp:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        url: error.config?.url,
        timeout: error.code === 'ECONNABORTED'
      });

      let errorMessage = 'حدث خطأ في إرسال الرسالة';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'انتهت مهلة الاتصال - تحقق من الاتصال بالإنترنت';
      } else if (error.response?.status === 401) {
        errorMessage = 'خطأ في المصادقة - تحقق من المفتاح السري';
      } else if (error.response?.status === 403) {
        errorMessage = 'غير مصرح - تحقق من مفتاح الحساب';
      } else if (error.response?.status === 400) {
        errorMessage = 'خطأ في البيانات المرسلة - ' + (error.response?.data?.message || 'تحقق من رقم الهاتف');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return {
        success: false,
        error: errorMessage,
        details: error.response?.data
      };
    }
  }

  // إرسال رسالة OTP (محسن مثل Python)
  async sendOTP(phone: string, otp: string): Promise<CloudTextResponse> {
    try {
      // Debug: removed console.log statement
      const settings = await prisma.settings.findFirst();
      const siteName = settings?.companyName || "نظام إدارة الحضور";
      const expiryMinutes = 10;

      // استخدام قالب من الإعدادات أو القالب الافتراضي (مثل Python)
      let message: string;
      if (settings?.otpTemplate) {
        // استبدال المتغيرات في القالب
        message = settings.otpTemplate
          .replace('{site_name}', siteName)
          .replace('{otp_code}', otp)
          .replace('{otp}', otp) // للتوافق مع القوالب القديمة
          .replace('{expiry_minutes}', expiryMinutes.toString());
      } else {
        // القالب الافتراضي مثل Python
        message = `*رمز التحقق من ${siteName}*

رمز التحقق الخاص بك هو: *${otp}*

هذا الرمز صالح لمدة ${expiryMinutes} دقائق فقط.
يرجى عدم مشاركة هذا الرمز مع أي شخص.

شكراً لك.`;
      }
      // Debug: removed console.log statement
      return await this.sendMessage({ to: phone, message });
    } catch (error) {
      console.error('❌ خطأ في إرسال OTP:', error);
      return {
        success: false,
        error: 'حدث خطأ في إرسال رمز التحقق'
      };
    }
  }

  // إرسال إشعار الخروج المبكر
  async sendEarlyExitNotification(managerPhone: string, employeeName: string, exitTime: string): Promise<CloudTextResponse> {
    try {
      const settings = await prisma.settings.findFirst();
      const template = settings?.earlyExitTemplate || 'الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.';

      const message = template
        .replace('{employeeName}', employeeName)
        .replace('{exitTime}', exitTime);

      return await this.sendMessage({ to: managerPhone, message });
    } catch (error) {
      console.error('خطأ في إرسال إشعار الخروج المبكر:', error);
      return {
        success: false,
        error: 'حدث خطأ في إرسال الإشعار'
      };
    }
  }

  // إرسال إشعار وصول الزائر
  async sendVisitorArrivalNotification(employeePhone: string, visitorName: string, purpose: string): Promise<CloudTextResponse> {
    try {
      const settings = await prisma.settings.findFirst();
      const template = settings?.visitorArrivalTemplate || 'وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}';

      const message = template
        .replace('{visitorName}', visitorName)
        .replace('{purpose}', purpose);

      return await this.sendMessage({ to: employeePhone, message });
    } catch (error) {
      console.error('خطأ في إرسال إشعار وصول الزائر:', error);
      return {
        success: false,
        error: 'حدث خطأ في إرسال الإشعار'
      };
    }
  }

  // إرسال إشعار الموافقة على التصريح
  async sendPermitApprovalNotification(employeePhone: string, details: string): Promise<CloudTextResponse> {
    try {
      const settings = await prisma.settings.findFirst();
      const template = settings?.permitApprovalTemplate || 'تم الموافقة على طلبك. التفاصيل: {details}';

      const message = template.replace('{details}', details);

      return await this.sendMessage({ to: employeePhone, message });
    } catch (error) {
      console.error('خطأ في إرسال إشعار الموافقة:', error);
      return {
        success: false,
        error: 'حدث خطأ في إرسال الإشعار'
      };
    }
  }

  // اختبار الاتصال
  async testConnection(): Promise<CloudTextResponse> {
    try {
      const creditsResult = await this.getCredits();

      if (creditsResult.success) {
        return {
          success: true,
          message: 'تم الاتصال بنجاح',
          data: creditsResult.data
        };
      } else {
        return creditsResult;
      }
    } catch (error) {
      return {
        success: false,
        error: 'فشل في الاتصال بخدمة WhatsApp'
      };
    }
  }

  // تحديث الإعدادات
  async updateSettings(apiUrl: string, apiSecret: string, accountKey: string, enabled: boolean) {
    // استخدام الرابط كما هو (يجب أن يكون مثل: https://w.gcccons.org/api)
    let cleanApiUrl = apiUrl;

    // التأكد من عدم انتهاء الرابط بـ /
    cleanApiUrl = cleanApiUrl.replace(/\/$/, '');

    this.baseUrl = cleanApiUrl;
    this.apiSecret = apiSecret;
    this.accountKey = accountKey;
    this.isEnabled = enabled;
      // Debug: removed console.log statement
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const whatsappService = new WhatsAppService();

export default WhatsAppService;
