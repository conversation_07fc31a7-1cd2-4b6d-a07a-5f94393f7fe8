/**
 * مساعدات تنسيق التواريخ
 */

/**
 * تنسيق التاريخ بصيغة dd/mm/yyyy
 */
export function formatDate(date: string | Date): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return 'تاريخ غير صحيح';
  }

  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const year = d.getFullYear();

  return `${day}/${month}/${year}`;
}

/**
 * تنسيق التاريخ والوقت
 */
export function formatDateTime(date: string | Date): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return 'تاريخ غير صحيح';
  }

  const dateStr = formatDate(d);
  const timeStr = d.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });

  return `${dateStr} ${timeStr}`;
}

/**
 * تنسيق الوقت فقط
 */
export function formatTime(date: string | Date): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return 'وقت غير صحيح';
  }

  return d.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
}

/**
 * حساب الفرق بين تاريخين بالأيام
 */
export function getDaysDifference(startDate: string | Date, endDate: string | Date): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0;
  }

  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

/**
 * التحقق من أن التاريخ في المستقبل
 */
export function isFutureDate(date: string | Date): boolean {
  const d = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return d >= today;
}

/**
 * التحقق من أن التاريخ في الماضي
 */
export function isPastDate(date: string | Date): boolean {
  const d = new Date(date);
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  
  return d < today;
}

/**
 * الحصول على تاريخ اليوم بصيغة YYYY-MM-DD للـ input
 */
export function getTodayString(): string {
  const today = new Date();
  return today.toISOString().split('T')[0];
}

/**
 * تحويل التاريخ إلى صيغة YYYY-MM-DD للـ input
 */
export function toDateInputString(date: string | Date): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }

  return d.toISOString().split('T')[0];
}

/**
 * تحويل الوقت إلى صيغة HH:MM للـ input
 */
export function toTimeInputString(date: string | Date): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }

  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  
  return `${hours}:${minutes}`;
}

/**
 * تنسيق التاريخ النسبي (منذ كم يوم)
 */
export function getRelativeDate(date: string | Date): string {
  const d = new Date(date);
  const now = new Date();
  
  if (isNaN(d.getTime())) {
    return 'تاريخ غير صحيح';
  }

  const diffTime = now.getTime() - d.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffTime / (1000 * 60));

  if (diffDays > 0) {
    return `منذ ${diffDays} ${diffDays === 1 ? 'يوم' : 'أيام'}`;
  } else if (diffHours > 0) {
    return `منذ ${diffHours} ${diffHours === 1 ? 'ساعة' : 'ساعات'}`;
  } else if (diffMinutes > 0) {
    return `منذ ${diffMinutes} ${diffMinutes === 1 ? 'دقيقة' : 'دقائق'}`;
  } else {
    return 'الآن';
  }
}

/**
 * تنسيق مدة بالساعات والدقائق
 */
export function formatDuration(startTime: string | Date, endTime: string | Date): string {
  const start = new Date(startTime);
  const end = new Date(endTime);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 'مدة غير صحيحة';
  }

  const diffTime = end.getTime() - start.getTime();
  const hours = Math.floor(diffTime / (1000 * 60 * 60));
  const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0 && minutes > 0) {
    return `${hours} ساعة و ${minutes} دقيقة`;
  } else if (hours > 0) {
    return `${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
  } else if (minutes > 0) {
    return `${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
  } else {
    return 'أقل من دقيقة';
  }
}
