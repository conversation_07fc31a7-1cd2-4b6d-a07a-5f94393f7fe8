/**
 * دالة مساعدة لتنسيق التاريخ بالشكل العربي الميلادي
 * تستخدم في جميع أنحاء التطبيق لضمان الثبات
 */

export const formatArabicDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) + ' م';
};

export const formatArabicDateWithDay = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return dateObj.toLocaleDateString('ar-EG', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) + ' م';
};

export const formatArabicDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const dateStr = formatArabicDate(dateObj);
  const timeStr = dateObj.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit'
  });
  
  return `${dateStr} الساعة ${timeStr}`;
};

export const formatDateRange = (startDate: Date | string, endDate?: Date | string): string => {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  
  if (!endDate) {
    return formatArabicDate(start);
  }
  
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  
  if (diffDays === 1) {
    return formatArabicDateWithDay(start);
  } else {
    return `${formatArabicDate(start)} - ${formatArabicDate(end)} (${diffDays} أيام)`;
  }
}; 