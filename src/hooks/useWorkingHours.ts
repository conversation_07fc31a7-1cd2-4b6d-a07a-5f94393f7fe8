"use client";

import { useMemo } from 'react';
import { usePublicSettings } from '@/providers/settings-provider';

export interface WorkingHours {
  startTime: string;
  endTime: string;
  timezone: string;
  isWorkingTime: (date?: Date) => boolean;
  getWorkingHoursForDate: (date: Date) => { start: Date; end: Date };
  formatTime: (time: string) => string;
  parseTime: (time: string) => { hours: number; minutes: number };
}

/**
 * خطاف للحصول على ساعات العمل من الإعدادات
 * يوفر وظائف مساعدة للتعامل مع أوقات العمل
 */
export const useWorkingHours = (): WorkingHours => {
  const { settings } = usePublicSettings();

  const workingHours = useMemo(() => {
    const startTime = settings?.workStartTime || '07:30';
    const endTime = settings?.workEndTime || '14:30';
    const timezone = settings?.timezone || 'Asia/Muscat';

    // تحليل الوقت من النص (HH:MM)
    const parseTime = (time: string) => {
      const [hours, minutes] = time.split(':').map(Number);
      return { hours: hours || 0, minutes: minutes || 0 };
    };

    // تنسيق الوقت للعرض
    const formatTime = (time: string) => {
      const { hours, minutes } = parseTime(time);
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    };

    // التحقق من كون الوقت الحالي ضمن ساعات العمل
    const isWorkingTime = (date: Date = new Date()) => {
      const now = new Date(date.toLocaleString("en-US", { timeZone: timezone }));
      const { start, end } = getWorkingHoursForDate(now);
      
      return now >= start && now <= end;
    };

    // الحصول على ساعات العمل لتاريخ محدد
    const getWorkingHoursForDate = (date: Date) => {
      const { hours: startHours, minutes: startMinutes } = parseTime(startTime);
      const { hours: endHours, minutes: endMinutes } = parseTime(endTime);

      const start = new Date(date);
      start.setHours(startHours, startMinutes, 0, 0);

      const end = new Date(date);
      end.setHours(endHours, endMinutes, 0, 0);

      return { start, end };
    };

    return {
      startTime,
      endTime,
      timezone,
      isWorkingTime,
      getWorkingHoursForDate,
      formatTime,
      parseTime,
    };
  }, [settings]);

  return workingHours;
};

/**
 * خطاف مبسط للحصول على أوقات العمل فقط
 */
export const useWorkTimes = () => {
  const { startTime, endTime, timezone } = useWorkingHours();
  return { startTime, endTime, timezone };
};

/**
 * خطاف للتحقق من كون الوقت الحالي ضمن ساعات العمل
 */
export const useIsWorkingTime = () => {
  const { isWorkingTime } = useWorkingHours();
  return isWorkingTime();
}; 