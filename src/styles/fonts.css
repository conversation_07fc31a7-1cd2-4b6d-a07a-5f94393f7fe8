/* خط <PERSON>wal المحلي - جميع الأوزان */

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-300.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-400.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-500.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-700.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-800.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/tajawal-900.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* متغيرات الخط */
:root {
  --font-tajawal: 'Tajawal', 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
}

/* تطبيق الخط على جميع العناصر مع دعم RTL */
* {
  font-family: var(--font-tajawal) !important;
}

html {
  font-family: var(--font-tajawal) !important;
  direction: rtl;
  text-align: right;
}

body {
  font-family: var(--font-tajawal) !important;
}

/* تأكيد تطبيق الخط على العناصر المهمة */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
input, textarea, select,
label, td, th, li {
  font-family: var(--font-tajawal) !important;
}

/* تطبيق الخط على العناصر */
.font-tajawal {
  font-family: var(--font-tajawal);
}

/* أوزان الخط */
.font-extralight {
  font-weight: 200;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}
