@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-color: #1e40af;
  --secondary-color: #3b82f6;
  --accent-color: #10b981;
  --font-tajawal: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>hom<PERSON>', 'Segoe UI', sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  font-family: var(--font-tajawal) !important;
}

html {
  font-family: var(--font-tajawal) !important;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-tajawal) !important;
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

@layer components {
  .btn-primary {
    @apply bg-blue-800 hover:bg-blue-900 text-white px-4 py-2 rounded-md transition-colors;
  }

  .btn-secondary {
    @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6;
  }

  .input {
    @apply w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white;
  }
}

/* فرض تطبيق خط Tajawal على جميع العناصر */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
input, textarea, select,
label, td, th, li, ul, ol,
.btn-primary, .btn-secondary,
.card, .input {
  font-family: var(--font-tajawal) !important;
}

/* تطبيق خاص للعناصر المهمة */
.sidebar, .navbar, .menu,
.form-control, .form-label,
.table, .modal, .dropdown {
  font-family: var(--font-tajawal) !important;
}

/* أنماط الطباعة */
@media print {
  @page {
    margin: 1cm;
    size: A4;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
    color: black !important;
    background: white !important;
  }

  .print\:hidden {
    display: none !important;
  }

  .print\:block {
    display: block !important;
  }

  .print\:text-sm {
    font-size: 10pt !important;
  }

  .print\:text-base {
    font-size: 12pt !important;
  }

  .print\:text-lg {
    font-size: 14pt !important;
  }

  .print\:text-xl {
    font-size: 16pt !important;
  }

  .print\:mb-6 {
    margin-bottom: 1.5rem !important;
  }

  .print\:border {
    border: 1px solid #000 !important;
  }

  .print\:border-b {
    border-bottom: 1px solid #000 !important;
  }

  .print\:bg-transparent {
    background: transparent !important;
  }

  .print\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }

  .print\:break-inside-avoid {
    break-inside: avoid !important;
  }

  .print\:hover\:bg-transparent:hover {
    background: transparent !important;
  }

  .print\:mt-6 {
    margin-top: 1.5rem !important;
  }

  .print\:overflow-visible {
    overflow: visible !important;
  }

  .print\:max-h-none {
    max-height: none !important;
  }

  /* إخفاء العناصر غير المرغوب فيها في الطباعة */
  .no-print,
  button:not(.print-button),
  .btn-primary:not(.print-button),
  .btn-secondary:not(.print-button) {
    display: none !important;
  }

  /* تحسين الجداول للطباعة */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
  }

  th, td {
    border: 1px solid #000 !important;
    padding: 4pt 6pt !important;
    text-align: right !important;
  }

  th {
    background: #f0f0f0 !important;
    font-weight: bold !important;
  }

  /* منع كسر الصفحات داخل العناصر المهمة */
  .avoid-break {
    break-inside: avoid !important;
  }
}

/* Static theme classes */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-accent {
  background-color: var(--accent-color) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-accent {
  color: var(--accent-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.border-secondary {
  border-color: var(--secondary-color) !important;
}

.border-accent {
  border-color: var(--accent-color) !important;
}

/* Sidebar styling */
.sidebar-bg {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

/* Blue color overrides for consistency */
.bg-blue-800 {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.bg-blue-600 {
  background-color: var(--primary-color) !important;
}

.bg-blue-700 {
  background-color: var(--secondary-color) !important;
}

.text-blue-500, .text-blue-600 {
  color: var(--primary-color) !important;
}

.border-blue-500 {
  border-color: var(--primary-color) !important;
}

.border-b-2.border-blue-500 {
  border-color: var(--accent-color) !important;
}
