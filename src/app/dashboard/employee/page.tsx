"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  Fa<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaCalendarAlt,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaClipboard<PERSON>ist,
  FaChart<PERSON>ine,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaUserCheck,
  FaUserClock,
  FaUserSlash,
  FaCalendarCheck
} from "react-icons/fa";

interface DashboardStats {
  todayAttendance: {
    status: 'FULL_ATTENDANCE' | 'PARTIAL_ATTENDANCE' | 'ON_LEAVE' | 'ABSENT';
    checkInTime: string | null;
    checkOutTime: string | null;
    exitType: string | null;
    isOnLeave: boolean;
    leaveReason: string | null;
    workingHours: number;
    expectedWorkingHours: number;
    isWeekend: boolean;
    isOfficialHoliday: boolean;
    holidayName: string | null;
  };
  thisMonthStats: {
    totalWorkDays: number;
    fullAttendanceDays: number;
    partialAttendanceDays: number;
    leaveDays: number;
    absentDays: number;
    lateDays: number;
    attendanceRate: number;
    fullAttendanceRate: number;
    effectiveWorkDays: number;
  };
  pendingRequests: {
    leaves: number;
    visitors: number;
    afterHours: number;
  };
  upcomingEvents: {
    holidays: number;
    approvedLeaves: number;
    scheduledVisitors: number;
  };
}

export default function EmployeeDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/dashboard/employee-stats");
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("خطأ في جلب إحصائيات الموظف:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // Auto-refresh every 5 minutes for real-time statistics
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Helper function to get status details
  const getStatusDetails = (status: string) => {
    switch (status) {
      case 'FULL_ATTENDANCE':
        return {
          label: 'حضور',
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          icon: FaUserCheck
        };
      case 'PARTIAL_ATTENDANCE':
        return {
          label: 'حضور جزئي',
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          icon: FaUserClock
        };
      case 'ON_LEAVE':
        return {
          label: 'في إجازة',
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          icon: FaCalendarAlt
        };
      case 'ABSENT':
        return {
          label: 'غياب',
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          icon: FaUserSlash
        };
      default:
        return {
          label: 'غير محدد',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          icon: FaUser
        };
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const statusDetails = getStatusDetails(stats?.todayAttendance.status || 'ABSENT');
  const StatusIcon = statusDetails.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-white/20 rounded-full">
            <FaUser size={32} />
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">مرحباً، {session?.user?.name}</h1>
            <p className="text-blue-100 mt-1">
              لوحة تحكم الموظف - {new Date().toLocaleDateString('en-GB')}
            </p>
          </div>
          {/* Status Indicators */}
          <div className="flex gap-2">
            {stats?.todayAttendance.isOnLeave && (
              <div className="bg-white/20 rounded-lg p-3 text-center">
                <div className="text-sm font-medium">في إجازة</div>
                <div className="text-xs text-blue-200 mt-1">
                  {stats.todayAttendance.leaveReason}
                </div>
              </div>
            )}
            {(stats?.todayAttendance.isWeekend || stats?.todayAttendance.isOfficialHoliday) && (
              <div className="bg-white/20 rounded-lg p-3 text-center">
                <div className="text-sm font-medium">
                  {stats.todayAttendance.isOfficialHoliday ? 'عطلة رسمية' : 'عطلة نهاية أسبوع'}
                </div>
                {stats.todayAttendance.holidayName && (
                  <div className="text-xs text-blue-200 mt-1">
                    {stats.todayAttendance.holidayName}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Today's Status */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">حالة اليوم</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Status Card */}
          <div className={`p-6 rounded-xl ${statusDetails.bgColor}`}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white rounded-full">
                <StatusIcon className={statusDetails.color} size={24} />
              </div>
              <div>
                <h3 className={`text-xl font-bold ${statusDetails.color}`}>
                  {statusDetails.label}
                </h3>
                <p className="text-gray-600 mt-1">
                  {stats?.todayAttendance.status === 'ON_LEAVE' && stats.todayAttendance.leaveReason}
                  {stats?.todayAttendance.status === 'FULL_ATTENDANCE' && 'حضور طبيعي'}
                  {stats?.todayAttendance.status === 'PARTIAL_ATTENDANCE' && 'خروج مبكر'}
                  {stats?.todayAttendance.status === 'ABSENT' && 'لم يتم تسجيل حضور'}
                </p>
              </div>
            </div>
          </div>

          {/* Working Hours Card */}
          <div className="p-6 bg-gray-50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white rounded-full">
                <FaClock className="text-purple-600" size={24} />
              </div>
              <div>
                <h3 className="text-xl font-bold text-purple-600">
                  {stats?.todayAttendance.workingHours || 0}h
                </h3>
                <p className="text-gray-600 mt-1">
                  من أصل {stats?.todayAttendance.expectedWorkingHours || 0} ساعات مطلوبة
                </p>
                {stats?.todayAttendance.workingHours && stats?.todayAttendance.expectedWorkingHours && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ 
                          width: `${Math.min((stats.todayAttendance.workingHours / stats.todayAttendance.expectedWorkingHours) * 100, 100)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Time Details */}
        {(stats?.todayAttendance.checkInTime || stats?.todayAttendance.checkOutTime) && (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            {stats.todayAttendance.checkInTime && (
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <FaCheckCircle className="text-green-600" size={16} />
                <div>
                  <p className="font-medium text-green-700">وقت الدخول</p>
                  <p className="text-sm text-green-600">
                    {new Date(stats.todayAttendance.checkInTime).toLocaleTimeString('en-GB')}
                  </p>
                </div>
              </div>
            )}
            
            {stats.todayAttendance.checkOutTime && (
              <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                <FaTimesCircle className="text-red-600" size={16} />
                <div>
                  <p className="font-medium text-red-700">وقت الخروج</p>
                  <p className="text-sm text-red-600">
                    {new Date(stats.todayAttendance.checkOutTime).toLocaleTimeString('en-GB')}
                    {stats.todayAttendance.exitType && (
                      <span className="ml-2 text-xs">
                        ({stats.todayAttendance.exitType === 'PERSONAL' ? 'شخصي' : 'رسمي'})
                      </span>
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Monthly Statistics */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">إحصائيات الشهر الحالي</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Attendance Days */}
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="p-3 bg-green-100 rounded-full w-fit mx-auto mb-3">
              <FaUserCheck className="text-green-600" size={24} />
            </div>
            <p className="text-2xl font-bold text-green-600">
              {stats?.thisMonthStats.fullAttendanceDays || 0}
            </p>
            <p className="text-sm text-green-700 mt-1">أيام حضور</p>
            <p className="text-xs text-gray-600 mt-1">
                              {stats?.thisMonthStats.fullAttendanceRate || 0}% معدل الحضور
            </p>
          </div>

          {/* Partial Attendance Days */}
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="p-3 bg-orange-100 rounded-full w-fit mx-auto mb-3">
              <FaUserClock className="text-orange-600" size={24} />
            </div>
            <p className="text-2xl font-bold text-orange-600">
              {stats?.thisMonthStats.partialAttendanceDays || 0}
            </p>
            <p className="text-sm text-orange-700 mt-1">أيام حضور جزئي</p>
            <p className="text-xs text-gray-600 mt-1">خروج مبكر</p>
          </div>

          {/* Leave Days */}
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-3">
              <FaCalendarAlt className="text-blue-600" size={24} />
            </div>
            <p className="text-2xl font-bold text-blue-600">
              {stats?.thisMonthStats.leaveDays || 0}
            </p>
            <p className="text-sm text-blue-700 mt-1">أيام إجازة</p>
            <p className="text-xs text-gray-600 mt-1">إجازات معتمدة</p>
          </div>

          {/* Absent Days */}
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="p-3 bg-red-100 rounded-full w-fit mx-auto mb-3">
              <FaUserSlash className="text-red-600" size={24} />
            </div>
            <p className="text-2xl font-bold text-red-600">
              {stats?.thisMonthStats.absentDays || 0}
            </p>
            <p className="text-sm text-red-700 mt-1">أيام غياب</p>
            <p className="text-xs text-gray-600 mt-1">بدون إذن</p>
          </div>
        </div>

        {/* Overall Statistics */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">أيام العمل الفعلية</span>
            <span className="text-xl font-bold text-gray-900">
              {stats?.thisMonthStats.effectiveWorkDays || 0}
            </span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">معدل الحضور الإجمالي</span>
            <span className="text-xl font-bold text-gray-900">
              {stats?.thisMonthStats.attendanceRate || 0}%
            </span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">أيام التأخير</span>
            <span className="text-xl font-bold text-gray-900">
              {stats?.thisMonthStats.lateDays || 0}
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/dashboard/leaves/new"
            className="flex items-center gap-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
          >
            <FaCalendarAlt className="text-green-600" size={20} />
            <span className="font-medium text-green-700">طلب إجازة</span>
          </Link>
          
          <Link
            href="/dashboard/visitors/new"
            className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
          >
            <FaUsers className="text-blue-600" size={20} />
            <span className="font-medium text-blue-700">طلب زيارة</span>
          </Link>
          
          <Link
            href="/dashboard/after-hours/new"
            className="flex items-center gap-3 p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors"
          >
            <FaClock className="text-orange-600" size={20} />
            <span className="font-medium text-orange-700">عمل بعد الدوام</span>
          </Link>
          
          <Link
            href="/dashboard/attendance"
            className="flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
          >
            <FaChartLine className="text-purple-600" size={20} />
            <span className="font-medium text-purple-700">سجل الحضور</span>
          </Link>
        </div>
      </div>

      {/* Pending Requests & Upcoming Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Requests */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الطلبات المعلقة</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaCalendarAlt className="text-yellow-600" size={16} />
                <span className="font-medium text-yellow-700">طلبات الإجازات</span>
              </div>
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm">
                {stats?.pendingRequests.leaves || 0} معلق
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaUsers className="text-blue-600" size={16} />
                <span className="font-medium text-blue-700">طلبات الزيارات</span>
              </div>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                {stats?.pendingRequests.visitors || 0} معلق
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaClock className="text-orange-600" size={16} />
                <span className="font-medium text-orange-700">العمل بعد الدوام</span>
              </div>
              <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-sm">
                {stats?.pendingRequests.afterHours || 0} معلق
              </span>
            </div>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الأحداث القادمة</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaCalendarCheck className="text-purple-600" size={16} />
                <span className="font-medium text-purple-700">عطل رسمية</span>
              </div>
              <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm">
                {stats?.upcomingEvents.holidays || 0} قادمة
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaCalendarAlt className="text-green-600" size={16} />
                <span className="font-medium text-green-700">إجازات معتمدة</span>
              </div>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                {stats?.upcomingEvents.approvedLeaves || 0} قادمة
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaUsers className="text-indigo-600" size={16} />
                <span className="font-medium text-indigo-700">زيارات مجدولة</span>
              </div>
              <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-sm">
                {stats?.upcomingEvents.scheduledVisitors || 0} قادمة
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
