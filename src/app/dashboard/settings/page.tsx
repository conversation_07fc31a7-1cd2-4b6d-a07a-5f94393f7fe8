"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useFullSettings } from "@/providers/settings-provider";
import {
  FaCog,
  FaBuilding,
  FaClock,
  FaWhatsapp,
  FaEnvelope,
  FaSave,
  FaUpload,
  FaImage
} from "react-icons/fa";

const settingsSchema = z.object({
  // معلومات الهيئة
  companyName: z.string().min(1, "اسم الهيئة مطلوب"),
  logo: z.string().optional(),
  favicon: z.string().optional(),

  // ساعات العمل
  workStartTime: z.string().min(1, "وقت بداية العمل مطلوب"),
  workEndTime: z.string().min(1, "وقت نهاية العمل مطلوب"),
  workHoursRequired: z.number().min(1, "الحد الأدنى لساعات العمل مطلوب"),
  workingHours: z.number().min(1, "عدد ساعات العمل مطلوب"),
  timezone: z.string().min(1, "المنطقة الزمنية مطلوبة"),

  // إعدادات WhatsApp
  whatsappApiUrl: z.string().optional(),
  whatsappApiSecret: z.string().optional(),
  whatsappAccountKey: z.string().optional(),
  whatsappEnabled: z.boolean(),

  // قوالب الرسائل
  otpTemplate: z.string().optional(),
  earlyExitTemplate: z.string().optional(),
  visitorArrivalTemplate: z.string().optional(),
  permitApprovalTemplate: z.string().optional(),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

export default function SettingsPage() {
  const { data: session } = useSession();
  const { fullSettings, loading, error: settingsError, updateSettings, refreshFullSettings } = useFullSettings();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("company");
  const [testingWhatsApp, setTestingWhatsApp] = useState(false);
  const [testPhone, setTestPhone] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
  });

  // تحديث النموذج عند تحميل الإعدادات
  useEffect(() => {
    if (fullSettings) {
      reset({
        companyName: fullSettings.companyName || "",
        logo: fullSettings.logo || "",
        favicon: fullSettings.favicon || "",
        workStartTime: fullSettings.workStartTime || "07:30",
        workEndTime: fullSettings.workEndTime || "14:30",
        workHoursRequired: fullSettings.workHoursRequired || 4,
        workingHours: fullSettings.workingHours || 7,
        timezone: fullSettings.timezone || "Asia/Muscat",
        whatsappApiUrl: fullSettings.whatsappApiUrl || "https://w.gcccons.org/api",
        whatsappApiSecret: fullSettings.whatsappApiSecret || "",
        whatsappAccountKey: fullSettings.whatsappAccountKey || "",
        whatsappEnabled: fullSettings.whatsappEnabled || false,
        otpTemplate: fullSettings.otpTemplate || "رمز التحقق الخاص بك هو: {otp}",
        earlyExitTemplate: fullSettings.earlyExitTemplate || "الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.",
        visitorArrivalTemplate: fullSettings.visitorArrivalTemplate || "وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}",
        permitApprovalTemplate: fullSettings.permitApprovalTemplate || "تم الموافقة على طلبك. التفاصيل: {details}",
      });
      setError(null);
    }
  }, [fullSettings, reset]);

  // عرض خطأ الإعدادات إذا كان موجوداً
  useEffect(() => {
    if (settingsError) {
      setError(settingsError);
    }
  }, [settingsError]);

  // حفظ الإعدادات
  const onSubmit = async (data: SettingsFormData) => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const success = await updateSettings(data);

      if (success) {
        setSuccess("تم حفظ الإعدادات بنجاح");
      } else {
        setError("فشل في حفظ الإعدادات");
      }

    } catch (error) {
      console.error("خطأ في حفظ الإعدادات:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ في حفظ الإعدادات");
    } finally {
      setSaving(false);
    }
  };

  // رفع الملفات
  const handleFileUpload = async (file: File, type: 'logo' | 'favicon') => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('فشل في رفع الملف');
      }

      const data = await response.json();
      setValue(type, data.url);
      setSuccess(`تم رفع ${type === 'logo' ? 'الشعار' : 'الأيقونة'} بنجاح`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'حدث خطأ في رفع الملف');
    }
  };

  // اختبار اتصال WhatsApp
  const testWhatsAppConnection = async () => {
    try {
      setTestingWhatsApp(true);
      setError(null);
      setSuccess(null);

      const apiUrl = watch("whatsappApiUrl");
      const apiSecret = watch("whatsappApiSecret");
      const accountKey = watch("whatsappAccountKey");

      if (!apiUrl || !apiSecret || !accountKey) {
        setError("يجب إدخال جميع بيانات الاتصال أولاً");
        return;
      }

      const response = await fetch("/api/whatsapp/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          apiUrl,
          apiSecret,
          accountKey,
          testPhone: testPhone || undefined,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "فشل في اختبار الاتصال");
      }

      if (data.success) {
        setSuccess(
          testPhone
            ? "تم اختبار الاتصال وإرسال الرسالة التجريبية بنجاح!"
            : "تم اختبار الاتصال بنجاح!"
        );
      } else {
        setError(data.error || "فشل في اختبار الاتصال");
      }
    } catch (error) {
      console.error("خطأ في اختبار WhatsApp:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ في اختبار الاتصال");
    } finally {
      setTestingWhatsApp(false);
    }
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManageSettings = userRole === "ADMIN";

  if (!canManageSettings) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const tabs = [
    { id: "company", label: "معلومات الهيئة", icon: <FaBuilding /> },
    { id: "work", label: "ساعات العمل", icon: <FaClock /> },
    { id: "whatsapp", label: "WhatsApp", icon: <FaWhatsapp /> },
    { id: "templates", label: "قوالب الرسائل", icon: <FaEnvelope /> },
  ];

  return (
    <div>
      <div className="flex items-center gap-4 mb-6">
        <FaCog className="text-blue-500" size={32} />
        <h1 className="text-2xl font-bold">إعدادات النظام</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          {success}
        </div>
      )}

      <div className="card">
        {/* التبويبات */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8 space-x-reverse">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          {/* تبويب معلومات الهيئة */}
          {activeTab === "company" && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">معلومات الهيئة</h3>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  اسم الهيئة *
                </label>
                <input
                  type="text"
                  className="input"
                  {...register("companyName")}
                  disabled={saving}
                />
                {errors.companyName && (
                  <p className="text-red-500 text-sm mt-1">{errors.companyName.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    شعار الهيئة
                  </label>
                  <div className="space-y-2">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleFileUpload(file, 'logo');
                      }}
                      className="input"
                      disabled={saving}
                    />
                    {watch("logo") && (
                      <div className="mt-2">
                        <img
                          src={watch("logo")}
                          alt="شعار الهيئة"
                          className="h-16 w-auto object-contain border rounded"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    أيقونة الموقع (Favicon)
                  </label>
                  <div className="space-y-2">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleFileUpload(file, 'favicon');
                      }}
                      className="input"
                      disabled={saving}
                    />
                    {watch("favicon") && (
                      <div className="mt-2">
                        <img
                          src={watch("favicon")}
                          alt="أيقونة الموقع"
                          className="h-8 w-8 object-contain border rounded"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* تبويب ساعات العمل */}
          {activeTab === "work" && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">إعدادات ساعات العمل</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    وقت بداية العمل *
                  </label>
                  <input
                    type="time"
                    className="input"
                    {...register("workStartTime")}
                    disabled={saving}
                  />
                  {errors.workStartTime && (
                    <p className="text-red-500 text-sm mt-1">{errors.workStartTime.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    وقت نهاية العمل *
                  </label>
                  <input
                    type="time"
                    className="input"
                    {...register("workEndTime")}
                    disabled={saving}
                  />
                  {errors.workEndTime && (
                    <p className="text-red-500 text-sm mt-1">{errors.workEndTime.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    الحد الأدنى لساعات العمل *
                  </label>
                  <input
                    type="number"
                    step="0.5"
                    min="1"
                    max="12"
                    className="input"
                    {...register("workHoursRequired", { valueAsNumber: true })}
                    disabled={saving}
                  />
                  {errors.workHoursRequired && (
                    <p className="text-red-500 text-sm mt-1">{errors.workHoursRequired.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    عدد ساعات العمل *
                  </label>
                  <input
                    type="number"
                    step="0.5"
                    min="1"
                    max="12"
                    className="input"
                    {...register("workingHours", { valueAsNumber: true })}
                    disabled={saving}
                  />
                  {errors.workingHours && (
                    <p className="text-red-500 text-sm mt-1">{errors.workingHours.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  المنطقة الزمنية *
                </label>
                <select
                  className="input"
                  {...register("timezone")}
                  disabled={saving}
                >
                  <option value="Asia/Muscat">آسيا/مسقط (GMT+4)</option>
                  <option value="Asia/Riyadh">آسيا/الرياض (GMT+3)</option>
                  <option value="Asia/Dubai">آسيا/دبي (GMT+4)</option>
                  <option value="Asia/Kuwait">آسيا/الكويت (GMT+3)</option>
                  <option value="Asia/Qatar">آسيا/قطر (GMT+3)</option>
                  <option value="Asia/Bahrain">آسيا/البحرين (GMT+3)</option>
                </select>
                {errors.timezone && (
                  <p className="text-red-500 text-sm mt-1">{errors.timezone.message}</p>
                )}
              </div>
            </div>
          )}

          {/* تبويب WhatsApp */}
          {activeTab === "whatsapp" && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">إعدادات WhatsApp</h3>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
                <div className="flex items-start gap-3">
                  <FaWhatsapp className="text-green-500 mt-1" size={20} />
                  <div>
                    <h4 className="font-medium text-blue-800 dark:text-blue-200">خدمة CloudText</h4>
                    <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                      يتم استخدام خدمة CloudText لإرسال رسائل WhatsApp. تأكد من الحصول على API Secret من موقعهم.
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label className="flex items-center gap-2 mb-4">
                  <input
                    type="checkbox"
                    className="rounded"
                    {...register("whatsappEnabled")}
                    disabled={saving}
                  />
                  <span className="font-medium">تفعيل خدمة WhatsApp</span>
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    رابط API
                  </label>
                  <input
                    type="url"
                    className="input"
                    {...register("whatsappApiUrl")}
                    disabled={saving}
                    placeholder="https://w.gcccons.org/api"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    رابط API الأساسي لخدمة CloudText
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    💡 مثال: https://w.gcccons.org/api (بدون /send/whatsapp)
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    ✅ النظام سيضيف /send/whatsapp تلقائياً عند الإرسال
                  </p>
                </div>

                <div>
                  <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                    مفتاح الحساب
                  </label>
                  <input
                    type="text"
                    className="input"
                    {...register("whatsappAccountKey")}
                    disabled={saving}
                    placeholder="أدخل مفتاح الحساب"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    مفتاح الحساب الخاص بك في CloudText
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  المفتاح السري
                </label>
                <input
                  type="password"
                  className="input"
                  {...register("whatsappApiSecret")}
                  disabled={saving}
                  placeholder="أدخل المفتاح السري من CloudText"
                />
                <p className="text-sm text-gray-500 mt-1">
                  يمكنك الحصول على المفتاح السري من لوحة تحكم CloudText
                </p>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">ملاحظة مهمة</h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  تأكد من اختبار الاتصال بعد حفظ الإعدادات للتأكد من صحة API Secret.
                </p>
              </div>

              {/* اختبار الاتصال */}
              <div className="border-t pt-6">
                <h4 className="font-medium mb-4">اختبار الاتصال</h4>

                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-md mb-4">
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    💡 <strong>مهم:</strong> احفظ الإعدادات أولاً قبل الاختبار لضمان استخدام البيانات الصحيحة
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                      رقم الهاتف للاختبار (اختياري)
                    </label>
                    <input
                      type="tel"
                      className="input"
                      value={testPhone}
                      onChange={(e) => setTestPhone(e.target.value)}
                      placeholder="96812345678"
                      disabled={testingWhatsApp}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      إذا تم إدخال رقم، سيتم إرسال رسالة تجريبية
                    </p>
                  </div>

                  <button
                    type="button"
                    onClick={testWhatsAppConnection}
                    disabled={testingWhatsApp || !watch("whatsappApiUrl") || !watch("whatsappApiSecret") || !watch("whatsappAccountKey")}
                    className="btn-secondary flex items-center gap-2"
                  >
                    {testingWhatsApp ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600"></div>
                    ) : (
                      <FaWhatsapp />
                    )}
                    {testingWhatsApp ? "جاري الاختبار..." : "اختبار الاتصال"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* تبويب قوالب الرسائل */}
          {activeTab === "templates" && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">قوالب رسائل WhatsApp</h3>

              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
                <h4 className="font-medium mb-2">المتغيرات المتاحة</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{otp}"}</code> - رمز التحقق</p>
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{employeeName}"}</code> - اسم الموظف</p>
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{exitTime}"}</code> - وقت الخروج</p>
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{visitorName}"}</code> - اسم الزائر</p>
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{purpose}"}</code> - الغرض من الزيارة</p>
                  <p><code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{"{details}"}</code> - تفاصيل إضافية</p>
                </div>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  قالب رسالة OTP
                </label>
                <textarea
                  className="input min-h-[80px] resize-vertical"
                  {...register("otpTemplate")}
                  disabled={saving}
                  placeholder="رمز التحقق الخاص بك هو: {otp}"
                />
                <p className="text-sm text-gray-500 mt-1">
                  يُستخدم لإرسال رمز التحقق للمستخدمين
                </p>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  قالب رسالة الخروج المبكر
                </label>
                <textarea
                  className="input min-h-[80px] resize-vertical"
                  {...register("earlyExitTemplate")}
                  disabled={saving}
                  placeholder="الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي."
                />
                <p className="text-sm text-gray-500 mt-1">
                  يُرسل للمدير عند خروج الموظف قبل انتهاء الدوام
                </p>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  قالب رسالة وصول الزائر
                </label>
                <textarea
                  className="input min-h-[80px] resize-vertical"
                  {...register("visitorArrivalTemplate")}
                  disabled={saving}
                  placeholder="وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}"
                />
                <p className="text-sm text-gray-500 mt-1">
                  يُرسل للموظف عند وصول الزائر إلى البوابة
                </p>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                  قالب رسالة الموافقة على التصريح
                </label>
                <textarea
                  className="input min-h-[80px] resize-vertical"
                  {...register("permitApprovalTemplate")}
                  disabled={saving}
                  placeholder="تم الموافقة على طلبك. التفاصيل: {details}"
                />
                <p className="text-sm text-gray-500 mt-1">
                  يُرسل للموظف عند الموافقة على طلبه
                </p>
              </div>
            </div>
          )}

          {/* حفظ الإعدادات */}
          <div className="flex justify-end mt-8">
            <button
              type="submit"
              disabled={saving}
              className="btn-primary flex items-center gap-2"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              ) : (
                <FaSave />
              )}
              {saving ? "جاري الحفظ..." : "حفظ الإعدادات"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}