"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  <PERSON>a<PERSON>sers,
  FaPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaLock,
  FaUnlock
} from "react-icons/fa";
import { DataTable } from "@/components/ui/DataTable";

interface User {
  id: string;
  employeeNumber?: string;
  name: string;
  email: string;
  phone: string;
  position?: string;
  role: string;
  department?: {
    id: string;
    name: string;
  };
  manager?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  _count: {
    employees: number;
  };
}

export default function UsersPage() {
  const { data: session } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // تسميات الأدوار
  const roleLabels = {
    ADMIN: "مدير النظام",
    HR: "الموارد البشرية",
    MANAGER: "مدير",
    EMPLOYEE: "موظف",
    SECURITY: "أمن"
  };

  // ألوان الأدوار
  const roleColors = {
    ADMIN: "bg-red-100 text-red-800 border-red-200",
    HR: "bg-blue-100 text-blue-800 border-blue-200",
    MANAGER: "bg-purple-100 text-purple-800 border-purple-200",
    EMPLOYEE: "bg-green-100 text-green-800 border-green-200",
    SECURITY: "bg-orange-100 text-orange-800 border-orange-200"
  };

  // أيقونات الأدوار
  const roleIcons = {
    ADMIN: <FaUserShield className="inline ml-1" size={14} />,
    HR: <FaUserCog className="inline ml-1" size={14} />,
    MANAGER: <FaUserTie className="inline ml-1" size={14} />,
    EMPLOYEE: <FaUser className="inline ml-1" size={14} />,
    SECURITY: <FaUserShield className="inline ml-1" size={14} />
  };

  // أعمدة الجدول
  const columns = [
    {
      key: 'employeeNumber',
      title: 'رقم الموظف',
      render: (value: string) => value || <span className="text-gray-400">غير محدد</span>,
    },
    {
      key: 'name',
      title: 'الاسم',
      searchable: true,
      render: (value: string, row: User) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.email}</div>
        </div>
      ),
    },
    {
      key: 'role',
      title: 'الدور',
      sortable: true,
      render: (value: keyof typeof roleLabels) => (
        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${roleColors[value]}`}>
          {roleIcons[value]}
          {roleLabels[value]}
        </span>
      ),
    },
    {
      key: 'department',
      title: 'القسم',
      render: (value: User['department']) =>
        value ? value.name : <span className="text-gray-400">غير محدد</span>,
    },
    {
      key: 'position',
      title: 'المنصب',
      render: (value: string) => value || <span className="text-gray-400">غير محدد</span>,
    },
    {
      key: 'phone',
      title: 'الهاتف',
      render: (value: string) => value || <span className="text-gray-400">غير محدد</span>,
    },
    {
      key: '_count',
      title: 'المرؤوسين',
      render: (value: { employees: number }) => (
        <span className="text-center block">{value.employees}</span>
      ),
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_: any, row: User) => (
        <div className="flex justify-center gap-2">
          <Link
            href={`/dashboard/users/${row.id}`}
            className="text-blue-600 hover:text-blue-800 p-2"
            title="عرض التفاصيل"
          >
            <FaEye size={16} />
          </Link>
          <Link
            href={`/dashboard/users/${row.id}/edit`}
            className="text-green-600 hover:text-green-800 p-2"
            title="تعديل"
          >
            <FaEdit size={16} />
          </Link>
          <button
            onClick={() => handleResetPassword(row.id)}
            disabled={actionLoading === row.id}
            className="text-orange-600 hover:text-orange-800 p-2 disabled:opacity-50"
            title="إعادة تعيين كلمة المرور"
          >
            <FaLock size={16} />
          </button>
          {row.role !== 'ADMIN' && (
            <button
              onClick={() => handleDelete(row.id)}
              disabled={actionLoading === row.id}
              className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
              title="حذف"
            >
              {actionLoading === row.id ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
              ) : (
                <FaTrash size={16} />
              )}
            </button>
          )}
        </div>
      ),
    },
  ];

  // جلب المستخدمين
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/users");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب المستخدمين");
      }

      const data = await response.json();
      setUsers(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب المستخدمين:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // إعادة تعيين كلمة المرور
  const handleResetPassword = async (userId: string) => {
    if (!confirm("هل أنت متأكد من إعادة تعيين كلمة المرور؟ سيتم إرسال كلمة مرور جديدة للمستخدم.")) {
      return;
    }

    try {
      setActionLoading(userId);
      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في إعادة تعيين كلمة المرور");
      }

      alert("تم إعادة تعيين كلمة المرور بنجاح");
    } catch (error) {
      console.error("خطأ في إعادة تعيين كلمة المرور:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في إعادة تعيين كلمة المرور");
    } finally {
      setActionLoading(null);
    }
  };

  // حذف المستخدم
  const handleDelete = async (userId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.")) {
      return;
    }

    try {
      setActionLoading(userId);
      const response = await fetch(`/api/users/${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف المستخدم");
      }

      await fetchUsers();
      alert("تم حذف المستخدم بنجاح");
    } catch (error) {
      console.error("خطأ في حذف المستخدم:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف المستخدم");
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const isAdmin = userRole === "ADMIN";

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام فقط</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FaUsers className="text-blue-600" />
            إدارة المستخدمين والصلاحيات
          </h1>
          <p className="text-gray-600 mt-1">إدارة المستخدمين وأدوارهم وصلاحياتهم في النظام</p>
        </div>
        <div className="flex gap-3">
          <Link
            href="/dashboard/users/roles"
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <FaUserShield size={16} />
            إدارة الأدوار
          </Link>
          <Link
            href="/dashboard/users/new"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <FaPlus size={16} />
            إضافة مستخدم جديد
          </Link>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {Object.entries(roleLabels).map(([role, label]) => {
          const count = users.filter(user => user.role === role).length;
          return (
            <div key={role} className="bg-white rounded-lg shadow-md p-4 border-r-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{label}</p>
                  <p className="text-2xl font-bold text-gray-900">{count}</p>
                </div>
                <div className={`p-3 rounded-full ${roleColors[role as keyof typeof roleColors].replace('border-', 'bg-').replace('text-', '').replace('bg-', 'bg-opacity-20 bg-')}`}>
                  {roleIcons[role as keyof typeof roleIcons]}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* رسائل الخطأ */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* جدول المستخدمين */}
      {users.length === 0 && !loading ? (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
          <div className="text-center">
            <FaUsers size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد مستخدمين مسجلين</p>
            <Link
              href="/dashboard/users/new"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
            >
              <FaPlus size={16} />
              إضافة أول مستخدم
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <DataTable
            data={users}
            columns={columns}
            loading={loading}
            searchPlaceholder="البحث في المستخدمين..."
            pageSize={10}
          />
        </div>
      )}
    </div>
  );
}
