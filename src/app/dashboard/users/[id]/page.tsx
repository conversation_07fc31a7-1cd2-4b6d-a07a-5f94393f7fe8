"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { 
  <PERSON>a<PERSON>ser, 
  FaEnvelope, 
  FaPhone, 
  FaIdCard, 
  FaUserShield, 
  FaBuilding, 
  FaUserTie, 
  FaEdit,
  FaArrowLeft,
  FaCalendarAlt,
  FaUsers,
  FaClock,
  FaFileAlt,
  FaEye,
  FaTrash,
  FaLock
} from "react-icons/fa";

interface UserDetails {
  id: string;
  employeeNumber?: string;
  name: string;
  email: string;
  phone: string;
  position?: string;
  role: string;
  departmentId?: string;
  managerId?: string;
  createdAt: string;
  updatedAt: string;
  department?: {
    id: string;
    name: string;
  };
  manager?: {
    id: string;
    name: string;
  };
  employees: {
    id: string;
    name: string;
    employeeNumber?: string;
    position?: string;
  }[];
  _count: {
    employees: number;
    attendanceRecords: number;
    leaveRequests: number;
    visitorRequests: number;
    afterHoursPermits: number;
  };
}

export default function UserDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { data: session } = useSession();
  const router = useRouter();
  const [user, setUser] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);

  // تسميات الأدوار
  const roleLabels = {
    ADMIN: "مدير النظام",
    HR: "الموارد البشرية", 
    MANAGER: "مدير",
    EMPLOYEE: "موظف",
    SECURITY: "أمن"
  };

  // ألوان الأدوار
  const roleColors = {
    ADMIN: "bg-red-100 text-red-800 border-red-200",
    HR: "bg-blue-100 text-blue-800 border-blue-200",
    MANAGER: "bg-purple-100 text-purple-800 border-purple-200", 
    EMPLOYEE: "bg-green-100 text-green-800 border-green-200",
    SECURITY: "bg-orange-100 text-orange-800 border-orange-200"
  };

  const fetchUser = async (id: string) => {
    try {
      const response = await fetch(`/api/users/${id}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب بيانات المستخدم");
      }
      const data = await response.json();
      setUser(data);
    } catch (error) {
      console.error("خطأ في جلب المستخدم:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
    };
    loadParams();
  }, [params]);

  useEffect(() => {
    if (resolvedParams?.id) {
      fetchUser(resolvedParams.id);
    }
  }, [resolvedParams?.id]);

  // إعادة تعيين كلمة المرور
  const handleResetPassword = async () => {
    if (!confirm("هل أنت متأكد من إعادة تعيين كلمة المرور؟ سيتم إرسال كلمة مرور جديدة للمستخدم.")) {
      return;
    }

    try {
      setActionLoading("reset-password");
      const response = await fetch(`/api/users/${resolvedParams?.id}/reset-password`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في إعادة تعيين كلمة المرور");
      }

      const result = await response.json();
      alert(`تم إعادة تعيين كلمة المرور بنجاح\nكلمة المرور الجديدة: ${result.newPassword}`);
    } catch (error) {
      console.error("خطأ في إعادة تعيين كلمة المرور:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في إعادة تعيين كلمة المرور");
    } finally {
      setActionLoading(null);
    }
  };

  // حذف المستخدم
  const handleDelete = async () => {
    if (!confirm("هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.")) {
      return;
    }

    try {
      setActionLoading("delete");
      const response = await fetch(`/api/users/${resolvedParams?.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف المستخدم");
      }

      alert("تم حذف المستخدم بنجاح");
      router.push("/dashboard/users");
    } catch (error) {
      console.error("خطأ في حذف المستخدم:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف المستخدم");
    } finally {
      setActionLoading(null);
    }
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const isAdmin = userRole === "ADMIN";

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام فقط</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3 text-gray-600">جاري تحميل بيانات المستخدم...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg inline-block">
          {error}
        </div>
        <div className="mt-4">
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800"
          >
            العودة
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">المستخدم غير موجود</p>
        <div className="mt-4">
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800"
          >
            العودة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <button
              onClick={() => router.back()}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <FaArrowLeft size={20} />
            </button>
            <h1 className="text-3xl font-bold text-gray-900">تفاصيل المستخدم</h1>
          </div>
          <p className="text-gray-600 mr-8">عرض تفاصيل وإحصائيات المستخدم</p>
        </div>
        <div className="flex gap-3">
          <Link
            href={`/dashboard/users/${user.id}/edit`}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
          >
            <FaEdit size={16} />
            تعديل
          </Link>
          <button
            onClick={handleResetPassword}
            disabled={actionLoading === "reset-password"}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {actionLoading === "reset-password" ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <FaLock size={16} />
            )}
            إعادة تعيين كلمة المرور
          </button>
          {user.role !== 'ADMIN' && (
            <button
              onClick={handleDelete}
              disabled={actionLoading === "delete"}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              {actionLoading === "delete" ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <FaTrash size={16} />
              )}
              حذف
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* المعلومات الأساسية */}
        <div className="lg:col-span-2 space-y-6">
          {/* بطاقة المعلومات الشخصية */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <FaUser className="text-blue-600" />
                المعلومات الشخصية
              </h2>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaIdCard className="inline ml-2 text-blue-500" />
                    رقم الموظف
                  </label>
                  <p className="text-gray-900">{user.employeeNumber || "غير محدد"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaUser className="inline ml-2 text-green-500" />
                    الاسم الكامل
                  </label>
                  <p className="text-gray-900 font-medium">{user.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaEnvelope className="inline ml-2 text-red-500" />
                    البريد الإلكتروني
                  </label>
                  <p className="text-gray-900">{user.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaPhone className="inline ml-2 text-purple-500" />
                    رقم الهاتف
                  </label>
                  <p className="text-gray-900">{user.phone || "غير محدد"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaUserTie className="inline ml-2 text-indigo-500" />
                    المنصب
                  </label>
                  <p className="text-gray-900">{user.position || "غير محدد"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaUserShield className="inline ml-2 text-orange-500" />
                    الدور
                  </label>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${roleColors[user.role as keyof typeof roleColors]}`}>
                    {roleLabels[user.role as keyof typeof roleLabels]}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* المعلومات التنظيمية */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <FaBuilding className="text-teal-600" />
                المعلومات التنظيمية
              </h2>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaBuilding className="inline ml-2 text-teal-500" />
                    القسم
                  </label>
                  <p className="text-gray-900">{user.department?.name || "غير محدد"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaUserTie className="inline ml-2 text-gray-500" />
                    المدير المباشر
                  </label>
                  <p className="text-gray-900">{user.manager?.name || "غير محدد"}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaCalendarAlt className="inline ml-2 text-blue-500" />
                    تاريخ الانضمام
                  </label>
                  <p className="text-gray-900">{new Date(user.createdAt).toLocaleDateString('ar-EG', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })} م</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaClock className="inline ml-2 text-green-500" />
                    آخر تحديث
                  </label>
                  <p className="text-gray-900">{new Date(user.updatedAt).toLocaleDateString('ar-EG', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })} م</p>
                </div>
              </div>
            </div>
          </div>

          {/* المرؤوسين */}
          {user.employees.length > 0 && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaUsers className="text-purple-600" />
                  المرؤوسين ({user.employees.length})
                </h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {user.employees.map((employee) => (
                    <div key={employee.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{employee.name}</p>
                        <p className="text-sm text-gray-500">{employee.position || "غير محدد"}</p>
                        {employee.employeeNumber && (
                          <p className="text-xs text-gray-400">#{employee.employeeNumber}</p>
                        )}
                      </div>
                      <Link
                        href={`/dashboard/users/${employee.id}`}
                        className="text-blue-600 hover:text-blue-800 p-2"
                        title="عرض التفاصيل"
                      >
                        <FaEye size={16} />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* الإحصائيات */}
        <div className="space-y-6">
          {/* إحصائيات النشاط */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <FaFileAlt className="text-green-600" />
                إحصائيات النشاط
              </h2>
            </div>
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <FaClock className="text-blue-600" />
                  <span className="text-sm font-medium">سجلات الحضور</span>
                </div>
                <span className="text-xl font-bold text-blue-600">{user._count.attendanceRecords}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <FaCalendarAlt className="text-green-600" />
                  <span className="text-sm font-medium">طلبات الإجازة</span>
                </div>
                <span className="text-xl font-bold text-green-600">{user._count.leaveRequests}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <FaUsers className="text-purple-600" />
                  <span className="text-sm font-medium">طلبات الزوار</span>
                </div>
                <span className="text-xl font-bold text-purple-600">{user._count.visitorRequests}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <FaClock className="text-orange-600" />
                  <span className="text-sm font-medium">العمل بعد الدوام</span>
                </div>
                <span className="text-xl font-bold text-orange-600">{user._count.afterHoursPermits}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <FaUsers className="text-gray-600" />
                  <span className="text-sm font-medium">المرؤوسين</span>
                </div>
                <span className="text-xl font-bold text-gray-600">{user._count.employees}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
