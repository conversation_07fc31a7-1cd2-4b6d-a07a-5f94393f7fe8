"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON>a<PERSON>ser, FaEnvelope, FaPhone, FaIdCard, FaUserShield, FaBuilding, FaUserTie, FaArrowLeft, FaLock } from "react-icons/fa";

interface Department {
  id: string;
  name: string;
}

interface Manager {
  id: string;
  name: string;
  employeeNumber?: string;
}

interface UserData {
  id: string;
  employeeNumber: string | null;
  name: string;
  email: string;
  phone: string;
  position: string | null;
  role: string;
  departmentId: string | null;
  managerId: string | null;
}

export default function EditUserPage({ params }: { params: Promise<{ id: string }> }) {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [managers, setManagers] = useState<Manager[]>([]);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);
  const [formData, setFormData] = useState({
    id: "",
    employeeNumber: "",
    name: "",
    email: "",
    phone: "",
    position: "",
    role: "EMPLOYEE",
    departmentId: "",
    managerId: "",
    password: ""
  });

  // تسميات الأدوار
  const roleLabels: Record<string, string> = {
    ADMIN: "مدير النظام",
    HR: "موارد بشرية", 
    MANAGER: "مدير",
    EMPLOYEE: "موظف",
    SECURITY: "أمن"
  };

  // جلب بيانات المستخدم
  const fetchUser = async (id: string) => {
    try {
      setFetchLoading(true);
      const response = await fetch(`/api/users/${id}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب بيانات المستخدم");
      }

      const userData = await response.json();
      setFormData({
        id: userData.id,
        employeeNumber: userData.employeeNumber || "",
        name: userData.name || "",
        email: userData.email || "",
        phone: userData.phone || "",
        position: userData.position || "",
        role: userData.role || "EMPLOYEE",
        departmentId: userData.departmentId || "",
        managerId: userData.managerId || "",
        password: ""
      });
    } catch (error) {
      console.error("خطأ في جلب بيانات المستخدم:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في جلب بيانات المستخدم");
      router.back();
    } finally {
      setFetchLoading(false);
    }
  };

  // جلب الأقسام والمدراء
  const fetchData = async () => {
    try {
      const [departmentsRes, employeesRes] = await Promise.all([
        fetch("/api/departments"),
        fetch("/api/employees"),
      ]);

      if (departmentsRes.ok) {
        const departmentsData = await departmentsRes.json();
        setDepartments(departmentsData);
      }

      if (employeesRes.ok) {
        const employeesData = await employeesRes.json();
        if (employeesData.employees) {
          // فلترة المدراء فقط واستبعاد المستخدم الحالي
          const managersData = employeesData.employees.filter((emp: any) =>
            emp.id !== resolvedParams?.id && (emp.role === "MANAGER" || emp.role === "HR" || emp.role === "ADMIN")
          );
          setManagers(managersData);
        }
      }
    } catch (error) {
      console.error("خطأ في جلب البيانات:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const updateData: any = {
        employeeNumber: formData.employeeNumber || null,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        position: formData.position || null,
        role: formData.role,
        departmentId: formData.departmentId || null,
        managerId: formData.managerId || null,
      };

      // إضافة كلمة المرور فقط إذا تم إدخالها
      if (formData.password && formData.password.trim() !== "") {
        if (formData.password.length < 6) {
          alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
          setLoading(false);
          return;
        }
        updateData.password = formData.password;
      }

      const response = await fetch(`/api/users/${formData.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        alert("تم تحديث المستخدم بنجاح" + (formData.password.trim() !== "" ? " وتم تغيير كلمة المرور" : ""));
        router.push(`/dashboard/users/${formData.id}`);
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تحديث المستخدم");
      }
    } catch (error) {
      console.error("خطأ في تحديث المستخدم:", error);
      alert("حدث خطأ في تحديث المستخدم");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
      fetchUser(resolvedParamsData.id);
    };
    loadParams();
  }, [params]);

  useEffect(() => {
    fetchData();
  }, [resolvedParams?.id]);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  if (fetchLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.back()}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <FaArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  تعديل المستخدم
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {formData.name}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <FaUser className="text-blue-600 dark:text-blue-400" size={20} />
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <FaUser className="text-blue-600" />
                المعلومات الأساسية
              </h3>

              {/* Employee Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaIdCard className="inline mr-2 text-gray-500" />
                  الرقم الوظيفي (اختياري)
                </label>
                <input
                  type="text"
                  name="employeeNumber"
                  value={formData.employeeNumber}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل الرقم الوظيفي"
                />
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaUser className="inline mr-2 text-gray-500" />
                  الاسم الكامل *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل الاسم الكامل"
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaEnvelope className="inline mr-2 text-gray-500" />
                  البريد الإلكتروني *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaPhone className="inline mr-2 text-gray-500" />
                  رقم الهاتف *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل رقم الهاتف"
                />
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaLock className="inline mr-2 text-gray-500" />
                  كلمة المرور الجديدة (اختياري)
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="اتركها فارغة لعدم التغيير"
                />
                <p className="text-sm text-gray-500 mt-1">
                  اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
                </p>
              </div>
            </div>

            {/* Work Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <FaBuilding className="text-blue-600" />
                المعلومات الوظيفية
              </h3>

              {/* Position */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaUserTie className="inline mr-2 text-gray-500" />
                  المنصب (اختياري)
                </label>
                <input
                  type="text"
                  name="position"
                  value={formData.position}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل المنصب"
                />
              </div>

              {/* Role */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaUserShield className="inline mr-2 text-gray-500" />
                  الدور *
                </label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {Object.entries(roleLabels).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Department */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaBuilding className="inline mr-2 text-gray-500" />
                  القسم (اختياري)
                </label>
                <select
                  name="departmentId"
                  value={formData.departmentId}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">اختر القسم</option>
                  {departments.map((department) => (
                    <option key={department.id} value={department.id}>
                      {department.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Manager */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FaUserTie className="inline mr-2 text-gray-500" />
                  المدير المباشر (اختياري)
                </label>
                <select
                  name="managerId"
                  value={formData.managerId}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">اختر المدير المباشر</option>
                  {managers.filter(manager => manager.id !== formData.id).map((manager) => (
                    <option key={manager.id} value={manager.id}>
                      {manager.name} {manager.employeeNumber && `(${manager.employeeNumber})`}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="mt-8 flex justify-end gap-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  جاري التحديث...
                </>
              ) : (
                <>
                  <FaUser size={16} />
                  تحديث المستخدم
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
