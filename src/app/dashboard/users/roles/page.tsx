"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  FaUserShield, 
  FaUsers, 
  FaEdit, 
  FaEye,
  FaArrowLeft,
  FaUser<PERSON>ie,
  <PERSON>a<PERSON>ser,
  FaUserCog
} from "react-icons/fa";

interface RoleStats {
  role: string;
  count: number;
  users: {
    id: string;
    name: string;
    email: string;
    employeeNumber?: string;
  }[];
}

export default function RolesPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [roleStats, setRoleStats] = useState<RoleStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تسميات الأدوار
  const roleLabels = {
    ADMIN: "مدير النظام",
    HR: "الموارد البشرية", 
    MANAGER: "مدير",
    EMPLOYEE: "موظف",
    SECURITY: "أمن"
  };

  // ألوان الأدوار
  const roleColors = {
    ADMIN: "bg-red-500",
    HR: "bg-blue-500",
    MANAGER: "bg-purple-500", 
    EMPLOYEE: "bg-green-500",
    SECURITY: "bg-orange-500"
  };

  // أيقونات الأدوار
  const roleIcons = {
    ADMIN: <FaUserShield size={24} />,
    HR: <FaUserCog size={24} />,
    MANAGER: <FaUserTie size={24} />,
    EMPLOYEE: <FaUser size={24} />,
    SECURITY: <FaUserShield size={24} />
  };

  // أوصاف الأدوار
  const roleDescriptions = {
    ADMIN: "صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات",
    HR: "إدارة الموظفين والأقسام والإجازات وطلبات الموارد البشرية",
    MANAGER: "إدارة الفريق ومراجعة الطلبات والتقارير الإدارية",
    EMPLOYEE: "الوصول الأساسي للنظام وتسجيل الحضور وتقديم الطلبات",
    SECURITY: "إدارة الزوار والأمن ومراقبة الدخول والخروج"
  };

  // جلب إحصائيات الأدوار
  const fetchRoleStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/users");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب بيانات المستخدمين");
      }

      const users = await response.json();
      
      // تجميع المستخدمين حسب الأدوار
      const stats: { [key: string]: RoleStats } = {};
      
      Object.keys(roleLabels).forEach(role => {
        stats[role] = {
          role,
          count: 0,
          users: []
        };
      });

      users.forEach((user: any) => {
        if (stats[user.role]) {
          stats[user.role].count++;
          stats[user.role].users.push({
            id: user.id,
            name: user.name,
            email: user.email,
            employeeNumber: user.employeeNumber
          });
        }
      });

      setRoleStats(Object.values(stats));
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب إحصائيات الأدوار:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoleStats();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const isAdmin = userRole === "ADMIN";

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام فقط</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <button
              onClick={() => router.back()}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <FaArrowLeft size={20} />
            </button>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <FaUserShield className="text-purple-600" />
              إدارة الأدوار والصلاحيات
            </h1>
          </div>
          <p className="text-gray-600 mr-8">عرض وإدارة أدوار المستخدمين وصلاحياتهم في النظام</p>
        </div>
      </div>

      {/* رسائل الخطأ */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
              <p className="text-3xl font-bold text-gray-900">
                {roleStats.reduce((total, role) => total + role.count, 0)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-blue-100">
              <FaUsers className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">عدد الأدوار</p>
              <p className="text-3xl font-bold text-gray-900">{Object.keys(roleLabels).length}</p>
            </div>
            <div className="p-3 rounded-full bg-purple-100">
              <FaUserShield className="text-purple-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المدراء</p>
              <p className="text-3xl font-bold text-gray-900">
                {roleStats.find(r => r.role === 'ADMIN')?.count || 0}
              </p>
            </div>
            <div className="p-3 rounded-full bg-red-100">
              <FaUserShield className="text-red-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* بطاقات الأدوار */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roleStats.map((roleStat) => (
          <div key={roleStat.role} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            {/* Header */}
            <div className={`${roleColors[roleStat.role as keyof typeof roleColors]} text-white p-6`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {roleIcons[roleStat.role as keyof typeof roleIcons]}
                  <div>
                    <h3 className="text-xl font-bold">
                      {roleLabels[roleStat.role as keyof typeof roleLabels]}
                    </h3>
                    <p className="text-sm opacity-90">{roleStat.count} مستخدم</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* الوصف */}
              <p className="text-gray-600 text-sm mb-4">
                {roleDescriptions[roleStat.role as keyof typeof roleDescriptions]}
              </p>

              {/* قائمة المستخدمين */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 mb-3">المستخدمون:</h4>
                {roleStat.users.length === 0 ? (
                  <p className="text-gray-500 text-sm italic">لا يوجد مستخدمون بهذا الدور</p>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {roleStat.users.map((user) => (
                      <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{user.name}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                          {user.employeeNumber && (
                            <p className="text-xs text-gray-400">#{user.employeeNumber}</p>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <button
                            onClick={() => router.push(`/dashboard/users/${user.id}`)}
                            className="text-blue-600 hover:text-blue-800 p-1"
                            title="عرض التفاصيل"
                          >
                            <FaEye size={14} />
                          </button>
                          <button
                            onClick={() => router.push(`/dashboard/users/${user.id}/edit`)}
                            className="text-green-600 hover:text-green-800 p-1"
                            title="تعديل"
                          >
                            <FaEdit size={14} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex items-start gap-3">
          <FaUserShield className="text-blue-600 mt-1" />
          <div>
            <h4 className="text-lg font-semibold text-blue-800 mb-2">معلومات حول الأدوار</h4>
            <div className="text-sm text-blue-700 space-y-2">
              <p>• <strong>مدير النظام:</strong> يمكنه الوصول إلى جميع أجزاء النظام وإدارة المستخدمين والإعدادات</p>
              <p>• <strong>الموارد البشرية:</strong> يمكنه إدارة الموظفين والأقسام ومراجعة طلبات الإجازات</p>
              <p>• <strong>المدير:</strong> يمكنه إدارة فريقه ومراجعة طلبات المرؤوسين</p>
              <p>• <strong>الموظف:</strong> يمكنه تسجيل الحضور وتقديم الطلبات الشخصية</p>
              <p>• <strong>الأمن:</strong> يمكنه إدارة الزوار ومراقبة الدخول والخروج</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
