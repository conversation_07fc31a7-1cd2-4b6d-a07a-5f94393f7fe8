"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { FaUser, FaEnvelope, FaPhone, FaIdCard, FaUserShield, FaBuilding, FaUserTie, FaLock } from "react-icons/fa";

interface Department {
  id: string;
  name: string;
}

interface Manager {
  id: string;
  name: string;
  employeeNumber?: string;
}

export default function NewUserPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [managers, setManagers] = useState<Manager[]>([]);
  const [formData, setFormData] = useState({
    employeeNumber: "",
    name: "",
    email: "",
    phone: "",
    position: "",
    password: "",
    confirmPassword: "",
    role: "EMPLOYEE",
    departmentId: "",
    managerId: ""
  });

  // تسميات الأدوار
  const roleOptions = [
    { value: "EMPLOYEE", label: "موظف", icon: <FaUser className="text-green-500" /> },
    { value: "MANAGER", label: "مدير", icon: <FaUserTie className="text-purple-500" /> },
    { value: "HR", label: "الموارد البشرية", icon: <FaUserShield className="text-blue-500" /> },
    { value: "SECURITY", label: "أمن", icon: <FaUserShield className="text-orange-500" /> },
    { value: "ADMIN", label: "مدير النظام", icon: <FaUserShield className="text-red-500" /> },
  ];

  // جلب الأقسام والمدراء
  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب الأقسام
        const deptResponse = await fetch("/api/departments");
        if (deptResponse.ok) {
          const deptData = await deptResponse.json();
          setDepartments(deptData);
        }

        // جلب المدراء (المستخدمين الذين لديهم دور مدير أو أعلى)
        const managersResponse = await fetch("/api/employees?role=MANAGER,HR,ADMIN");
        if (managersResponse.ok) {
          const managersData = await managersResponse.json();
          setManagers(managersData);
        }
      } catch (error) {
        console.error("خطأ في جلب البيانات:", error);
      }
    };

    fetchData();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من تطابق كلمات المرور
    if (formData.password !== formData.confirmPassword) {
      alert("كلمات المرور غير متطابقة");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          employeeNumber: formData.employeeNumber || null,
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          position: formData.position || null,
          password: formData.password,
          role: formData.role,
          departmentId: formData.departmentId || null,
          managerId: formData.managerId || null,
        }),
      });

      if (response.ok) {
        router.push("/dashboard/users");
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في إنشاء المستخدم");
      }
    } catch (error) {
      console.error("خطأ في إنشاء المستخدم:", error);
      alert("حدث خطأ في إنشاء المستخدم");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const isAdmin = userRole === "ADMIN";

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام فقط</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إضافة مستخدم جديد</h1>
          <p className="text-gray-600 mt-1">إنشاء حساب مستخدم جديد في النظام</p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          العودة
        </button>
      </div>

      {/* Form */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">معلومات المستخدم</h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* الصف الأول - المعلومات الأساسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* رقم الموظف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaIdCard className="inline ml-2 text-blue-500" />
                رقم الموظف
              </label>
              <input
                type="text"
                name="employeeNumber"
                value={formData.employeeNumber}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل رقم الموظف (اختياري)"
              />
            </div>

            {/* الاسم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUser className="inline ml-2 text-green-500" />
                الاسم الكامل *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل الاسم الكامل"
              />
            </div>
          </div>

          {/* الصف الثاني - الاتصال */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* البريد الإلكتروني */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaEnvelope className="inline ml-2 text-red-500" />
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>

            {/* رقم الهاتف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaPhone className="inline ml-2 text-purple-500" />
                رقم الهاتف
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل رقم الهاتف"
              />
            </div>
          </div>

          {/* الصف الثالث - المنصب والدور */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* المنصب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUserTie className="inline ml-2 text-indigo-500" />
                المنصب
              </label>
              <input
                type="text"
                name="position"
                value={formData.position}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل المنصب الوظيفي"
              />
            </div>

            {/* الدور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUserShield className="inline ml-2 text-orange-500" />
                الدور في النظام *
              </label>
              <select
                name="role"
                value={formData.role}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {roleOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* الصف الرابع - القسم والمدير */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* القسم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaBuilding className="inline ml-2 text-teal-500" />
                القسم
              </label>
              <select
                name="departmentId"
                value={formData.departmentId}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر القسم</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </select>
            </div>

            {/* المدير المباشر */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUserTie className="inline ml-2 text-gray-500" />
                المدير المباشر
              </label>
              <select
                name="managerId"
                value={formData.managerId}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر المدير المباشر</option>
                {managers.map((manager) => (
                  <option key={manager.id} value={manager.id}>
                    {manager.name} {manager.employeeNumber && `(${manager.employeeNumber})`}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* الصف الخامس - كلمات المرور */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* كلمة المرور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaLock className="inline ml-2 text-red-500" />
                كلمة المرور *
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                minLength={6}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل كلمة المرور"
              />
            </div>

            {/* تأكيد كلمة المرور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaLock className="inline ml-2 text-red-500" />
                تأكيد كلمة المرور *
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                required
                minLength={6}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أعد إدخال كلمة المرور"
              />
            </div>
          </div>

          {/* ملاحظة أمنية */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <FaUserShield className="text-blue-600 mt-1" />
              <div>
                <h4 className="text-sm font-medium text-blue-800">ملاحظة أمنية</h4>
                <p className="text-sm text-blue-700 mt-1">
                  • كلمة المرور يجب أن تكون 6 أحرف على الأقل<br/>
                  • سيتم إشعار المستخدم بتفاصيل الحساب عبر البريد الإلكتروني<br/>
                  • يمكن إعادة تعيين كلمة المرور لاحقاً من صفحة إدارة المستخدمين
                </p>
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 pt-6 border-t border-gray-100">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الإنشاء...
                </>
              ) : (
                <>
                  <FaUser />
                  إنشاء المستخدم
                </>
              )}
            </button>
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
