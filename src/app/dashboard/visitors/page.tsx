"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FaPlus, FaEdit, FaTrash, FaUsers, FaCheck, FaTimes, FaSignInAlt, FaSignOutAlt } from "react-icons/fa";
import Link from "next/link";
import { DataTable, Column } from "@/components/ui/DataTable";
import { formatDate } from "@/lib/utils/dateUtils";

interface VisitorRequest {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
    employeeNumber: string | null;
  };
  visitorName: string;
  visitorCompany: string | null;
  purpose: string;
  visitDate: string;
  visitTime: string | null;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: "PENDING" | "APPROVED" | "REJECTED" | "CANCELLED";
  createdAt: string;
  updatedAt: string;
}



export default function VisitorsPage() {
  const { data: session } = useSession();
  const [visitors, setVisitors] = useState<VisitorRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // تعريف أعمدة الجدول
  const columns: Column<VisitorRequest>[] = [
    {
      key: 'visitorName',
      title: 'اسم الزائر',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.visitorCompany && (
            <div className="text-sm text-gray-500">{row.visitorCompany}</div>
          )}
        </div>
      ),
    },
    {
      key: 'user.name',
      title: 'الموظف المضيف',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.user.employeeNumber && (
            <div className="text-sm text-gray-500">#{row.user.employeeNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'purpose',
      title: 'الغرض من الزيارة',
      searchable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      key: 'companions',
      title: 'المرافقون',
      render: (value) => value ? (
        <span className="text-sm text-gray-600" title={value}>
          {value.length > 30 ? `${value.substring(0, 30)}...` : value}
        </span>
      ) : (
        <span className="text-gray-400 text-sm">لا يوجد</span>
      ),
    },
    {
      key: 'vehicleInfo',
      title: 'بيانات السيارة',
      render: (value) => value ? (
        <span className="text-sm text-gray-600" title={value}>
          {value.length > 25 ? `${value.substring(0, 25)}...` : value}
        </span>
      ) : (
        <span className="text-gray-400 text-sm">لا يوجد</span>
      ),
    },
    {
      key: 'visitDate',
      title: 'تاريخ الزيارة',
      sortable: true,
      render: (value) => <span className="text-blue-600">{formatDate(value)}</span>,
    },
    {
      key: 'visitTime',
      title: 'الوقت المتوقع',
      render: (value) => value ? (
        <span className="text-purple-600 font-medium">{value}</span>
      ) : (
        <span className="text-gray-400 text-sm">غير محدد</span>
      ),
    },
    {
      key: 'checkInTime',
      title: 'وقت الدخول',
      render: (value) => (
        value ? (
          <span className="text-green-600">{new Date(value).toLocaleTimeString('ar-SA')}</span>
        ) : (
          <span className="text-gray-400">لم يدخل بعد</span>
        )
      ),
    },
    {
      key: 'checkOutTime',
      title: 'وقت الخروج',
      render: (value) => (
        value ? (
          <span className="text-red-600">{new Date(value).toLocaleTimeString('ar-SA')}</span>
        ) : (
          <span className="text-gray-400">لم يخرج بعد</span>
        )
      ),
    },

    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_, row) => (
        <div className="flex justify-center gap-2">
          {/* إزالة أزرار الموافقة والرفض - يتم التعامل معها في صفحة منفصلة */}
          {(canManageVisitors || row.userId === session?.user?.id) && (
            <Link
              href={`/dashboard/visitors/${row.id}/edit`}
              className="text-blue-600 hover:text-blue-800 p-2"
              title="تعديل"
            >
              <FaEdit size={16} />
            </Link>
          )}
          {(canManageVisitors || row.userId === session?.user?.id) && (
            <button
              onClick={() => handleDelete(row.id)}
              disabled={actionLoading === row.id}
              className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
              title="حذف"
            >
              {actionLoading === row.id ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
              ) : (
                <FaTrash size={16} />
              )}
            </button>
          )}
        </div>
      ),
    },
  ];

  // جلب طلبات الزوار
  const fetchVisitors = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/visitors");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب طلبات الزوار");
      }

      const data = await response.json();
      setVisitors(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب طلبات الزوار:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // تحديث حالة الطلب
  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/visitors/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تحديث حالة الطلب");
      }

      await fetchVisitors();
      alert(`تم ${status === 'APPROVED' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`);
    } catch (error) {
      console.error("خطأ في تحديث حالة الطلب:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في تحديث حالة الطلب");
    } finally {
      setActionLoading(null);
    }
  };

  // تسجيل دخول الزائر
  const handleCheckIn = async (id: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/visitors/${id}/checkin`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تسجيل دخول الزائر");
      }

      await fetchVisitors();
      alert("تم تسجيل دخول الزائر بنجاح");
    } catch (error) {
      console.error("خطأ في تسجيل دخول الزائر:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في تسجيل دخول الزائر");
    } finally {
      setActionLoading(null);
    }
  };

  // تسجيل خروج الزائر
  const handleCheckOut = async (id: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/visitors/${id}/checkout`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تسجيل خروج الزائر");
      }

      await fetchVisitors();
      alert("تم تسجيل خروج الزائر بنجاح");
    } catch (error) {
      console.error("خطأ في تسجيل خروج الزائر:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في تسجيل خروج الزائر");
    } finally {
      setActionLoading(null);
    }
  };

  // حذف طلب الزائر
  const handleDelete = async (id: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا الطلب؟")) {
      return;
    }

    try {
      setActionLoading(id);
      const response = await fetch(`/api/visitors/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف الطلب");
      }

      await fetchVisitors();
      alert("تم حذف الطلب بنجاح");
    } catch (error) {
      console.error("خطأ في حذف الطلب:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف الطلب");
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchVisitors();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManageVisitors = userRole === "ADMIN" || userRole === "HR" || userRole === "MANAGER" || userRole === "SECURITY";

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة الزوار</h1>
        <Link
          href="/dashboard/visitors/new"
          className="btn-primary flex items-center gap-2"
        >
          <FaPlus size={16} />
          طلب زائر جديد
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {visitors.length === 0 && !loading ? (
        <div className="card">
          <div className="text-center py-8">
            <FaUsers size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد طلبات زوار</p>
            <Link
              href="/dashboard/visitors/new"
              className="btn-primary"
            >
              إضافة أول طلب زائر
            </Link>
          </div>
        </div>
      ) : (
        <DataTable
          data={visitors}
          columns={columns}
          loading={loading}
          searchPlaceholder="البحث في طلبات الزوار..."
          pageSize={10}
        />
      )}
    </div>
  );
}
