"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { FaUser, FaBuilding, FaPhone, FaIdCard, FaCalendarAlt, FaClock } from "react-icons/fa";

export default function NewVisitorPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    visitorName: "",
    visitorCompany: "",
    purpose: "",
    visitDate: "",
    visitTime: "",
    companions: "",
    vehicleInfo: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/visitors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error("خطأ في تحليل الاستجابة:", parseError);
        throw new Error("استجابة غير صالحة من الخادم");
      }

      if (response.ok) {
        // Success - redirect to visitors list
        alert("تم حفظ طلب الزائر بنجاح");
        router.push("/dashboard/visitors");
        return; // Exit early to prevent setting loading to false
      } else {
        // Handle API errors
        const errorMessage = data?.error || `خطأ ${response.status}: حدث خطأ في إضافة الزائر`;
        setError(errorMessage);
        alert(errorMessage);
      }
    } catch (error) {
      console.error("خطأ في إضافة الزائر:", error);
      const errorMessage = error instanceof Error ? error.message : "حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.";
      setError(errorMessage);
      alert(errorMessage);
    }
    
    setLoading(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    // Clear any previous errors when user starts typing
    if (error) {
      setError(null);
    }
    
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const resetForm = () => {
    setFormData({
      visitorName: "",
      visitorCompany: "",
      purpose: "",
      visitDate: "",
      visitTime: "",
      companions: "",
      vehicleInfo: ""
    });
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إضافة زائر جديد</h1>
          <p className="text-gray-600 mt-1">تسجيل زيارة جديدة للمؤسسة</p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          العودة
        </button>
      </div>

      {/* Form */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">معلومات الزائر</h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* عرض الأخطاء */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="text-sm">{error}</p>
            </div>
          )}

          {/* الصف الأول */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* اسم الزائر */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUser className="inline ml-2" />
                اسم الزائر *
              </label>
              <input
                type="text"
                name="visitorName"
                value={formData.visitorName}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل اسم الزائر"
              />
            </div>

            {/* الشركة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaBuilding className="inline ml-2" />
                الشركة/المؤسسة
              </label>
              <input
                type="text"
                name="visitorCompany"
                value={formData.visitorCompany}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل اسم الشركة"
              />
            </div>
          </div>

          {/* الصف الثاني */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* تاريخ الزيارة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaCalendarAlt className="inline ml-2" />
                تاريخ الزيارة *
              </label>
              <input
                type="date"
                name="visitDate"
                value={formData.visitDate}
                onChange={handleChange}
                required
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* وقت الزيارة المتوقع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaClock className="inline ml-2" />
                وقت الزيارة المتوقع
              </label>
              <input
                type="time"
                name="visitTime"
                value={formData.visitTime}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="اختر وقت الزيارة"
              />
              <p className="text-xs text-gray-500 mt-1">الوقت المتوقع لوصول الزائر (اختياري)</p>
            </div>

            {/* غرض الزيارة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                غرض الزيارة *
              </label>
              <select
                name="purpose"
                value={formData.purpose}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر غرض الزيارة</option>
                <option value="اجتماع عمل">اجتماع عمل</option>
                <option value="مقابلة شخصية">مقابلة شخصية</option>
                <option value="استشارة">استشارة</option>
                <option value="تسليم مستندات">تسليم مستندات</option>
                <option value="زيارة رسمية">زيارة رسمية</option>
                <option value="صيانة">صيانة</option>
                <option value="توريد">توريد</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>
          </div>

          {/* الصف الثالث - الحقول الجديدة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* المرافقون */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaUser className="inline ml-2 text-green-500" />
                المرافقون
              </label>
              <textarea
                name="companions"
                value={formData.companions}
                onChange={handleChange}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل أسماء المرافقين (إن وجدوا)..."
              />
              <p className="text-xs text-gray-500 mt-1">اكتب أسماء المرافقين مفصولة بفواصل</p>
            </div>

            {/* بيانات السيارة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <svg className="inline ml-2 w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                </svg>
                بيانات السيارة
              </label>
              <textarea
                name="vehicleInfo"
                value={formData.vehicleInfo}
                onChange={handleChange}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="رقم اللوحة، نوع السيارة، اللون..."
              />
              <p className="text-xs text-gray-500 mt-1">مثال: ABC-123، تويوتا كامري، أبيض</p>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 pt-6 border-t border-gray-100">
            <button
              type="submit"
              disabled={loading || !formData.visitorName || !formData.purpose || !formData.visitDate}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الحفظ...
                </>
              ) : (
                "حفظ الزيارة"
              )}
            </button>
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
