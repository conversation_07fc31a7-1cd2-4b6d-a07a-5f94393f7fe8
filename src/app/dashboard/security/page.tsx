"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  FaShieldAlt,
  FaUserCheck,
  FaUserTimes,
  FaUsers,
  FaClock,
  FaCalendarDay,
  FaExclamationTriangle,
  FaTimesCircle,
  FaEye,
  FaPlus,
  FaMoon
} from "react-icons/fa";

interface Employee {
  id: string;
  name: string;
  employeeNumber: string;
  position: string;
  department: {
    name: string;
  } | null;
}

interface VisitorRequest {
  id: string;
  visitorName: string;
  visitorCompany: string;
  purpose: string;
  visitDate: string;
  companions: string;
  vehicleInfo: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: string;
  user: {
    name: string;
    employeeNumber: string;
  };
}

interface AfterHoursPermit {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: string;
  user: {
    name: string;
    employeeNumber: string;
  };
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkInTime: string;
  checkOutTime: string | null;
  exitType: string | null;
  entryNumber: number;
  user: {
    name: string;
    employeeNumber: string;
  };
}

export default function SecurityDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [todayVisitors, setTodayVisitors] = useState<VisitorRequest[]>([]);
  const [approvedPermits, setApprovedPermits] = useState<AfterHoursPermit[]>([]);
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord[]>([]);

  // التحقق من صلاحية الأمن
  useEffect(() => {
    if (session?.user?.role !== "SECURITY") {
      router.push("/dashboard");
      return;
    }
    fetchData();
  }, [session, router]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // جلب الموظفين
      const employeesRes = await fetch("/api/employees");
      if (employeesRes.ok) {
        const employeesData = await employeesRes.json();
        setEmployees(employeesData);
      }

      // جلب زوار اليوم
      const visitorsRes = await fetch("/api/security/today-visitors");
      if (visitorsRes.ok) {
        const visitorsData = await visitorsRes.json();
        setTodayVisitors(visitorsData);
      }

      // جلب تصاريح ما بعد العمل المعتمدة
      const permitsRes = await fetch("/api/security/approved-permits");
      if (permitsRes.ok) {
        const permitsData = await permitsRes.json();
        setApprovedPermits(permitsData);
      }

      // جلب حضور اليوم
      const attendanceRes = await fetch("/api/security/today-attendance");
      if (attendanceRes.ok) {
        const attendanceData = await attendanceRes.json();
        setTodayAttendance(attendanceData);
      }

    } catch (error) {
      console.error("خطأ في جلب البيانات:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل لوحة تحكم الأمن...</p>
        </div>
      </div>
    );
  }

  // إحصائيات سريعة
  const stats = {
    presentEmployees: todayAttendance.filter(a => !a.checkOutTime).length,
    todayVisitorsCount: todayVisitors.length,
    activeVisitors: todayVisitors.filter(v => v.checkInTime && !v.checkOutTime).length,
    pendingPermits: approvedPermits.filter(p => p.status === "APPROVED" && !p.checkInTime).length,
  };

  // أقسام التنقل الرئيسية
  const navigationSections = [
    {
      title: "حضور وانصراف الموظفين",
      description: "تسجيل دخول وخروج الموظفين",
      icon: <FaUserCheck size={24} />,
      href: "/dashboard/security/employee-attendance",
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600"
    },
    {
      title: "إدارة الزوار",
      description: "تسجيل دخول وخروج الزوار",
      icon: <FaUsers size={24} />,
      href: "/dashboard/security/visitor-checkin",
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600"
    },
    {
      title: "العمل بعد الدوام",
      description: "تسجيل تصاريح ما بعد العمل",
      icon: <FaMoon size={24} />,
      href: "/dashboard/security/after-hours",
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-white/20 rounded-full">
            <FaShieldAlt size={32} />
          </div>
          <div>
            <h1 className="text-3xl font-bold">لوحة تحكم الأمن</h1>
            <p className="text-blue-100 mt-1">
              إدارة دخول وخروج الموظفين والزوار - {new Date().toLocaleDateString('en-GB')}
            </p>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الموظفون الحاضرون</p>
              <p className="text-2xl font-bold text-gray-900">{stats.presentEmployees}</p>
            </div>
            <div className="p-3 rounded-full bg-green-100">
              <FaUserCheck className="text-green-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">زوار اليوم</p>
              <p className="text-2xl font-bold text-gray-900">{stats.todayVisitorsCount}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100">
              <FaUsers className="text-blue-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">زوار داخل المبنى</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeVisitors}</p>
            </div>
            <div className="p-3 rounded-full bg-purple-100">
              <FaUserTimes className="text-purple-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">تصاريح معلقة</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingPermits}</p>
            </div>
            <div className="p-3 rounded-full bg-orange-100">
              <FaClock className="text-orange-600" size={20} />
            </div>
          </div>
        </div>
      </div>

      {/* أقسام التنقل الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {navigationSections.map((section, index) => (
          <Link
            key={index}
            href={section.href}
            className={`block bg-white rounded-xl shadow-lg ${section.hoverColor} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}
          >
            <div className="p-6">
              <div className="flex items-center gap-4">
                <div className={`p-4 ${section.color} text-white rounded-lg`}>
                  {section.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{section.title}</h3>
                  <p className="text-gray-600">{section.description}</p>
                </div>
                <div className="text-gray-400">
                  <FaEye size={20} />
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* قائمة الموظفين الحاضرين */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
          <FaUserCheck className="text-green-600" />
          الموظفون الحاضرون حالياً ({stats.presentEmployees})
        </h2>
        {stats.presentEmployees > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {todayAttendance
              .filter(record => !record.checkOutTime)
              .slice(0, 9) // عرض أول 9 موظفين فقط
              .map((record) => (
                <div key={record.id} className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <div className="p-2 bg-green-100 rounded-full">
                    <FaUserCheck className="text-green-600" size={16} />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{record.user.name}</p>
                    <p className="text-sm text-gray-600">رقم: {record.user.employeeNumber}</p>
                    <p className="text-xs text-green-600">
                      دخل في: {new Date(record.checkInTime).toLocaleTimeString('ar-SA')}
                    </p>
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaUserTimes size={48} className="mx-auto mb-4 text-gray-300" />
            <p>لا يوجد موظفون حاضرون حالياً</p>
          </div>
        )}
        {stats.presentEmployees > 9 && (
          <div className="mt-4 text-center">
            <Link
              href="/dashboard/security/employee-attendance"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              عرض جميع الموظفين الحاضرين ({stats.presentEmployees})
            </Link>
          </div>
        )}
      </div>

      {/* الزوار النشطون */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
          <FaUsers className="text-blue-600" />
          الزوار داخل المبنى ({stats.activeVisitors})
        </h2>
        {stats.activeVisitors > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {todayVisitors
              .filter(visitor => visitor.checkInTime && !visitor.checkOutTime)
              .slice(0, 6)
              .map((visitor) => (
                <div key={visitor.id} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <FaUsers className="text-blue-600" size={16} />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{visitor.visitorName}</p>
                    <p className="text-sm text-gray-600">{visitor.visitorCompany}</p>
                    <p className="text-xs text-blue-600">
                      دخل في: {visitor.checkInTime ? new Date(visitor.checkInTime).toLocaleTimeString('ar-SA') : 'لم يسجل دخول'}
                    </p>
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaUsers size={48} className="mx-auto mb-4 text-gray-300" />
            <p>لا يوجد زوار داخل المبنى حالياً</p>
          </div>
        )}
        {stats.activeVisitors > 6 && (
          <div className="mt-4 text-center">
            <Link
              href="/dashboard/security/visitor-checkin"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              عرض جميع الزوار النشطين ({stats.activeVisitors})
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
