"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>a<PERSON>lock,
  FaUserCheck,
  FaUserTimes,
  FaSearch,
  FaCalendarDay,
  FaArrowLeft,
  FaCheckCircle,
  FaTimesCircle,
  FaUser,
  FaBuilding
} from "react-icons/fa";

interface AfterHoursPermit {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: string;
  user: {
    name: string;
    employeeNumber: string;
    position?: string;
    department?: {
      name: string;
    } | null;
  };
}

export default function AfterHoursPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [permits, setPermits] = useState<AfterHoursPermit[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // التحقق من صلاحية الأمن
  useEffect(() => {
    if (session?.user?.role !== "SECURITY") {
      router.push("/dashboard");
      return;
    }
    fetchPermits();
  }, [session, router]);

  const fetchPermits = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch("/api/security/approved-permits");
      
      if (response.ok) {
        const data = await response.json();
        
        // التحقق من صحة البيانات
        if (Array.isArray(data)) {
          setPermits(data);
        } else {
          console.error("البيانات المُستلمة ليست مصفوفة:", data);
          setError("تنسيق البيانات غير صحيح");
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || "فشل في جلب البيانات");
      }
    } catch (error) {
      console.error("خطأ في جلب التصاريح:", error);
      setError("حدث خطأ في الاتصال بالخادم");
    } finally {
      setLoading(false);
    }
  };

  const handleCheckIn = async (permitId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch("/api/security/after-hours-checkin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ permitId }),
      });

      if (response.ok) {
        fetchPermits();
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل الدخول");
      }
    } catch (error) {
      console.error("خطأ في تسجيل الدخول:", error);
      alert("حدث خطأ في تسجيل الدخول");
    } finally {
      setActionLoading(false);
    }
  };

  const handleCheckOut = async (permitId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch("/api/security/after-hours-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ permitId }),
      });

      if (response.ok) {
        fetchPermits();
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل الخروج");
      }
    } catch (error) {
      console.error("خطأ في تسجيل الخروج:", error);
      alert("حدث خطأ في تسجيل الخروج");
    } finally {
      setActionLoading(false);
    }
  };

  // تصفية التصاريح مع معالجة الأخطاء
  const filteredPermits = permits.filter(permit => {
    try {
      if (!permit || !permit.user) return false;
      
      const searchLower = searchTerm.toLowerCase();
      return (
        (permit.user.name || '').toLowerCase().includes(searchLower) ||
        (permit.user.employeeNumber || '').toLowerCase().includes(searchLower) ||
        (permit.reason || '').toLowerCase().includes(searchLower)
      );
    } catch (error) {
      console.error("خطأ في تصفية التصريح:", permit, error);
      return false;
    }
  });

  // تصنيف التصاريح مع معالجة الأخطاء
  const pendingPermits = filteredPermits.filter(p => {
    try {
      return p && !p.checkInTime;
    } catch (error) {
      console.error("خطأ في تصنيف التصريح المعلق:", p, error);
      return false;
    }
  });
  
  const activePermits = filteredPermits.filter(p => {
    try {
      return p && p.checkInTime && !p.checkOutTime;
    } catch (error) {
      console.error("خطأ في تصنيف التصريح النشط:", p, error);
      return false;
    }
  });
  
  const completedPermits = filteredPermits.filter(p => {
    try {
      return p && p.checkInTime && p.checkOutTime;
    } catch (error) {
      console.error("خطأ في تصنيف التصريح المكتمل:", p, error);
      return false;
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التصاريح...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-orange-600 to-orange-800 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-full">
                <FaClock size={32} />
              </div>
              <div>
                <h1 className="text-3xl font-bold">العمل بعد الدوام الرسمي</h1>
                <p className="text-orange-100 mt-1">حدث خطأ في تحميل البيانات</p>
              </div>
            </div>
            <button
              onClick={() => router.push("/dashboard/security")}
              className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              <FaArrowLeft />
              العودة
            </button>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <FaTimesCircle className="text-red-500" size={24} />
            <div>
              <h3 className="text-lg font-semibold text-red-800">خطأ في تحميل البيانات</h3>
              <p className="text-red-600 mt-1">{error}</p>
              <button
                onClick={fetchPermits}
                className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const PermitCard = ({ permit, showCheckIn = false, showCheckOut = false }: {
    permit: AfterHoursPermit;
    showCheckIn?: boolean;
    showCheckOut?: boolean;
  }) => {
    // التحقق من صحة البيانات
    if (!permit || !permit.user) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">خطأ في بيانات التصريح</p>
        </div>
      );
    }

    return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-orange-100 rounded-full">
            <FaUser className="text-orange-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{permit.user.name}</h3>
            <p className="text-sm text-gray-600">رقم الموظف: {permit.user.employeeNumber}</p>
            <p className="text-sm text-gray-500">
              {permit.user.department?.name || 'غير محدد'} - {permit.user.position || 'غير محدد'}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2">
          <FaClock className="text-gray-400" size={14} />
          <span className="text-sm text-gray-600">
            من {permit.startTime ? new Date(permit.startTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }) : 'غير محدد'}
            إلى {permit.endTime ? new Date(permit.endTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }) : 'غير محدد'}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <FaBuilding className="text-gray-400" size={14} />
          <span className="text-sm text-gray-600">السبب: {permit.reason}</span>
        </div>

        {permit.checkInTime && (
          <div className="flex items-center gap-2">
            <FaUserCheck className="text-green-500" size={14} />
            <span className="text-sm text-green-600">
              دخول: {new Date(permit.checkInTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        )}

        {permit.checkOutTime && (
          <div className="flex items-center gap-2">
            <FaUserTimes className="text-red-500" size={14} />
            <span className="text-sm text-red-600">
              خروج: {new Date(permit.checkOutTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        )}
      </div>

      <div className="flex gap-2">
        {showCheckIn && (
          <button
            onClick={() => handleCheckIn(permit.id)}
            disabled={actionLoading}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
          >
            <FaUserCheck />
            تسجيل الدخول
          </button>
        )}

        {showCheckOut && (
          <button
            onClick={() => handleCheckOut(permit.id)}
            disabled={actionLoading}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
          >
            <FaUserTimes />
            تسجيل الخروج
          </button>
        )}
      </div>
    </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 to-orange-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-white/20 rounded-full">
              <FaClock size={32} />
            </div>
            <div>
              <h1 className="text-3xl font-bold">العمل بعد الدوام الرسمي</h1>
              <p className="text-orange-100 mt-1">
                إدارة تصاريح العمل بعد الدوام - {new Date().toLocaleDateString('en-GB')}
              </p>
            </div>
          </div>
          <button
            onClick={() => router.push("/dashboard/security")}
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaArrowLeft />
            العودة
          </button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">في انتظار الدخول</p>
              <p className="text-2xl font-bold text-gray-900">{pendingPermits.length}</p>
            </div>
            <div className="p-3 rounded-full bg-orange-100">
              <FaClock className="text-orange-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">يعملون حالياً</p>
              <p className="text-2xl font-bold text-gray-900">{activePermits.length}</p>
            </div>
            <div className="p-3 rounded-full bg-green-100">
              <FaUserCheck className="text-green-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-gray-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">انتهى العمل</p>
              <p className="text-2xl font-bold text-gray-900">{completedPermits.length}</p>
            </div>
            <div className="p-3 rounded-full bg-gray-100">
              <FaCheckCircle className="text-gray-600" size={20} />
            </div>
          </div>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="relative">
          <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث بالاسم أو رقم الموظف أو السبب..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* قوائم التصاريح */}
      <div className="space-y-8">
        {/* التصاريح في انتظار الدخول */}
        {pendingPermits.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaClock className="text-orange-500" />
              في انتظار الدخول ({pendingPermits.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pendingPermits.map((permit) => (
                <PermitCard
                  key={permit.id}
                  permit={permit}
                  showCheckIn={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* الموظفون الذين يعملون حالياً */}
        {activePermits.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaUserCheck className="text-green-500" />
              يعملون حالياً ({activePermits.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activePermits.map((permit) => (
                <PermitCard
                  key={permit.id}
                  permit={permit}
                  showCheckOut={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* الموظفون الذين انتهوا من العمل */}
        {completedPermits.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaCheckCircle className="text-gray-500" />
              انتهى العمل ({completedPermits.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {completedPermits.map((permit) => (
                <PermitCard
                  key={permit.id}
                  permit={permit}
                />
              ))}
            </div>
          </div>
        )}

        {/* رسالة عدم وجود تصاريح */}
        {filteredPermits.length === 0 && (
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-12 text-center">
            <FaClock className="mx-auto text-gray-300 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد تصاريح عمل بعد الدوام</h3>
            <p className="text-gray-600">لم يتم الموافقة على أي تصاريح عمل بعد الدوام لهذا اليوم</p>
          </div>
        )}
      </div>
    </div>
  );
}
