"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  FaUsers,
  FaUserCheck,
  FaUserTimes,
  FaSearch,
  FaClock,
  FaBuilding,
  FaArrowLeft,
  FaCheckCircle,
  FaTimesCircle,
  FaCar,
  FaUserFriends
} from "react-icons/fa";

interface VisitorRequest {
  id: string;
  visitorName: string;
  visitorCompany: string;
  purpose: string;
  visitDate: string;
  visitTime: string | null;
  companions: string;
  vehicleInfo: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: string;
  user: {
    name: string;
    employeeNumber: string;
  };
}

export default function VisitorCheckinPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [visitors, setVisitors] = useState<VisitorRequest[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [actionLoading, setActionLoading] = useState(false);

  // التحقق من صلاحية الأمن
  useEffect(() => {
    if (session?.user?.role !== "SECURITY") {
      router.push("/dashboard");
      return;
    }
    fetchVisitors();
  }, [session, router]);

  const fetchVisitors = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/security/today-visitors");
      if (response.ok) {
        const data = await response.json();
        setVisitors(data);
      }
    } catch (error) {
      console.error("خطأ في جلب الزوار:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleVisitorCheckIn = async (visitorId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch("/api/security/visitor-checkin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ visitorId }),
      });

      if (response.ok) {
        fetchVisitors();
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل دخول الزائر");
      }
    } catch (error) {
      console.error("خطأ في تسجيل دخول الزائر:", error);
      alert("حدث خطأ في تسجيل دخول الزائر");
    } finally {
      setActionLoading(false);
    }
  };

  const handleVisitorCheckOut = async (visitorId: string) => {
    try {
      setActionLoading(true);
      const response = await fetch("/api/security/visitor-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ visitorId }),
      });

      if (response.ok) {
        fetchVisitors();
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل خروج الزائر");
      }
    } catch (error) {
      console.error("خطأ في تسجيل خروج الزائر:", error);
      alert("حدث خطأ في تسجيل خروج الزائر");
    } finally {
      setActionLoading(false);
    }
  };

  // تصفية الزوار
  const filteredVisitors = visitors.filter(visitor =>
    visitor.visitorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    visitor.visitorCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
    visitor.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // تصنيف الزوار
  const pendingVisitors = filteredVisitors.filter(v => !v.checkInTime);
  const activeVisitors = filteredVisitors.filter(v => v.checkInTime && !v.checkOutTime);
  const completedVisitors = filteredVisitors.filter(v => v.checkInTime && v.checkOutTime);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الزوار...</p>
        </div>
      </div>
    );
  }

  const VisitorCard = ({ visitor, showCheckIn = false, showCheckOut = false }: {
    visitor: VisitorRequest;
    showCheckIn?: boolean;
    showCheckOut?: boolean;
  }) => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-full">
            <FaUsers className="text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{visitor.visitorName}</h3>
            <p className="text-sm text-gray-600">{visitor.visitorCompany}</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">الموظف المضيف</p>
          <p className="font-medium text-gray-900">{visitor.user.name}</p>
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2">
          <FaBuilding className="text-gray-400" size={14} />
          <span className="text-sm text-gray-600">الغرض: {visitor.purpose}</span>
        </div>

        {visitor.visitTime && (
          <div className="flex items-center gap-2">
            <FaClock className="text-purple-500" size={14} />
            <span className="text-sm text-purple-600">الوقت المتوقع: {visitor.visitTime}</span>
          </div>
        )}

        {visitor.companions && (
          <div className="flex items-center gap-2">
            <FaUserFriends className="text-gray-400" size={14} />
            <span className="text-sm text-gray-600">المرافقون: {visitor.companions}</span>
          </div>
        )}

        {visitor.vehicleInfo && (
          <div className="flex items-center gap-2">
            <FaCar className="text-gray-400" size={14} />
            <span className="text-sm text-gray-600">بيانات السيارة: {visitor.vehicleInfo}</span>
          </div>
        )}

        {visitor.checkInTime && (
          <div className="flex items-center gap-2">
            <FaClock className="text-green-500" size={14} />
            <span className="text-sm text-green-600">
              دخول: {new Date(visitor.checkInTime).toLocaleTimeString('en-GB')}
            </span>
          </div>
        )}

        {visitor.checkOutTime && (
          <div className="flex items-center gap-2">
            <FaClock className="text-red-500" size={14} />
            <span className="text-sm text-red-600">
              خروج: {new Date(visitor.checkOutTime).toLocaleTimeString('en-GB')}
            </span>
          </div>
        )}
      </div>

      <div className="flex gap-2">
        {showCheckIn && (
          <button
            onClick={() => handleVisitorCheckIn(visitor.id)}
            disabled={actionLoading}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
          >
            <FaUserCheck />
            تسجيل الدخول
          </button>
        )}

        {showCheckOut && (
          <button
            onClick={() => handleVisitorCheckOut(visitor.id)}
            disabled={actionLoading}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
          >
            <FaUserTimes />
            تسجيل الخروج
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-white/20 rounded-full">
              <FaUsers size={32} />
            </div>
            <div>
              <h1 className="text-3xl font-bold">إدارة دخول وخروج الزوار</h1>
              <p className="text-blue-100 mt-1">
                تسجيل حركة الزوار - {new Date().toLocaleDateString('en-GB')}
              </p>
            </div>
          </div>
          <button
            onClick={() => router.push("/dashboard/security")}
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaArrowLeft />
            العودة
          </button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">في انتظار الدخول</p>
              <p className="text-2xl font-bold text-gray-900">{pendingVisitors.length}</p>
            </div>
            <div className="p-3 rounded-full bg-orange-100">
              <FaClock className="text-orange-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">داخل المبنى</p>
              <p className="text-2xl font-bold text-gray-900">{activeVisitors.length}</p>
            </div>
            <div className="p-3 rounded-full bg-green-100">
              <FaUserCheck className="text-green-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-gray-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">انتهت الزيارة</p>
              <p className="text-2xl font-bold text-gray-900">{completedVisitors.length}</p>
            </div>
            <div className="p-3 rounded-full bg-gray-100">
              <FaCheckCircle className="text-gray-600" size={20} />
            </div>
          </div>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="relative">
          <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث بالاسم أو الشركة أو الموظف المضيف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* قوائم الزوار */}
      <div className="space-y-8">
        {/* الزوار في انتظار الدخول */}
        {pendingVisitors.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaClock className="text-orange-500" />
              في انتظار الدخول ({pendingVisitors.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pendingVisitors.map((visitor) => (
                <VisitorCard
                  key={visitor.id}
                  visitor={visitor}
                  showCheckIn={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* الزوار داخل المبنى */}
        {activeVisitors.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaUserCheck className="text-green-500" />
              داخل المبنى ({activeVisitors.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeVisitors.map((visitor) => (
                <VisitorCard
                  key={visitor.id}
                  visitor={visitor}
                  showCheckOut={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* الزوار الذين انتهت زيارتهم */}
        {completedVisitors.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaCheckCircle className="text-gray-500" />
              انتهت الزيارة ({completedVisitors.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {completedVisitors.map((visitor) => (
                <VisitorCard
                  key={visitor.id}
                  visitor={visitor}
                />
              ))}
            </div>
          </div>
        )}

        {/* رسالة عدم وجود زوار */}
        {filteredVisitors.length === 0 && (
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-12 text-center">
            <FaUsers className="mx-auto text-gray-300 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد زوار اليوم</h3>
            <p className="text-gray-600">لم يتم تسجيل أي زوار لهذا اليوم</p>
          </div>
        )}
      </div>
    </div>
  );
}
