"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  FaCalendarDay,
  Fa<PERSON>ser<PERSON><PERSON>ck,
  Fa<PERSON><PERSON><PERSON>,
  FaClock,
  FaArrowLeft,
  FaDownload,
  FaPrint,
  FaEye,
  FaFileAlt
} from "react-icons/fa";

interface DailyReport {
  date: string;
  totalEmployees: number;
  presentEmployees: number;
  absentEmployees: number;
  lateArrivals: number;
  earlyDepartures: number;
  totalVisitors: number;
  activeVisitors: number;
  afterHoursPermits: number;
  activeAfterHours: number;
}

interface AttendanceRecord {
  id: string;
  checkInTime: string;
  checkOutTime: string | null;
  exitType: string | null;
  entryNumber: number;
  user: {
    name: string;
    employeeNumber: string;
  };
}

interface VisitorRecord {
  id: string;
  visitorName: string;
  visitorCompany: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  user: {
    name: string;
  };
}

export default function SecurityReportsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [dailyReport, setDailyReport] = useState<DailyReport | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [visitorRecords, setVisitorRecords] = useState<VisitorRecord[]>([]);

  // التحقق من صلاحية الأمن
  useEffect(() => {
    if (session?.user?.role !== "SECURITY") {
      router.push("/dashboard");
      return;
    }
    fetchReports();
  }, [session, router, selectedDate]);

  const fetchReports = async () => {
    try {
      setLoading(true);

      // جلب التقرير اليومي
      const reportRes = await fetch(`/api/security/daily-report?date=${selectedDate}`);
      if (reportRes.ok) {
        const reportData = await reportRes.json();
        setDailyReport(reportData);
      }

      // جلب سجلات الحضور
      const attendanceRes = await fetch(`/api/security/attendance-report?date=${selectedDate}`);
      if (attendanceRes.ok) {
        const attendanceData = await attendanceRes.json();
        setAttendanceRecords(attendanceData);
      }

      // جلب سجلات الزوار
      const visitorsRes = await fetch(`/api/security/visitors-report?date=${selectedDate}`);
      if (visitorsRes.ok) {
        const visitorsData = await visitorsRes.json();
        setVisitorRecords(visitorsData);
      }

    } catch (error) {
      console.error("خطأ في جلب التقارير:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // يمكن إضافة منطق تصدير التقرير هنا
    alert("سيتم إضافة ميزة التصدير قريباً");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التقارير...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 print:space-y-4">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-xl shadow-lg p-6 text-white print:hidden">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-white/20 rounded-full">
              <FaCalendarDay size={32} />
            </div>
            <div>
              <h1 className="text-3xl font-bold">تقارير الأمن اليومية</h1>
              <p className="text-purple-100 mt-1">
                تقارير شاملة لحركة الموظفين والزوار
              </p>
            </div>
          </div>
          <button
            onClick={() => router.push("/dashboard/security")}
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaArrowLeft />
            العودة
          </button>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 print:hidden">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">التاريخ:</label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex gap-3">
            <button
              onClick={handlePrint}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <FaPrint />
              طباعة
            </button>
            <button
              onClick={handleExport}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <FaDownload />
              تصدير
            </button>
          </div>
        </div>
      </div>

      {/* رأس التقرير للطباعة */}
      <div className="hidden print:block text-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">تقرير الأمن اليومي</h1>
        <p className="text-gray-600 mt-2">
          التاريخ: {new Date(selectedDate).toLocaleDateString('en-GB')}
        </p>
      </div>

      {/* الملخص الإحصائي */}
      {dailyReport && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaFileAlt className="text-blue-600" />
            ملخص اليوم
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{dailyReport.presentEmployees}</div>
              <div className="text-sm text-gray-600">موظفون حاضرون</div>
            </div>

            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{dailyReport.absentEmployees}</div>
              <div className="text-sm text-gray-600">موظفون غائبون</div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{dailyReport.totalVisitors}</div>
              <div className="text-sm text-gray-600">إجمالي الزوار</div>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{dailyReport.afterHoursPermits}</div>
              <div className="text-sm text-gray-600">تصاريح بعد الدوام</div>
            </div>
          </div>
        </div>
      )}

      {/* سجلات الحضور */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <FaUserCheck className="text-green-600" />
          سجلات الحضور ({attendanceRecords.length})
        </h2>

        {attendanceRecords.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-2">الموظف</th>
                  <th className="text-right py-2">رقم الموظف</th>
                  <th className="text-right py-2">وقت الدخول</th>
                  <th className="text-right py-2">وقت الخروج</th>
                  <th className="text-right py-2">نوع الخروج</th>
                  <th className="text-right py-2">رقم الحركة</th>
                </tr>
              </thead>
              <tbody>
                {attendanceRecords.map((record) => (
                  <tr key={record.id} className="border-b border-gray-100">
                    <td className="py-2">{record.user.name}</td>
                    <td className="py-2">{record.user.employeeNumber}</td>
                    <td className="py-2">
                      {new Date(record.checkInTime).toLocaleTimeString('en-GB')}
                    </td>
                    <td className="py-2">
                      {record.checkOutTime
                        ? new Date(record.checkOutTime).toLocaleTimeString('en-GB')
                        : "لم يخرج بعد"
                      }
                    </td>
                    <td className="py-2">
                      {record.exitType === "OFFICIAL" ? "خروج رسمي" :
                       record.exitType === "PERSONAL" ? "استئذان شخصي" :
                       record.exitType === "WORK" ? "استئذان عمل" :
                       record.exitType === "HEALTH" ? "استئذان صحي" : "-"}
                    </td>
                    <td className="py-2">{record.entryNumber}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-4">لا توجد سجلات حضور لهذا التاريخ</p>
        )}
      </div>

      {/* سجلات الزوار */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <FaUsers className="text-blue-600" />
          سجلات الزوار ({visitorRecords.length})
        </h2>

        {visitorRecords.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-2">اسم الزائر</th>
                  <th className="text-right py-2">الشركة</th>
                  <th className="text-right py-2">الموظف المضيف</th>
                  <th className="text-right py-2">وقت الدخول</th>
                  <th className="text-right py-2">وقت الخروج</th>
                  <th className="text-right py-2">مدة الزيارة</th>
                </tr>
              </thead>
              <tbody>
                {visitorRecords.map((record) => (
                  <tr key={record.id} className="border-b border-gray-100">
                    <td className="py-2">{record.visitorName}</td>
                    <td className="py-2">{record.visitorCompany}</td>
                    <td className="py-2">{record.user.name}</td>
                    <td className="py-2">
                      {record.checkInTime
                        ? new Date(record.checkInTime).toLocaleTimeString('en-GB')
                        : "لم يدخل بعد"
                      }
                    </td>
                    <td className="py-2">
                      {record.checkOutTime
                        ? new Date(record.checkOutTime).toLocaleTimeString('en-GB')
                        : "لم يخرج بعد"
                      }
                    </td>
                    <td className="py-2">
                      {record.checkInTime && record.checkOutTime ? (
                        Math.round((new Date(record.checkOutTime).getTime() - new Date(record.checkInTime).getTime()) / (1000 * 60)) + " دقيقة"
                      ) : "-"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-4">لا توجد سجلات زوار لهذا التاريخ</p>
        )}
      </div>
    </div>
  );
}
