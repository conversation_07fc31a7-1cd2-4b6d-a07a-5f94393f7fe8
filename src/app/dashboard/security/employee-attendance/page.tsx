"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  FaUserCheck,
  FaUserTimes,
  FaSearch,
  FaClock,
  FaArrowLeft,
  FaUser
} from "react-icons/fa";

interface Employee {
  id: string;
  name: string;
  employeeNumber: string;
  position: string;
  department: {
    name: string;
  } | null;
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkInTime: string;
  checkOutTime: string | null;
  exitType: string | null;
  entryNumber: number;
  user: {
    name: string;
    employeeNumber: string;
  };
}



export default function EmployeeAttendancePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [allTodayRecords, setAllTodayRecords] = useState<AttendanceRecord[]>([]);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // التحقق من صلاحية الأمن
  useEffect(() => {
    if (session?.user?.role !== "SECURITY") {
      router.push("/dashboard");
      return;
    }
    fetchData();
  }, [session, router]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // جلب الموظفين المتاحين (باستثناء الذين في إجازة معتمدة)
      const employeesRes = await fetch("/api/security/employees-available");
      if (employeesRes.ok) {
        const employeesData = await employeesRes.json();
        setEmployees(employeesData);
      }

      // جلب جميع سجلات اليوم
      const attendanceRes = await fetch("/api/security/today-attendance");
      if (attendanceRes.ok) {
        const attendanceData = await attendanceRes.json();
        setAllTodayRecords(attendanceData);
      }

    } catch (error) {
      console.error("خطأ في جلب البيانات:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckIn = async (employeeId: string) => {
    try {
      setActionLoading(employeeId);
      const response = await fetch("/api/security/check-in", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          employeeId,
        }),
      });

      if (response.ok) {
        fetchData(); // إعادة جلب جميع البيانات
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل الدخول");
      }
    } catch (error) {
      console.error("خطأ في تسجيل الدخول:", error);
      alert("حدث خطأ في تسجيل الدخول");
    } finally {
      setActionLoading(null);
    }
  };

  const handleCheckOut = async (employeeId: string) => {
    try {
      setActionLoading(employeeId);
      const response = await fetch("/api/security/check-out", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          employeeId,
          exitType: "AUTO", // سيتم تحديد النوع تلقائياً في الخادم
        }),
      });

      if (response.ok) {
        fetchData(); // إعادة جلب جميع البيانات
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تسجيل الخروج");
      }
    } catch (error) {
      console.error("خطأ في تسجيل الخروج:", error);
      alert("حدث خطأ في تسجيل الخروج");
    } finally {
      setActionLoading(null);
    }
  };

  // تصفية الموظفين
  const filteredEmployees = employees.filter(emp =>
    emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emp.employeeNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );



  // التحقق من إمكانية الدخول/الخروج لموظف معين
  const getEmployeeStatus = (employeeId: string) => {
    const employeeRecords = allTodayRecords.filter(record =>
      record.user && record.user.name &&
      employees.find(emp => emp.id === employeeId && emp.name === record.user.name)
    );

    const canCheckIn = employeeRecords.length < 4 &&
      (!employeeRecords.length || employeeRecords[employeeRecords.length - 1].checkOutTime);
    const canCheckOut = employeeRecords.length > 0 &&
      !employeeRecords[employeeRecords.length - 1].checkOutTime;

    return { canCheckIn, canCheckOut, recordsCount: employeeRecords.length };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-white/20 rounded-full">
              <FaUserCheck size={32} />
            </div>
            <div>
              <h1 className="text-3xl font-bold">تسجيل حضور الموظفين</h1>
              <p className="text-green-100 mt-1">
                إدارة دخول وخروج الموظفين - {new Date().toLocaleDateString('en-GB')}
              </p>
              <p className="text-green-100 mt-1 text-sm">
                ملاحظة: الموظفون في الإجازات المعتمدة لا يظهرون في هذا الكشف
              </p>
            </div>
          </div>
          <button
            onClick={() => router.push("/dashboard/security")}
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaArrowLeft />
            العودة
          </button>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="relative">
          <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث بالاسم أو رقم الموظف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* جدول الموظفين */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">قائمة الموظفين وحضورهم</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الموظف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رقم الموظف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  القسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  عدد الحركات
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تسجيل دخول
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تسجيل خروج
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEmployees.map((employee) => {
                const status = getEmployeeStatus(employee.id);
                return (
                  <tr key={employee.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="p-2 bg-gray-100 rounded-full mr-3">
                          <FaUser className="text-gray-600" size={16} />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {employee.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {employee.position || "غير محدد"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {employee.employeeNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {employee.department?.name || "غير محدد"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {status.recordsCount}/4
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleCheckIn(employee.id)}
                        disabled={!status.canCheckIn || actionLoading === employee.id}
                        className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 mx-auto ${
                          status.canCheckIn && actionLoading !== employee.id
                            ? "bg-green-600 hover:bg-green-700 text-white"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        }`}
                      >
                        {actionLoading === employee.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                        ) : (
                          <>
                            <FaUserCheck size={16} />
                            دخول
                          </>
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleCheckOut(employee.id)}
                        disabled={!status.canCheckOut || actionLoading === employee.id}
                        className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 mx-auto ${
                          status.canCheckOut && actionLoading !== employee.id
                            ? "bg-red-600 hover:bg-red-700 text-white"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        }`}
                      >
                        {actionLoading === employee.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                        ) : (
                          <>
                            <FaUserTimes size={16} />
                            خروج
                          </>
                        )}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* عرض جميع حركات اليوم مجمعة حسب الموظف */}
      {allTodayRecords.length > 0 && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900">جميع حركات اليوم</h2>
          </div>

          <div className="p-6">
            {(() => {
              // تجميع السجلات حسب الموظف
              const groupedRecords = allTodayRecords.reduce((acc, record) => {
                const employeeName = record.user?.name || "غير محدد";
                if (!acc[employeeName]) {
                  acc[employeeName] = {
                    employee: record.user,
                    records: []
                  };
                }
                acc[employeeName].records.push(record);
                return acc;
              }, {} as Record<string, { employee: any; records: AttendanceRecord[] }>);

              return Object.entries(groupedRecords).map(([employeeName, data]) => (
                <div key={employeeName} className="mb-6 last:mb-0">
                  {/* معلومات الموظف */}
                  <div className="flex items-center gap-3 mb-3 p-3 bg-gray-50 rounded-lg">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <FaUser className="text-blue-600" size={16} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{employeeName}</h3>
                      <p className="text-sm text-gray-500">
                        رقم الموظف: {data.employee?.employeeNumber || "غير محدد"}
                      </p>
                    </div>
                    <div className="mr-auto">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {data.records.length} حركة
                      </span>
                    </div>
                  </div>

                  {/* حركات الموظف */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {data.records.map((record) => (
                      <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            الحركة #{record.entryNumber}
                          </span>
                          {record.exitType && (
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              record.exitType === "OFFICIAL"
                                ? "bg-green-100 text-green-800"
                                : record.exitType === "WORK"
                                ? "bg-blue-100 text-blue-800"
                                : record.exitType === "HEALTH"
                                ? "bg-purple-100 text-purple-800"
                                : "bg-orange-100 text-orange-800"
                            }`}>
                              {record.exitType === "OFFICIAL" ? "رسمي" : 
                               record.exitType === "WORK" ? "عمل" :
                               record.exitType === "HEALTH" ? "صحي" : "شخصي"}
                            </span>
                          )}
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <FaUserCheck className="text-green-500" size={12} />
                            <span className="text-gray-600">دخول:</span>
                            <span className="font-medium">
                              {new Date(record.checkInTime).toLocaleTimeString('en-GB')}
                            </span>
                          </div>

                          {record.checkOutTime ? (
                            <div className="flex items-center gap-2">
                              <FaUserTimes className="text-red-500" size={12} />
                              <span className="text-gray-600">خروج:</span>
                              <span className="font-medium">
                                {new Date(record.checkOutTime).toLocaleTimeString('en-GB')}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <FaClock className="text-orange-500" size={12} />
                              <span className="text-orange-600 font-medium">لم يخرج بعد</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ));
            })()}
          </div>
        </div>
      )}
    </div>
  );
}
