'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // تسجيل الخطأ في لوحة التحكم
    console.error('Dashboard error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-lg mx-4">
        <div className="text-center">
          <div className="text-blue-500 text-6xl mb-4">🔧</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            خطأ في لوحة التحكم
          </h2>
          <p className="text-gray-600 mb-6">
            حدث خطأ أثناء تحميل لوحة التحكم. يرجى المحاولة مرة أخرى أو العودة للصفحة الرئيسية.
          </p>
          <div className="space-y-3">
            <button
              onClick={reset}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              إعادة المحاولة
            </button>
            <Link
              href="/dashboard"
              className="block w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center"
            >
              العودة للوحة التحكم
            </Link>
            <Link
              href="/login"
              className="block w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-center"
            >
              تسجيل الدخول مرة أخرى
            </Link>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-6 text-left">
              <summary className="cursor-pointer text-sm text-gray-500">
                تفاصيل الخطأ (وضع التطوير)
              </summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                {error.message}
                {error.stack && '\n\n' + error.stack}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );
}
