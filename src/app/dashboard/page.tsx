"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardCard from "@/components/dashboard/DashboardCard";
// import QuickActions from "@/components/dashboard/QuickActions";
// import RecentActivity from "@/components/dashboard/RecentActivity";
// import AttendanceChart from "@/components/dashboard/AttendanceChart";
// import SystemStatus from "@/components/dashboard/SystemStatus";
import {
  FaUsersCog,
  FaCalendarCheck,
  FaUserClock,
  FaUserTie,
  FaUsers,
  FaClipboardList,
  FaUserFriends,
  FaClock,
  FaCog,
  FaChartBar,
  FaBuilding,
  FaCalendarAlt,
  FaUserCheck,
  FaUserTimes,
  FaPlus,
  FaEye,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
  FaArrowUp
} from "react-icons/fa";

// Hook to handle both NextAuth and temporary sessions
function useAuth() {
  const { data: nextAuthSession } = useSession();
  const [tempSession, setTempSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkTempSession = async () => {
      try {
        if (nextAuthSession) {
          setLoading(false);
          return;
        }

        const response = await fetch('/api/auth/temp-session');
        const data = await response.json();
        
        if (data.session) {
          setTempSession(data.session);
        }
      } catch (error) {
        console.error('❌ خطأ في فحص الجلسة المؤقتة:', error);
      } finally {
        setLoading(false);
      }
    };

    checkTempSession();
  }, [nextAuthSession]);

  return {
    session: nextAuthSession || tempSession,
    loading
  };
}

interface AdminDashboardStats {
  overview: {
    totalEmployees: number;
    totalDepartments: number;
    fullAttendanceToday: number;     // حضور (ساعات العمل >= الحد الأدنى)
    partialAttendanceToday: number;  // حضور جزئي (ساعات العمل < الحد الأدنى)
    onLeaveToday: number;            // في إجازة معتمدة
    absentToday: number;             // غياب بدون إذن
    totalVisitorsToday: number;
    activeVisitors: number;
    isWeekend: boolean;
    isOfficialHoliday: boolean;
    holidayName: string | null;
    minWorkingHours?: number;        // الحد الأدنى لساعات العمل
    workingHours?: number;           // إجمالي ساعات العمل اليومية
  };
  pendingApprovals: {
    leaves: number;
    visitors: number;
    afterHours: number;
    total: number;
  };
  monthlyStats: {
    attendanceRate: number;
    overtimeHours: number;
    workDaysInMonth: number;
    actualAttendanceDays: number;
  };
  details: {
    fullAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      status?: string;
      exitTime?: string;
      exitType?: string;
      totalWorkingHours?: number;
    }>;
    partialAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      exitTime: string;
      exitType: string;
      totalWorkingHours?: number;
    }>;
    employeesOnLeave: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      leaveReason: string;
    }>;
    absentEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
    }>;
  };
}

export default function DashboardPage() {
  const { session, loading: sessionLoading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  const role = session?.user?.role;
  const canViewStats = role === "ADMIN" || role === "HR";

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      if (!canViewStats) {
        setLoading(false);
        return;
      }

      const response = await fetch("/api/dashboard/admin-stats");
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        console.error("فشل في جلب الإحصائيات:", response.statusText);
      }
    } catch (error) {
      console.error("خطأ في جلب الإحصائيات:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Wait for session to load
    if (sessionLoading) return;

    // If no session, redirect to login
    if (!session) {
      router.push("/login");
      return;
    }

    // إعادة توجيه المستخدمين إلى لوحات التحكم المخصصة
    if (session?.user?.role === "SECURITY") {
      router.push("/dashboard/security");
      return;
    }
    if (session?.user?.role === "EMPLOYEE") {
      router.push("/dashboard/employee");
      return;
    }
    if (session?.user?.role === "MANAGER") {
      router.push("/dashboard/manager");
      return;
    }
    fetchStats();
    
    // Auto-refresh every 5 minutes for real-time statistics
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [canViewStats, session, router, sessionLoading]);

  // Show loading state while checking session
  if (sessionLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto"></div>
          <p className="mt-6 text-gray-700 text-lg">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="space-y-8 p-6">
        {/* Header مع تصميم جديد */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl shadow-2xl p-8 text-white relative overflow-hidden">
          {/* تأثيرات بصرية */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white opacity-10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white opacity-5 rounded-full translate-y-24 -translate-x-24"></div>
          
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        <div>
                <h1 className="text-4xl lg:text-5xl font-bold mb-2">أهلاً وسهلاً</h1>
                <p className="text-xl text-blue-100 mb-1">
                  {session?.user?.name}
                </p>
                <p className="text-blue-200">
                  نظرة عامة شاملة على نظام إدارة الحضور والانصراف
          </p>
        </div>
              <div className="flex flex-col items-end text-right">
                <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                  <p className="text-sm text-blue-100 mb-1">اليوم</p>
                  <p className="text-lg font-semibold">
                    {new Date().toLocaleDateString('ar-EG', {
                      weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })} م
                  </p>
                  <p className="text-sm text-blue-200 mt-1">
                    {new Date().toLocaleTimeString('ar-EG', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>
      </div>

        {/* الإحصائيات الرئيسية مع تصميم محسن */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {canViewStats && (
          <>
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl p-6 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium">إجمالي الموظفين</p>
                    <p className="text-3xl font-bold mt-2">
                      {loading ? (
                        <div className="animate-pulse bg-white/30 h-8 w-16 rounded"></div>
                      ) : stats?.overview.totalEmployees || 0}
                    </p>
                    <div className="flex items-center mt-2">
                      <FaArrowUp className="text-green-300 mr-1" size={12} />
                      <span className="text-green-300 text-xs">نشط في النظام</span>
                    </div>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <FaUsers size={24} />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-xl p-6 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium">الأقسام</p>
                    <p className="text-3xl font-bold mt-2">
                      {loading ? (
                        <div className="animate-pulse bg-white/30 h-8 w-16 rounded"></div>
                      ) : stats?.overview.totalDepartments || 0}
                    </p>
                    <p className="text-green-200 text-xs mt-2">قسم نشط</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <FaBuilding size={24} />
                  </div>
                </div>
              </div>
          </>
        )}

          <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-xl p-6 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">الحضور اليوم</p>
                <p className="text-3xl font-bold mt-2">
                  {loading ? (
                    <div className="animate-pulse bg-white/30 h-8 w-16 rounded"></div>
                  ) : (stats?.overview.fullAttendanceToday || 0) + (stats?.overview.partialAttendanceToday || 0)}
                </p>
                <p className="text-purple-200 text-xs mt-2">من أصل {stats?.overview.totalEmployees || 0} موظف</p>
              </div>
              <div className="bg-white/20 p-3 rounded-xl">
                <FaUserCheck size={24} />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl shadow-xl p-6 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">الإجازات النشطة</p>
                <p className="text-3xl font-bold mt-2">
                  {loading ? (
                    <div className="animate-pulse bg-white/30 h-8 w-16 rounded"></div>
                  ) : stats?.overview.onLeaveToday || 0}
                </p>
                <p className="text-orange-200 text-xs mt-2">إجازة مُعتمدة</p>
              </div>
              <div className="bg-white/20 p-3 rounded-xl">
                <FaCalendarAlt size={24} />
              </div>
            </div>
          </div>
      </div>

        {/* ملخص الحضور مع رسوم بيانية */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">ملخص الحضور اليوم</h2>
            <p className="text-gray-600">إحصائيات مفصلة لحضور الموظفين (بناءً على ساعات العمل الفعلية)</p>
            {stats?.overview.minWorkingHours && (
              <div className="mt-2 bg-blue-50 text-blue-800 px-3 py-1 rounded-full text-sm inline-block">
                الحد الأدنى: {stats.overview.minWorkingHours} ساعات | المطلوب: {stats.overview.workingHours} ساعات
              </div>
            )}
            {stats?.overview.isWeekend && (
              <div className="mt-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block">
                عطلة نهاية أسبوع
              </div>
            )}
            {stats?.overview.isOfficialHoliday && (
              <div className="mt-2 bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm inline-block">
                عطلة رسمية: {stats.overview.holidayName}
              </div>
            )}
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* حضور */}
              <div className="text-center">
                <div className="bg-green-50 p-6 rounded-xl border border-green-100">
                  <FaCheckCircle className="text-green-500 mx-auto mb-3" size={32} />
                  <h3 className="text-2xl font-bold text-green-600">{stats?.overview.fullAttendanceToday || 0}</h3>
                  <p className="text-green-700 font-medium">حضور</p>
                  <div className="mt-2 bg-green-100 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${stats?.overview.totalEmployees ? (stats.overview.fullAttendanceToday / stats.overview.totalEmployees) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    {stats?.overview.totalEmployees ? Math.round((stats.overview.fullAttendanceToday / stats.overview.totalEmployees) * 100) : 0}%
                  </p>
                  {stats?.overview.minWorkingHours && (
                    <p className="text-xs text-green-500 mt-1">
                      ≥ {stats.overview.minWorkingHours} ساعات
                    </p>
                  )}
                </div>
              </div>

              {/* حضور جزئي */}
              <div className="text-center">
                <div className="bg-orange-50 p-6 rounded-xl border border-orange-100">
                  <FaUserClock className="text-orange-500 mx-auto mb-3" size={32} />
                  <h3 className="text-2xl font-bold text-orange-600">{stats?.overview.partialAttendanceToday || 0}</h3>
                  <p className="text-orange-700 font-medium">حضور جزئي</p>
                  <div className="mt-2 bg-orange-100 rounded-full h-2">
                    <div 
                      className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${stats?.overview.totalEmployees ? (stats.overview.partialAttendanceToday / stats.overview.totalEmployees) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-orange-600 mt-1">
                    {stats?.overview.totalEmployees ? Math.round((stats.overview.partialAttendanceToday / stats.overview.totalEmployees) * 100) : 0}%
                  </p>
                  {stats?.overview.minWorkingHours && (
                    <p className="text-xs text-orange-500 mt-1">
                      &lt; {stats.overview.minWorkingHours} ساعات
                    </p>
                  )}
                </div>
              </div>

              {/* في إجازة */}
              <div className="text-center">
                <div className="bg-blue-50 p-6 rounded-xl border border-blue-100">
                  <FaCalendarAlt className="text-blue-500 mx-auto mb-3" size={32} />
                  <h3 className="text-2xl font-bold text-blue-600">{stats?.overview.onLeaveToday || 0}</h3>
                  <p className="text-blue-700 font-medium">في إجازة</p>
                  <div className="mt-2 bg-blue-100 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${stats?.overview.totalEmployees ? (stats.overview.onLeaveToday / stats.overview.totalEmployees) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    {stats?.overview.totalEmployees ? Math.round((stats.overview.onLeaveToday / stats.overview.totalEmployees) * 100) : 0}%
                  </p>
                </div>
              </div>

              {/* الغائبين */}
              <div className="text-center">
                <div className="bg-red-50 p-6 rounded-xl border border-red-100">
                  <FaTimesCircle className="text-red-500 mx-auto mb-3" size={32} />
                  <h3 className="text-2xl font-bold text-red-600">{stats?.overview.absentToday || 0}</h3>
                  <p className="text-red-700 font-medium">غائب</p>
                  <div className="mt-2 bg-red-100 rounded-full h-2">
                    <div 
                      className="bg-red-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${stats?.overview.totalEmployees ? (stats.overview.absentToday / stats.overview.totalEmployees) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-red-600 mt-1">
                    {stats?.overview.totalEmployees ? Math.round((stats.overview.absentToday / stats.overview.totalEmployees) * 100) : 0}%
                  </p>
                </div>
              </div>
            </div>

            {/* Progress bar showing overall distribution */}
            <div className="mt-6">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>التوزيع الإجمالي</span>
                <span>إجمالي الموظفين: {stats?.overview.totalEmployees || 0}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div className="flex h-4 rounded-full overflow-hidden">
                  {/* Attendance */}
                  <div 
                    className="bg-green-500" 
                    style={{ 
                      width: `${stats?.overview.totalEmployees ? (stats.overview.fullAttendanceToday / stats.overview.totalEmployees) * 100 : 0}%` 
                    }}
                    title={`حضور: ${stats?.overview.fullAttendanceToday || 0}`}
                  ></div>
                  {/* Partial Attendance */}
                  <div 
                    className="bg-orange-500" 
                    style={{ 
                      width: `${stats?.overview.totalEmployees ? (stats.overview.partialAttendanceToday / stats.overview.totalEmployees) * 100 : 0}%` 
                    }}
                    title={`حضور جزئي: ${stats?.overview.partialAttendanceToday || 0}`}
                  ></div>
                  {/* On Leave */}
                  <div 
                    className="bg-blue-500" 
                    style={{ 
                      width: `${stats?.overview.totalEmployees ? (stats.overview.onLeaveToday / stats.overview.totalEmployees) * 100 : 0}%` 
                    }}
                    title={`في إجازة: ${stats?.overview.onLeaveToday || 0}`}
                  ></div>
                  {/* Absent */}
                  <div 
                    className="bg-red-500" 
                    style={{ 
                      width: `${stats?.overview.totalEmployees ? (stats.overview.absentToday / stats.overview.totalEmployees) * 100 : 0}%` 
                    }}
                    title={`غائب: ${stats?.overview.absentToday || 0}`}
                  ></div>
                </div>
              </div>
              <div className="flex justify-between text-xs text-gray-600 mt-2">
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  حضور
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-orange-500 rounded"></div>
                  حضور جزئي
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  في إجازة
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  غائب
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* الإجراءات السريعة مع تصميم شبكي */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* الإجراءات السريعة */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">الإجراءات السريعة</h2>
              <p className="text-gray-600">الوصول السريع للمهام الأساسية</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-4">
                <a 
                  href="/dashboard/employees/new" 
                  className="group flex items-center gap-4 p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 hover:shadow-md"
                >
                  <div className="bg-blue-100 group-hover:bg-blue-200 p-3 rounded-lg transition-colors">
                    <FaPlus className="text-blue-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 group-hover:text-blue-900">إضافة موظف جديد</h3>
                    <p className="text-sm text-gray-600">تسجيل موظف جديد في النظام</p>
                  </div>
                </a>

                <a 
                  href="/dashboard/attendance" 
                  className="group flex items-center gap-4 p-4 rounded-xl border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300 hover:shadow-md"
                >
                  <div className="bg-purple-100 group-hover:bg-purple-200 p-3 rounded-lg transition-colors">
                    <FaEye className="text-purple-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 group-hover:text-purple-900">مراجعة الحضور</h3>
                    <p className="text-sm text-gray-600">عرض وإدارة سجلات الحضور</p>
                  </div>
                </a>

                <a 
                  href="/dashboard/leaves" 
                  className="group flex items-center gap-4 p-4 rounded-xl border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-all duration-300 hover:shadow-md"
                >
                  <div className="bg-orange-100 group-hover:bg-orange-200 p-3 rounded-lg transition-colors">
                    <FaCalendarCheck className="text-orange-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 group-hover:text-orange-900">إدارة الإجازات</h3>
                    <p className="text-sm text-gray-600">مراجعة وموافقة طلبات الإجازات</p>
                  </div>
                </a>

                <a 
                  href="/dashboard/settings" 
                  className="group flex items-center gap-4 p-4 rounded-xl border border-gray-200 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 hover:shadow-md"
                >
                  <div className="bg-gray-100 group-hover:bg-gray-200 p-3 rounded-lg transition-colors">
                    <FaCog className="text-gray-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">إعدادات النظام</h3>
                    <p className="text-sm text-gray-600">تخصيص إعدادات النظام</p>
                  </div>
                </a>
              </div>
            </div>
          </div>

          {/* الإحصائيات التفصيلية */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">إحصائيات إضافية</h2>
              <p className="text-gray-600">معلومات مفصلة حول النظام</p>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-100">
                  <div className="flex items-center gap-3">
                    <FaUserFriends className="text-indigo-600" size={24} />
                    <div>
                      <p className="font-semibold text-gray-900">الزوار اليوم</p>
                      <p className="text-sm text-gray-600">زائر مسجل</p>
                    </div>
                  </div>
                  <span className="text-2xl font-bold text-indigo-600">{stats?.overview.totalVisitorsToday || 0}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-100">
                  <div className="flex items-center gap-3">
                    <FaClock className="text-red-600" size={24} />
                    <div>
                      <p className="font-semibold text-gray-900">العمل الإضافي</p>
                      <p className="text-sm text-gray-600">ساعات هذا الشهر</p>
                    </div>
                  </div>
                  <span className="text-2xl font-bold text-red-600">{stats?.monthlyStats.overtimeHours || 0}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                  <div className="flex items-center gap-3">
                    <FaChartBar className="text-green-600" size={24} />
                    <div>
                      <p className="font-semibold text-gray-900">معدل الحضور</p>
                      <p className="text-sm text-gray-600">هذا الشهر</p>
                    </div>
                  </div>
                  <span className="text-2xl font-bold text-green-600">{stats?.monthlyStats.attendanceRate || 0}%</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-100">
                  <div className="flex items-center gap-3">
                    <FaExclamationTriangle className="text-yellow-600" size={24} />
                    <div>
                      <p className="font-semibold text-gray-900">طلبات معلقة</p>
                      <p className="text-sm text-gray-600">تحتاج موافقة</p>
                    </div>
                  </div>
                  <span className="text-2xl font-bold text-yellow-600">{stats?.pendingApprovals.total || 0}</span>
                </div>


              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
