"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FaPlus, FaEdit, FaTrash, FaClock, FaCheck, FaTimes } from "react-icons/fa";
import Link from "next/link";
import { DataTable, Column } from "@/components/ui/DataTable";
import { formatDate } from "@/lib/utils/dateUtils";

interface AfterHoursPermit {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
    employeeNumber: string | null;
  };
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  status: "PENDING" | "APPROVED" | "REJECTED" | "CANCELLED";
  createdAt: string;
  updatedAt: string;
}

const statusLabels = {
  PENDING: "في الانتظار",
  APPROVED: "موافق عليها",
  REJECTED: "مرفوضة",
  CANCELLED: "ملغاة",
};

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
};

export default function AfterHoursPage() {
  const { data: session } = useSession();
  const [permits, setPermits] = useState<AfterHoursPermit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // تعريف أعمدة الجدول
  const columns: Column<AfterHoursPermit>[] = [
    {
      key: 'user.name',
      title: 'الموظف',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.user.employeeNumber && (
            <div className="text-sm text-gray-500">#{row.user.employeeNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'date',
      title: 'التاريخ',
      sortable: true,
      render: (value) => <span className="text-blue-600">{formatDate(value)}</span>,
    },
    {
      key: 'startTime',
      title: 'وقت البداية',
      sortable: true,
      render: (value) => (
        <span className="text-green-600">
          {new Date(value).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      ),
    },
    {
      key: 'endTime',
      title: 'وقت النهاية',
      sortable: true,
      render: (value) => (
        <span className="text-red-600">
          {new Date(value).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      ),
    },
    {
      key: 'reason',
      title: 'السبب',
      searchable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      key: 'checkInTime',
      title: 'وقت الدخول الفعلي',
      render: (value) => (
        value ? (
          <span className="text-green-600">{new Date(value).toLocaleTimeString('ar-SA')}</span>
        ) : (
          <span className="text-gray-400">لم يدخل بعد</span>
        )
      ),
    },
    {
      key: 'checkOutTime',
      title: 'وقت الخروج الفعلي',
      render: (value) => (
        value ? (
          <span className="text-red-600">{new Date(value).toLocaleTimeString('ar-SA')}</span>
        ) : (
          <span className="text-gray-400">لم يخرج بعد</span>
        )
      ),
    },
    {
      key: 'status',
      title: 'الحالة',
      sortable: true,
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-sm ${statusColors[value as keyof typeof statusColors]}`}>
          {statusLabels[value as keyof typeof statusLabels]}
        </span>
      ),
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_, row) => (
        <div className="flex justify-center gap-2">
          {row.status === 'PENDING' && canManagePermits && (
            <>
              <button
                onClick={() => handleStatusUpdate(row.id, 'APPROVED')}
                disabled={actionLoading === row.id}
                className="text-green-600 hover:text-green-800 p-2 disabled:opacity-50"
                title="موافقة"
              >
                <FaCheck size={16} />
              </button>
              <button
                onClick={() => handleStatusUpdate(row.id, 'REJECTED')}
                disabled={actionLoading === row.id}
                className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
                title="رفض"
              >
                <FaTimes size={16} />
              </button>
            </>
          )}
          {/* تم حذف أزرار تسجيل الدخول والخروج */}
          {(canManagePermits || row.userId === session?.user?.id) && (
            <Link
              href={`/dashboard/after-hours/${row.id}/edit`}
              className="text-blue-600 hover:text-blue-800 p-2"
              title="تعديل"
            >
              <FaEdit size={16} />
            </Link>
          )}
          {(canManagePermits || row.userId === session?.user?.id) && row.status === 'PENDING' && (
            <button
              onClick={() => handleDelete(row.id)}
              disabled={actionLoading === row.id}
              className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
              title="حذف"
            >
              {actionLoading === row.id ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
              ) : (
                <FaTrash size={16} />
              )}
            </button>
          )}
        </div>
      ),
    },
  ];

  // جلب تصاريح العمل بعد الدوام
  const fetchPermits = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/after-hours");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب تصاريح العمل بعد الدوام");
      }

      const data = await response.json();
      setPermits(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب تصاريح العمل بعد الدوام:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // تحديث حالة التصريح
  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/after-hours/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تحديث حالة التصريح");
      }

      await fetchPermits();
      alert(`تم ${status === 'APPROVED' ? 'الموافقة على' : 'رفض'} التصريح بنجاح`);
    } catch (error) {
      console.error("خطأ في تحديث حالة التصريح:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في تحديث حالة التصريح");
    } finally {
      setActionLoading(null);
    }
  };

  // تم حذف دوال تسجيل الدخول والخروج

  // حذف التصريح
  const handleDelete = async (id: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا التصريح؟")) {
      return;
    }

    try {
      setActionLoading(id);
      const response = await fetch(`/api/after-hours/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف التصريح");
      }

      await fetchPermits();
      alert("تم حذف التصريح بنجاح");
    } catch (error) {
      console.error("خطأ في حذف التصريح:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف التصريح");
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchPermits();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManagePermits = userRole === "ADMIN" || userRole === "HR" || userRole === "MANAGER" || userRole === "SECURITY";

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">العمل بعد الدوام</h1>
        <Link
          href="/dashboard/after-hours/new"
          className="btn-primary flex items-center gap-2"
        >
          <FaPlus size={16} />
          طلب تصريح جديد
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {permits.length === 0 && !loading ? (
        <div className="card">
          <div className="text-center py-8">
            <FaClock size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد تصاريح عمل بعد الدوام</p>
            <Link
              href="/dashboard/after-hours/new"
              className="btn-primary"
            >
              إضافة أول تصريح
            </Link>
          </div>
        </div>
      ) : (
        <DataTable
          data={permits}
          columns={columns}
          loading={loading}
          searchPlaceholder="البحث في تصاريح العمل بعد الدوام..."
          pageSize={10}
        />
      )}
    </div>
  );
}
