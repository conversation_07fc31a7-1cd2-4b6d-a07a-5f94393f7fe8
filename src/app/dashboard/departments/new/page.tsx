"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { FaArrowRight, FaSave, FaCrown } from "react-icons/fa";

const departmentSchema = z.object({
  name: z.string().min(1, "اسم القسم مطلوب").max(100, "اسم القسم طويل جداً"),
  headId: z.string().optional(),
});

type DepartmentFormValues = z.infer<typeof departmentSchema>;

interface Employee {
  id: string;
  name: string;
  email: string;
  role: string;
}

export default function NewDepartmentPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: "",
      headId: "",
    },
  });

  // جلب الموظفين لاختيار رئيس القسم
  const fetchEmployees = async () => {
    try {
      const response = await fetch("/api/employees");
      if (response.ok) {
        const data = await response.json();
        setEmployees(data);
      }
    } catch (error) {
      console.error("خطأ في جلب الموظفين:", error);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const onSubmit = async (data: DepartmentFormValues) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/departments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.name,
          headId: data.headId || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في إنشاء القسم");
      }

      router.push("/dashboard/departments");
      router.refresh();
    } catch (error) {
      console.error("خطأ في إنشاء القسم:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-6">
        <Link
          href="/dashboard/departments"
          className="text-blue-600 hover:text-blue-800"
        >
          <FaArrowRight size={20} />
        </Link>
        <h1 className="text-2xl font-bold">إضافة قسم جديد</h1>
      </div>

      <div className="card max-w-2xl">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-6">
            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
              اسم القسم *
            </label>
            <input
              type="text"
              className="input"
              placeholder="أدخل اسم القسم"
              {...register("name")}
              disabled={loading}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>

          <div className="mb-6">
            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium flex items-center gap-2">
              <FaCrown className="text-yellow-500" />
              رئيس القسم (اختياري)
            </label>
            <select
              className="input"
              {...register("headId")}
              disabled={loading}
            >
              <option value="">🚫 لا يوجد رئيس قسم</option>
              {employees.filter(emp => emp.role === 'MANAGER' || emp.role === 'HR' || emp.role === 'ADMIN').map((employee) => (
                <option key={employee.id} value={employee.id}>
                  👨‍💼 {employee.name} - {employee.email} ({employee.role})
                </option>
              ))}
            </select>
            <div className="text-sm text-gray-500 mt-1">
              💡 يُنصح بتعيين رئيس قسم لضمان الإدارة الفعالة
            </div>
            {errors.headId && (
              <p className="text-red-500 text-sm mt-1">{errors.headId.message}</p>
            )}
          </div>

          <div className="flex gap-4">
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center gap-2 disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              ) : (
                <FaSave size={16} />
              )}
              {loading ? "جاري الحفظ..." : "حفظ القسم"}
            </button>

            <Link
              href="/dashboard/departments"
              className="btn-secondary"
            >
              إلغاء
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
