"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FaPlus, FaEdit, FaTrash, Fa<PERSON>sers, FaUser<PERSON>ie, FaCrown, FaExclamationTriangle } from "react-icons/fa";
import Link from "next/link";
import { DataTable, Column } from "@/components/ui/DataTable";

interface Department {
  id: string;
  name: string;
  headId: string | null;
  createdAt: string;
  updatedAt: string;
  users: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
  }>;
  head: {
    id: string;
    name: string;
    email: string;
  } | null;
  _count: {
    users: number;
  };
}

export default function DepartmentsPage() {
  const { data: session } = useSession();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);

  // تعريف أعمدة الجدول
  const columns: Column<Department>[] = [
    {
      key: 'name',
      title: 'اسم القسم',
      sortable: true,
      searchable: true,
      render: (value) => <span className="font-medium text-gray-900 dark:text-white">{value}</span>,
    },
    {
      key: '_count.users',
      title: 'عدد الموظفين',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-2">
          <FaUsers className="text-blue-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'head',
      title: 'رئيس القسم',
      sortable: false,
      searchable: true,
      render: (value, row) => (
        value ? (
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <FaCrown className="text-green-600 text-sm" />
              </div>
            </div>
            <div>
              <div className="font-medium text-green-700 dark:text-green-400 flex items-center gap-2">
                <FaUserTie className="text-sm" />
                {value.name}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{value.email}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
            <FaExclamationTriangle className="text-amber-500" />
            <div>
              <div className="font-medium">غير محدد</div>
              <div className="text-xs text-gray-500">يحتاج تعيين رئيس قسم</div>
            </div>
          </div>
        )
      ),
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_, row) => (
        <div className="flex justify-center gap-2">
          <Link
            href={`/dashboard/departments/${row.id}/edit`}
            className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
            title="تعديل"
          >
            <FaEdit size={16} />
          </Link>
          <button
            onClick={() => handleDelete(row.id, row.name)}
            disabled={deleteLoading === row.id}
            className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 disabled:opacity-50 transition-colors"
            title="حذف"
          >
            {deleteLoading === row.id ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
            ) : (
              <FaTrash size={16} />
            )}
          </button>
        </div>
      ),
    },
  ];

  // جلب الأقسام
  const fetchDepartments = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/departments");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب الأقسام");
      }

      const data = await response.json();
      setDepartments(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب الأقسام:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // حذف قسم
  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`هل أنت متأكد من حذف القسم "${name}"؟`)) {
      return;
    }

    try {
      setDeleteLoading(id);
      const response = await fetch(`/api/departments/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف القسم");
      }

      // إعادة جلب الأقسام بعد الحذف
      await fetchDepartments();
      alert("تم حذف القسم بنجاح");
    } catch (error) {
      console.error("خطأ في حذف القسم:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف القسم");
    } finally {
      setDeleteLoading(null);
    }
  };

  useEffect(() => {
    fetchDepartments();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  // حساب إحصائيات
  const departmentsWithHead = departments.filter(dept => dept.head !== null);
  const departmentsWithoutHead = departments.filter(dept => dept.head === null);
  const totalEmployees = departments.reduce((sum, dept) => sum + dept._count.users, 0);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">إدارة الأقسام</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إجمالي {departments.length} قسم - {totalEmployees} موظف
          </p>
        </div>
        <Link
          href="/dashboard/departments/new"
          className="btn-primary flex items-center gap-2"
        >
          <FaPlus size={16} />
          إضافة قسم جديد
        </Link>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <FaCrown className="text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{departmentsWithHead.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">أقسام لها رؤساء</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
              <FaExclamationTriangle className="text-amber-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-amber-600">{departmentsWithoutHead.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">أقسام بدون رؤساء</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <FaUsers className="text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{totalEmployees}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الموظفين</div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {departments.length === 0 && !loading ? (
        <div className="card">
          <div className="text-center py-8">
            <FaUsers size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد أقسام مسجلة</p>
            <Link
              href="/dashboard/departments/new"
              className="btn-primary"
            >
              إضافة أول قسم
            </Link>
          </div>
        </div>
      ) : (
        <DataTable
          data={departments}
          columns={columns}
          loading={loading}
          searchPlaceholder="البحث في الأقسام..."
          pageSize={10}
        />
      )}
    </div>
  );
}
