"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FaCalendar<PERSON>lt, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ser, FaCheck, FaTimes, FaClock, FaExclamationTriangle, FaPlus, FaEdit, FaTrash, FaPrint } from "react-icons/fa";

interface AttendanceData {
  employeeId: string;
  employeeName: string;
  employeeNumber: string;
  department: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'PARTIAL' | 'ON_LEAVE';
  totalWorkingHours: number;
  totalBreakTime: number;
  leaveReason?: string;
  movements: {
    id: string;
    checkInTime: string;
    checkOutTime: string | null;
    exitType: string | null;
    entryNumber: number;
    emp_working_hrs?: number;
  }[];
}

interface AttendanceSettings {
  minWorkingHours: number;
  workStartTime: string;
  workEndTime: string;
  workingHours: number; // إزالة علامة الاستفهام لجعلها مطلوبة
}

export default function AttendancePage() {
  const { data: session } = useSession();
  
  // التحقق من الصلاحيات - نقل هذا إلى أعلى
  const userRole = session?.user?.role;
  const canViewAttendance = userRole === "ADMIN" || userRole === "HR" || userRole === "EMPLOYEE" || userRole === "MANAGER";
  const canModifyAttendance = userRole === "ADMIN" || userRole === "HR";
  
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<AttendanceSettings | null>(null);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    department: '',
    employee: '',
    status: '',
    exitType: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // نموذج الإدخال اليدوي
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [manualEntryData, setManualEntryData] = useState({
    employeeId: '',
    date: new Date().toISOString().split('T')[0],
    checkInTime: '',
    checkOutTime: '',
    exitType: 'OFFICIAL',
    entryNumber: 1,
  });
  const [employees, setEmployees] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState(false);

  // دالة حساب مدة الخروج
  const calculateBreakDuration = (movement: Record<string, any>, nextMovement: Record<string, any>, workEndTime: string = '14:30') => {
    if (!movement.checkOutTime) return null;

    const checkOut = new Date(movement.checkOutTime);
    let nextCheckIn: Date;

    if (nextMovement && nextMovement.checkInTime) {
      nextCheckIn = new Date(nextMovement.checkInTime);
    } else {
      // إذا لم يكن هناك دخول تالي، احسب حتى نهاية العمل
      const [hours, minutes] = workEndTime.split(':');
      nextCheckIn = new Date(checkOut);
      nextCheckIn.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // إذا كان وقت الخروج بعد نهاية العمل، لا توجد مدة خروج
      if (checkOut >= nextCheckIn) return null;
    }

    const breakDuration = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
    return breakDuration > 0 ? breakDuration : null;
  };

  // دالة حساب إجمالي مدة الخروج باستخدام النظام المرن لساعات العمل
  const calculateTotalBreakTime = (movements: Record<string, any>[], workEndTime: string = '14:30', workingHours: number = 7) => {
    if (!movements || movements.length === 0) return 0;

    // ترتيب الحركات حسب entryNumber
    const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);
    let totalBreakTime = 0;

    // حساب مدة الخروج بين الحركات
    for (let i = 0; i < sortedMovements.length - 1; i++) {
      const currentMovement = sortedMovements[i];
      const nextMovement = sortedMovements[i + 1];

      // إذا كان الموظف خرج في الحركة الحالية ودخل في الحركة التالية
      if (currentMovement.checkOutTime && nextMovement.checkInTime) {
        const checkOut = new Date(currentMovement.checkOutTime);
        const nextCheckIn = new Date(nextMovement.checkInTime);
        
        const breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
        if (breakHours > 0) {
          totalBreakTime += breakHours;
        }
      }
    }

    // للحركة الأخيرة: إذا خرج الموظف ولم يعد
    const lastMovement = sortedMovements[sortedMovements.length - 1];
    if (lastMovement.checkOutTime) {
      const firstMovement = sortedMovements[0];
      
      // إذا كان هناك دخول في الحركة الأولى وخروج في الحركة الأخيرة ولم يعد
      if (firstMovement.checkInTime) {
        // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
        if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
          // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
          const expectedEndTime = new Date(firstMovement.emp_working_hrs);
          const checkOut = new Date(lastMovement.checkOutTime);
          
          // إذا خرج قبل الوقت المتوقع
          if (checkOut < expectedEndTime) {
            const breakTime = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
            if (breakTime > 0) {
              totalBreakTime += breakTime;
            }
          }
        } else {
          // النظام القديم: حساب بناءً على ساعات العمل الثابتة
          const checkIn = new Date(firstMovement.checkInTime);
          const checkOut = new Date(lastMovement.checkOutTime);
          
          // حساب الوقت الفعلي الذي قضاه الموظف في العمل
          const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
          
          // إذا كان الوقت الفعلي أقل من عدد ساعات العمل المطلوبة
          if (actualWorkHours < workingHours) {
            // مدة الخروج = عدد ساعات العمل - الوقت الفعلي
            const breakTime = workingHours - actualWorkHours;
            if (breakTime > 0) {
              totalBreakTime += breakTime;
            }
          }
        }
      } else {
        // إذا لم يكن هناك دخول، استخدم المنطق القديم
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // إنشاء وقت انتهاء العمل لنفس التاريخ
        const [hours, minutes] = workEndTime.split(':');
        const nextCheckIn = new Date(checkOut.getFullYear(), checkOut.getMonth(), checkOut.getDate(), parseInt(hours), parseInt(minutes), 0, 0);
        
        // إذا كان وقت الخروج قبل نهاية العمل
        if (checkOut < nextCheckIn) {
          const breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
          if (breakHours > 0) {
            totalBreakTime += breakHours;
          }
        }
      }
    }

    return totalBreakTime;
  };

  // دالة تحويل الساعات إلى تنسيق ساعات ودقائق
  const formatDuration = (hours: number) => {
    if (hours <= 0) return '-';
    
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    
    if (wholeHours === 0) {
      return `${minutes} دقيقة`;
    } else if (minutes === 0) {
      return `${wholeHours} ساعة`;
    } else {
      return `${wholeHours} ساعة و ${minutes} دقيقة`;
    }
  };

  // دالة تنسيق ساعات العمل لعرضها تحت اسم الموظف
  const formatWorkingHours = (hours: number) => {
    if (hours <= 0) return 'لا توجد ساعات';
    
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    
    if (wholeHours === 0) {
      return `${minutes} دقيقة عمل`;
    } else if (minutes === 0) {
      return `${wholeHours} ساعة عمل`;
    } else {
      return `${wholeHours} ساعة و ${minutes} دقيقة عمل`;
    }
  };

  const columns: Array<{
    key: string;
    title: string;
    render?: (value: any, row: AttendanceData) => React.JSX.Element;
  }> = [
    {
      key: 'date',
      title: 'التاريخ',
      render: (value: string) => {
        const date = new Date(value);
        const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

        const weekday = weekdays[date.getDay()];
        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        return (
          <span className="font-medium text-gray-900" key={`date-${value}`}>
            {weekday} {day} {month} {year}م
          </span>
        );
      },
    },
    {
      key: 'employeeName',
      title: 'الموظف',
      render: (value: string, row: AttendanceData) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-100 rounded-full">
            <FaUser className="text-gray-600" size={16} />
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">رقم: {row.employeeNumber}</div>
            <div className="text-xs text-blue-600 mt-1">
              {formatWorkingHours(row.totalWorkingHours)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'department',
      title: 'القسم',
      render: (value: string) => <span className="text-gray-600">{value || 'غير محدد'}</span>,
    },
    {
      key: 'status',
      title: 'الحالة',
      render: (value: string, row: AttendanceData) => {
        const statusConfig = {
          PRESENT: { label: 'حاضر', color: 'bg-green-100 text-green-800' },
          ABSENT: { label: 'غائب', color: 'bg-red-100 text-red-800' },
          PARTIAL: { label: 'جزئي', color: 'bg-yellow-100 text-yellow-800' },
          ON_LEAVE: { label: 'إجازة', color: 'bg-blue-100 text-blue-800' },
        };

        const config = statusConfig[value as keyof typeof statusConfig];
        return (
          <div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
              {config.label}
            </span>
            {row.leaveReason && value === 'ON_LEAVE' && (
              <div className="text-xs text-gray-600 mt-1">
                {row.leaveReason}
              </div>
            )}
          </div>
        );
      },
    },

    {
      key: 'totalBreakTime',
      title: 'إجمالي الخروج',
      render: (value: number, row: AttendanceData) => {
        const totalBreakTime = calculateTotalBreakTime(
          row.movements, 
          settings?.workEndTime || '14:30',
          settings?.workingHours || 7
        );

        return (
          <span className="font-medium text-orange-600">
            {formatDuration(totalBreakTime)}
          </span>
        );
      },
    },
    {
      key: 'movements',
      title: 'الحركات',
      render: (movements: AttendanceData['movements'], row: AttendanceData) => {
        // ترتيب الحركات حسب entryNumber
        const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);
        
        return (
          <div className="space-y-1">
            {sortedMovements.length === 0 ? (
              <span className="text-gray-400 text-sm">لا توجد حركات</span>
            ) : (
              sortedMovements.map((movement, index) => {
                const nextMovement = sortedMovements[index + 1];
                let breakDuration = '';

                // حساب مدة الخروج لكل حركة لها خروج
                if (movement.checkOutTime) {
                  const checkOut = new Date(movement.checkOutTime);
                  let breakHours = 0;

                  if (nextMovement && nextMovement.checkInTime) {
                    // إذا كان هناك حركة تالية، احسب المدة حتى الدخول التالي
                    const nextCheckIn = new Date(nextMovement.checkInTime);
                    breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                    if (breakHours > 0) {
                      breakDuration = ` (${formatDuration(breakHours)})`;
                    }
                  } else {
                    // إذا لم يعد الموظف (الحركة الأخيرة)
                    const firstMovement = sortedMovements[0];
                    
                    if (firstMovement.checkInTime) {
                      // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
                      if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
                        // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
                        const expectedEndTime = new Date(firstMovement.emp_working_hrs);
                        
                        // إذا خرج قبل الوقت المتوقع
                        if (checkOut < expectedEndTime) {
                          breakHours = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                          if (breakHours > 0) {
                            breakDuration = ` (${formatDuration(breakHours)})`;
                          }
                        }
                      } else {
                        // النظام القديم: حساب بناءً على ساعات العمل الثابتة
                        const checkIn = new Date(firstMovement.checkInTime);
                        const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
                        const workingHours = settings?.workingHours || 7;
                        
                        if (actualWorkHours < workingHours) {
                          breakHours = workingHours - actualWorkHours;
                          if (breakHours > 0) {
                            breakDuration = ` (${formatDuration(breakHours)})`;
                          }
                        }
                      }
                    } else {
                      // إذا لم يكن هناك دخول، استخدم المنطق القديم
                      const workEndTime = settings?.workEndTime || '14:30';
                      const [hours, minutes] = workEndTime.split(':');
                      const endOfWorkTime = new Date(checkOut.getFullYear(), checkOut.getMonth(), checkOut.getDate(), parseInt(hours), parseInt(minutes), 0, 0);
                  
                      if (checkOut < endOfWorkTime) {
                        breakHours = (endOfWorkTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                        if (breakHours > 0) {
                          breakDuration = ` (${formatDuration(breakHours)})`;
                        }
                      }
                    }
                  }
                }

                return (
                  <div key={movement.id} className="text-xs bg-gray-50 p-2 rounded mb-1">
                    <div className="flex items-center gap-2 flex-wrap">
                      <span className="font-medium text-indigo-600">#{movement.entryNumber}</span>

                      <span className="flex items-center gap-1">
                        <FaCheck className="text-green-500" size={10} />
                        {new Date(movement.checkInTime).toLocaleTimeString('ar-SA', {
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true
                        })}
                      </span>

                      {movement.checkOutTime && (
                        <>
                          <span className="flex items-center gap-1">
                            <FaTimes className="text-red-500" size={10} />
                            {new Date(movement.checkOutTime).toLocaleTimeString('ar-SA', {
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true
                            })}
                          </span>

                          {breakDuration && (
                            <span className="text-orange-600 font-medium">
                              {breakDuration}
                            </span>
                          )}
                        </>
                      )}

                      {movement.exitType && (
                        <span className={`px-1.5 py-0.5 rounded text-xs ${
                          movement.exitType === 'OFFICIAL'
                            ? 'bg-green-100 text-green-700'
                            : movement.exitType === 'WORK'
                            ? 'bg-blue-100 text-blue-700'
                            : movement.exitType === 'HEALTH'
                            ? 'bg-purple-100 text-purple-700'
                            : 'bg-orange-100 text-orange-700'
                        }`}>
                          {movement.exitType === 'OFFICIAL' ? 'رسمي' :
                           movement.exitType === 'WORK' ? 'عمل' :
                           movement.exitType === 'HEALTH' ? 'صحي' : 'شخصي'}
                        </span>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      render: (value: any, row: AttendanceData) => (
        <div className="flex items-center gap-2">
          {canModifyAttendance && row.movements.length > 0 && (
            <>
              <button
                onClick={() => handleEditRecord(row)}
                className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                title="تعديل"
              >
                <FaEdit size={14} />
              </button>
              <button
                onClick={() => handleDeleteRecord(row.movements[0].id)}
                className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors"
                title="حذف"
              >
                <FaTrash size={14} />
              </button>
            </>
          )}
          {!canModifyAttendance && (
            <span className="text-gray-400 text-sm">عرض فقط</span>
          )}
        </div>
      ),
    },
  ];

  const fetchAttendanceData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        startDate: filters.startDate,
        endDate: filters.endDate,
        ...(filters.department && { department: filters.department }),
        ...(filters.employee && { employee: filters.employee }),
        ...(filters.status && { status: filters.status }),
        ...(filters.exitType && { exitType: filters.exitType }),
      });

      // إضافة فلتر حسب الدور
      if (userRole === "EMPLOYEE") {
        // الموظف يرى سجلاته فقط
        params.set('employee', session?.user?.id || '');
      } else if (userRole === "MANAGER") {
        // المدير يرى سجلات موظفيه فقط (سيتم تطبيق هذا في API)
        params.set('managerFilter', 'true');
      }

      const response = await fetch(`/api/attendance/range-report?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // تحويل البيانات المجمعة إلى مصفوفة مسطحة مع معالجة أفضل للأخطاء
      const flatData: AttendanceData[] = [];
      if (data.attendanceData && typeof data.attendanceData === 'object') {
        Object.entries(data.attendanceData).forEach(([date, records]: [string, any]) => {
          if (Array.isArray(records)) {
            flatData.push(...records);
          }
        });
      }

      setAttendanceData(flatData);
      setSettings(data.settings || {
        minWorkingHours: 4,
        workStartTime: '08:00',
        workEndTime: '14:30',
        workingHours: 7
      });
    } catch (error) {
      console.error('خطأ في جلب بيانات الحضور:', error);
      // في حالة الخطأ، تعيين بيانات فارغة بدلاً من ترك الصفحة بيضاء
      setAttendanceData([]);
      setSettings({
        minWorkingHours: 4,
        workStartTime: '08:00',
        workEndTime: '14:30',
        workingHours: 7
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttendanceData();
  }, [filters]);

  // دوال جلب البيانات
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setEmployees(data.filter((user: any) =>
          ['EMPLOYEE', 'MANAGER', 'HR'].includes(user.role)
        ));
      }
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await fetch('/api/departments');
      if (response.ok) {
        const data = await response.json();
        setDepartments(data || []);
      } else {
        console.error('خطأ في جلب الأقسام:', response.status);
      }
    } catch (error) {
      console.error('خطأ في جلب الأقسام:', error);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchEmployees();
      fetchDepartments();
    }
  }, [session]);

  useEffect(() => {
    // فلترة الموظفين حسب القسم المختار
    if (filters.department) {
      const filtered = employees.filter(emp =>
        emp.department?.name === filters.department
      );
      setFilteredEmployees(filtered);
    } else {
      setFilteredEmployees(employees);
    }
    // إعادة تعيين الموظف المختار إذا لم يعد في القسم الجديد
    if (filters.employee && filters.department) {
      const employeeInDepartment = employees.find(emp =>
        emp.id === filters.employee && emp.department?.name === filters.department
      );
      if (!employeeInDepartment) {
        setFilters(prev => ({...prev, employee: ''}));
      }
    }
  }, [filters.department, employees]);

  if (!canViewAttendance) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto text-red-500 mb-4" size={48} />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">غير مصرح لك بالوصول</h2>
          <p className="text-gray-600">هذه الصفحة متاحة للموظفين، المدراء، الموارد البشرية، ومدير النظام</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الحضور...</p>
        </div>
      </div>
    );
  }

  const handleEditRecord = (row: AttendanceData) => {
    if (row.movements.length > 0) {
      // إذا كان هناك حركة واحدة فقط، اختارها مباشرة
      if (row.movements.length === 1) {
        const movement = row.movements[0];
        setEditingRecord(movement);
        setManualEntryData({
          employeeId: row.employeeId,
          date: row.date,
          checkInTime: movement.checkInTime ? new Date(movement.checkInTime).toTimeString().slice(0, 5) : '',
          checkOutTime: movement.checkOutTime ? new Date(movement.checkOutTime).toTimeString().slice(0, 5) : '',
          exitType: movement.exitType || 'OFFICIAL',
          entryNumber: movement.entryNumber,
        });
        setShowManualEntry(true);
      } else {
        // إذا كان هناك عدة حركات، اعرض قائمة للاختيار
        setEditingRecord({ employeeData: row, movements: row.movements });
        setShowManualEntry(true);
      }
    }
  };

  const handleSelectMovement = (movement: any) => {
    setManualEntryData({
      employeeId: editingRecord.employeeData.employeeId,
      date: editingRecord.employeeData.date,
      checkInTime: movement.checkInTime ? new Date(movement.checkInTime).toTimeString().slice(0, 5) : '',
      checkOutTime: movement.checkOutTime ? new Date(movement.checkOutTime).toTimeString().slice(0, 5) : '',
      exitType: movement.exitType || 'OFFICIAL',
      entryNumber: movement.entryNumber,
    });
    setEditingRecord(movement);
  };

  const handleManualEntry = async () => {
    try {
      setSubmitting(true);

      const requestData = {
        ...manualEntryData,
        checkInTime: manualEntryData.checkInTime ?
          `${manualEntryData.date}T${manualEntryData.checkInTime}:00` : null,
        checkOutTime: manualEntryData.checkOutTime ?
          `${manualEntryData.date}T${manualEntryData.checkOutTime}:00` : null,
      };

      const url = editingRecord ?
        `/api/attendance/manual/${editingRecord.id}` :
        '/api/attendance/manual';

      const method = editingRecord ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        setShowManualEntry(false);
        setEditingRecord(null);
        setManualEntryData({
          employeeId: '',
          date: new Date().toISOString().split('T')[0],
          checkInTime: '',
          checkOutTime: '',
          exitType: 'OFFICIAL',
          entryNumber: 1,
        });
        fetchAttendanceData();
      } else {
        const error = await response.json();
        alert(error.error || 'حدث خطأ في حفظ البيانات');
      }
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      alert('حدث خطأ في حفظ البيانات');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteRecord = async (recordId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/attendance/manual/${recordId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchAttendanceData();
      } else {
        const error = await response.json();
        alert(error.error || 'حدث خطأ في حذف السجل');
      }
    } catch (error) {
      console.error('خطأ في حذف السجل:', error);
      alert('حدث خطأ في حذف السجل');
    }
  };

  const getStatusSummary = () => {
    const summary = {
      PRESENT: 0,
      ABSENT: 0,
      PARTIAL: 0,
      ON_LEAVE: 0,
      // إضافة إحصائيات أنواع الاستئذان
      PERSONAL: 0,
      WORK: 0,
      HEALTH: 0,
      OFFICIAL: 0,
      // إضافة إجمالي ساعات الاستئذان
      PERSONAL_HOURS: 0,
      WORK_HOURS: 0,
      HEALTH_HOURS: 0,
      OFFICIAL_HOURS: 0,
    };

    attendanceData.forEach(record => {
      summary[record.status]++;
      
      // حساب أنواع الاستئذان وساعاتها من الحركات
      if (record.movements && record.movements.length > 0) {
        const sortedMovements = [...record.movements].sort((a, b) => a.entryNumber - b.entryNumber);
        
        // حساب مدة كل حركة خروج
        for (let i = 0; i < sortedMovements.length; i++) {
          const movement = sortedMovements[i];
          if (movement.exitType && movement.checkOutTime) {
            // عد نوع الاستئذان
            summary[movement.exitType as keyof typeof summary]++;
            
            // حساب مدة الاستئذان
            let breakHours = 0;
            const checkOut = new Date(movement.checkOutTime);
            const nextMovement = sortedMovements[i + 1];

            if (nextMovement && nextMovement.checkInTime) {
              // إذا كان هناك حركة تالية، احسب المدة حتى الدخول التالي
              const nextCheckIn = new Date(nextMovement.checkInTime);
              breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
            } else {
              // إذا لم يعد الموظف (الحركة الأخيرة)
              const firstMovement = sortedMovements[0];
              
              if (firstMovement.checkInTime) {
                // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
                if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
                  // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
                  const expectedEndTime = new Date(firstMovement.emp_working_hrs);
                  
                  // إذا خرج قبل الوقت المتوقع
                  if (checkOut < expectedEndTime) {
                    breakHours = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                  }
                } else {
                  // النظام القديم: حساب بناءً على ساعات العمل الثابتة
                  const checkIn = new Date(firstMovement.checkInTime);
                  const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
                  const workingHours = settings?.workingHours || 7;
                  
                  if (actualWorkHours < workingHours) {
                    breakHours = workingHours - actualWorkHours;
                  }
                }
              } else {
                // إذا لم يكن هناك دخول، استخدم المنطق القديم
                const workEndTime = settings?.workEndTime || '14:30';
                const [hours, minutes] = workEndTime.split(':');
                const endOfWorkTime = new Date(checkOut.getFullYear(), checkOut.getMonth(), checkOut.getDate(), parseInt(hours), parseInt(minutes), 0, 0);
            
                if (checkOut < endOfWorkTime) {
                  breakHours = (endOfWorkTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                }
              }
            }

            // إضافة الساعات لنوع الاستئذان المناسب
            if (breakHours > 0) {
              const exitTypeKey = `${movement.exitType}_HOURS` as keyof typeof summary;
              if (exitTypeKey in summary) {
                summary[exitTypeKey] += breakHours;
              }
            }
          }
        }
      }
    });

    return summary;
  };

  const handlePrintReport = () => {
    try {
      // التحقق من وجود بيانات للطباعة
      if (!attendanceData || attendanceData.length === 0) {
        alert('لا توجد بيانات للطباعة');
        return;
      }

      // فتح نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        return;
      }

      // حساب ملخص الحالات
      const statusSummary = getStatusSummary();

      // دالة مساعدة لتنسيق التاريخ
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        return weekdays[date.getDay()] + ' ' + date.getDate() + ' ' + months[date.getMonth()] + ' ' + date.getFullYear() + 'م';
      };

      // دالة مساعدة لتنسيق الوقت
      const formatTime = (timeString: string) => {
        try {
          return new Date(timeString).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
        } catch {
          return '-';
        }
      };

      // دالة تحويل الساعات إلى تنسيق ساعات ودقائق للطباعة
      const formatDurationForPrint = (hours: number) => {
        if (hours <= 0) return '-';
        
        const wholeHours = Math.floor(hours);
        const minutes = Math.round((hours - wholeHours) * 60);
        
        if (wholeHours === 0) {
          return `${minutes} دقيقة`;
        } else if (minutes === 0) {
          return `${wholeHours} ساعة`;
        } else {
          return `${wholeHours} ساعة و ${minutes} دقيقة`;
        }
      };

      // دالة تنسيق ساعات العمل للطباعة
      const formatWorkingHoursForPrint = (hours: number) => {
        if (hours <= 0) return 'لا توجد ساعات';
        
        const wholeHours = Math.floor(hours);
        const minutes = Math.round((hours - wholeHours) * 60);
        
        if (wholeHours === 0) {
          return `${minutes} دقيقة عمل`;
        } else if (minutes === 0) {
          return `${wholeHours} ساعة عمل`;
        } else {
          return `${wholeHours} ساعة و ${minutes} دقيقة عمل`;
        }
      };

      // بناء HTML للطباعة
      let htmlContent = '<!DOCTYPE html>';
      htmlContent += '<html dir="rtl" lang="ar">';
      htmlContent += '<head>';
      htmlContent += '<meta charset="UTF-8">';
      htmlContent += '<title>تقرير الحضور والانصراف</title>';
      htmlContent += '<style>';
      htmlContent += 'body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }';
      htmlContent += '.header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }';
      htmlContent += '.header h1 { color: #333; margin: 0; font-size: 24px; }';
      htmlContent += '.header p { color: #666; margin: 5px 0; }';
      htmlContent += '.summary { margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; }';
      htmlContent += '.summary-section { margin-bottom: 15px; }';
      htmlContent += '.summary-title { text-align: center; margin-bottom: 10px; color: #333; font-size: 14px; font-weight: bold; }';
      htmlContent += '.summary-row { display: flex; justify-content: space-around; margin-bottom: 10px; }';
      htmlContent += '.summary-item { text-align: center; }';
      htmlContent += '.summary-number { font-size: 18px; font-weight: bold; }';
      htmlContent += '.summary-label { font-size: 12px; color: #666; }';
      htmlContent += '.summary-hours { font-size: 10px; color: #888; margin-top: 2px; }';
      
      // إضافة تنسيقات الجداول
      htmlContent += '.attendance-table { width: 100%; border-collapse: collapse; margin: 20px 0; }';
      htmlContent += '.attendance-table th, .attendance-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }';
      htmlContent += '.attendance-table th { background-color: #f5f5f5; font-weight: bold; font-size: 12px; }';
      htmlContent += '.attendance-table td { font-size: 11px; }';
      htmlContent += '.date-header { background-color: #e3f2fd; padding: 10px; margin: 15px 0 5px 0; font-weight: bold; color: #1976d2; border-radius: 4px; }';
      htmlContent += '.status-badge { padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: bold; }';
      htmlContent += '.status-present { background-color: #e8f5e8; color: #2e7d32; }';
      htmlContent += '.status-absent { background-color: #ffebee; color: #c62828; }';
      htmlContent += '.status-partial { background-color: #fff3e0; color: #ef6c00; }';
      htmlContent += '.status-leave { background-color: #e3f2fd; color: #1976d2; }';
      htmlContent += '.movement-item { background-color: #f9f9f9; margin: 2px 0; padding: 4px; border-radius: 3px; font-size: 10px; }';
      htmlContent += '.exit-type { padding: 1px 4px; border-radius: 8px; font-size: 9px; margin-right: 4px; }';
      htmlContent += '.exit-official { background-color: #e8f5e8; color: #2e7d32; }';
      htmlContent += '.exit-personal { background-color: #fff3e0; color: #ef6c00; }';
      htmlContent += '.exit-work { background-color: #e3f2fd; color: #1976d2; }';
      htmlContent += '.exit-health { background-color: #f3e5f5; color: #7b1fa2; }';
      htmlContent += '.break-duration { color: #ff5722; font-weight: bold; }';
      
      htmlContent += '@media print { body { margin: 0; } .no-print { display: none; } }';
      htmlContent += '</style>';
      htmlContent += '</head>';
      htmlContent += '<body>';

      // Header
      htmlContent += '<div class="header">';
      htmlContent += '<h1>تقرير الحضور والانصراف</h1>';
      htmlContent += '<p>من ' + formatDate(filters.startDate) + ' إلى ' + formatDate(filters.endDate) + '</p>';
      if (filters.employee) {
        const selectedEmployee = filteredEmployees.find(emp => emp.id === filters.employee);
        if (selectedEmployee) {
          htmlContent += '<p>الموظف: ' + selectedEmployee.name + ' - رقم: ' + selectedEmployee.employeeNumber + '</p>';
        }
      }
      if (filters.department) {
        htmlContent += '<p>القسم: ' + filters.department + '</p>';
      }
      htmlContent += '<p>تاريخ الطباعة: ' + formatDate(new Date().toISOString()) + ' - ' + formatTime(new Date().toISOString()) + '</p>';
      htmlContent += '</div>';

      // Summary
      htmlContent += '<div class="summary">';
      
      // إحصائيات الحضور
      htmlContent += '<div class="summary-section">';
      htmlContent += '<div class="summary-title">حالات الحضور</div>';
      htmlContent += '<div class="summary-row">';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #4caf50;">' + statusSummary.PRESENT + '</div><div class="summary-label">حاضر</div></div>';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #ff9800;">' + statusSummary.PARTIAL + '</div><div class="summary-label">جزئي</div></div>';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #f44336;">' + statusSummary.ABSENT + '</div><div class="summary-label">غائب</div></div>';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #2196f3;">' + statusSummary.ON_LEAVE + '</div><div class="summary-label">إجازة</div></div>';
      htmlContent += '</div></div>';

      // إحصائيات أنواع الاستئذان
      htmlContent += '<div class="summary-section" style="border-top: 1px solid #ddd; padding-top: 10px;">';
      htmlContent += '<div class="summary-title">أنواع الاستئذان</div>';
      htmlContent += '<div class="summary-row">';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #ff9800;">' + statusSummary.PERSONAL + '</div><div class="summary-label">شخصي</div><div class="summary-hours">' + formatDurationForPrint(statusSummary.PERSONAL_HOURS) + '</div></div>';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #2196f3;">' + statusSummary.WORK + '</div><div class="summary-label">عمل</div><div class="summary-hours">' + formatDurationForPrint(statusSummary.WORK_HOURS) + '</div></div>';
      htmlContent += '<div class="summary-item"><div class="summary-number" style="color: #9c27b0;">' + statusSummary.HEALTH + '</div><div class="summary-label">صحي</div><div class="summary-hours">' + formatDurationForPrint(statusSummary.HEALTH_HOURS) + '</div></div>';
      htmlContent += '</div></div>';
      
      htmlContent += '</div>';

      // تفاصيل الحضور
      htmlContent += '<h2 style="margin-top: 30px; color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;">تفاصيل الحضور</h2>';

      // تجميع البيانات حسب التاريخ أو الموظف
      if (filters.employee) {
        // إذا كان هناك فلتر للموظف، اجمع حسب الموظف
        const groupedByEmployee = attendanceData.reduce((groups: any, item) => {
          const employeeKey = `${item.employeeName}-${item.employeeNumber}`;
          if (!groups[employeeKey]) {
            groups[employeeKey] = {
              employee: item,
              records: []
            };
          }
          groups[employeeKey].records.push(item);
          return groups;
        }, {});

        Object.entries(groupedByEmployee).forEach(([employeeKey, groupData]: [string, any]) => {
          // عنوان الموظف
          htmlContent += '<div class="date-header">';
          htmlContent += '<strong>' + groupData.employee.employeeName + ' - رقم: ' + groupData.employee.employeeNumber + '</strong>';
          htmlContent += ' | القسم: ' + (groupData.employee.department || 'غير محدد');
          htmlContent += ' | إجمالي الأيام: ' + groupData.records.length;
          htmlContent += '</div>';
          
          // جدول أيام الحضور للموظف
          htmlContent += '<table class="attendance-table">';
          htmlContent += '<thead><tr>';
          htmlContent += '<th>التاريخ</th>';
          htmlContent += '<th>الحالة</th>';
          htmlContent += '<th>ساعات العمل</th>';
          htmlContent += '<th>إجمالي الخروج</th>';
          htmlContent += '<th>الحركات</th>';
          htmlContent += '</tr></thead><tbody>';

          groupData.records
            .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .forEach((record: AttendanceData) => {
              htmlContent += '<tr>';
              
              // التاريخ
              htmlContent += '<td>' + formatDate(record.date) + '</td>';
              
              // الحالة
              htmlContent += '<td>';
              const statusConfig: any = {
                PRESENT: { label: 'حاضر', class: 'status-present' },
                ABSENT: { label: 'غائب', class: 'status-absent' },
                PARTIAL: { label: 'جزئي', class: 'status-partial' },
                ON_LEAVE: { label: 'إجازة', class: 'status-leave' },
              };
              const config = statusConfig[record.status];
              htmlContent += '<span class="status-badge ' + config.class + '">' + config.label + '</span>';
              if (record.leaveReason && record.status === 'ON_LEAVE') {
                htmlContent += '<br><small>' + record.leaveReason + '</small>';
              }
              htmlContent += '</td>';

              // ساعات العمل
              htmlContent += '<td>' + formatWorkingHoursForPrint(record.totalWorkingHours) + '</td>';

              // إجمالي الخروج
              const totalBreakTime = calculateTotalBreakTime(
                record.movements, 
                settings?.workEndTime || '14:30',
                settings?.workingHours || 7
              );
              htmlContent += '<td class="break-duration">' + formatDurationForPrint(totalBreakTime) + '</td>';

              // الحركات
              htmlContent += '<td>';
              if (record.movements.length === 0) {
                htmlContent += '<span style="color: #999;">لا توجد حركات</span>';
              } else {
                const sortedMovements = [...record.movements].sort((a, b) => a.entryNumber - b.entryNumber);
                sortedMovements.forEach((movement, index) => {
                  const nextMovement = sortedMovements[index + 1];
                  
                  htmlContent += '<div class="movement-item">';
                  htmlContent += '<strong>#' + movement.entryNumber + '</strong> ';
                  htmlContent += 'دخول: ' + formatTime(movement.checkInTime) + ' ';
                  
                  if (movement.checkOutTime) {
                    htmlContent += 'خروج: ' + formatTime(movement.checkOutTime) + ' ';
                    
                    // حساب مدة الخروج
                    let breakDuration = '';
                    const checkOut = new Date(movement.checkOutTime);
                    let breakHours = 0;

                    if (nextMovement && nextMovement.checkInTime) {
                const nextCheckIn = new Date(nextMovement.checkInTime);
                      breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                      if (breakHours > 0) {
                        breakDuration = formatDurationForPrint(breakHours);
                      }
                    } else {
              const firstMovement = sortedMovements[0];
              if (firstMovement.checkInTime) {
                const checkIn = new Date(firstMovement.checkInTime);
                const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
                const workingHours = settings?.workingHours || 7;
                
                if (actualWorkHours < workingHours) {
                          breakHours = workingHours - actualWorkHours;
                          if (breakHours > 0) {
                            breakDuration = formatDurationForPrint(breakHours);
                          }
                        }
                      }
                    }

                    if (breakDuration) {
                      htmlContent += '<span class="break-duration">(' + breakDuration + ')</span> ';
                    }
                  }

                  if (movement.exitType) {
                    const exitTypeClass = movement.exitType === 'OFFICIAL' ? 'exit-official' :
                                        movement.exitType === 'WORK' ? 'exit-work' :
                                        movement.exitType === 'HEALTH' ? 'exit-health' : 'exit-personal';
                    
                    const exitTypeLabel = movement.exitType === 'OFFICIAL' ? 'رسمي' :
                                        movement.exitType === 'WORK' ? 'عمل' :
                                        movement.exitType === 'HEALTH' ? 'صحي' : 'شخصي';
                    
                    htmlContent += '<span class="exit-type ' + exitTypeClass + '">' + exitTypeLabel + '</span>';
                  }
                  
                  htmlContent += '</div>';
                });
              }
              htmlContent += '</td>';
              
              htmlContent += '</tr>';
            });

          htmlContent += '</tbody></table>';
        });
      } else {
        // إذا لم يكن هناك فلتر للموظف، اجمع حسب التاريخ
        const groupedByDate = attendanceData.reduce((groups: any, item) => {
          const date = item.date;
          if (!groups[date]) {
            groups[date] = [];
          }
          groups[date].push(item);
          return groups;
        }, {});

        Object.entries(groupedByDate)
          .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
          .forEach(([date, records]: [string, any]) => {
            // عنوان التاريخ
            htmlContent += '<div class="date-header">';
            htmlContent += '<strong>' + formatDate(date) + '</strong>';
            htmlContent += ' | إجمالي: ' + records.length + ' موظف';
            htmlContent += ' | حاضر: ' + records.filter((r: any) => r.status === 'PRESENT').length;
            htmlContent += ' | جزئي: ' + records.filter((r: any) => r.status === 'PARTIAL').length;
            htmlContent += ' | غائب: ' + records.filter((r: any) => r.status === 'ABSENT').length;
            htmlContent += ' | إجازة: ' + records.filter((r: any) => r.status === 'ON_LEAVE').length;
            htmlContent += '</div>';

            // جدول الموظفين لهذا التاريخ
            htmlContent += '<table class="attendance-table">';
            htmlContent += '<thead><tr>';
            htmlContent += '<th>الموظف</th>';
            htmlContent += '<th>القسم</th>';
            htmlContent += '<th>الحالة</th>';
            htmlContent += '<th>ساعات العمل</th>';
            htmlContent += '<th>إجمالي الخروج</th>';
            htmlContent += '<th>الحركات</th>';
            htmlContent += '</tr></thead><tbody>';

            records.forEach((record: AttendanceData) => {
              htmlContent += '<tr>';
              
              // الموظف
              htmlContent += '<td>';
              htmlContent += '<strong>' + record.employeeName + '</strong><br>';
              htmlContent += '<small>رقم: ' + record.employeeNumber + '</small>';
              htmlContent += '</td>';

              // القسم
              htmlContent += '<td>' + (record.department || 'غير محدد') + '</td>';

              // الحالة
              htmlContent += '<td>';
              const statusConfig: any = {
                PRESENT: { label: 'حاضر', class: 'status-present' },
                ABSENT: { label: 'غائب', class: 'status-absent' },
                PARTIAL: { label: 'جزئي', class: 'status-partial' },
                ON_LEAVE: { label: 'إجازة', class: 'status-leave' },
              };
              const config = statusConfig[record.status];
              htmlContent += '<span class="status-badge ' + config.class + '">' + config.label + '</span>';
              if (record.leaveReason && record.status === 'ON_LEAVE') {
                htmlContent += '<br><small>' + record.leaveReason + '</small>';
              }
              htmlContent += '</td>';

              // ساعات العمل
              htmlContent += '<td>' + formatWorkingHoursForPrint(record.totalWorkingHours) + '</td>';

              // إجمالي الخروج
              const totalBreakTime = calculateTotalBreakTime(
                record.movements, 
                settings?.workEndTime || '14:30',
                settings?.workingHours || 7
              );
              htmlContent += '<td class="break-duration">' + formatDurationForPrint(totalBreakTime) + '</td>';

              // الحركات
              htmlContent += '<td>';
              if (record.movements.length === 0) {
                htmlContent += '<span style="color: #999;">لا توجد حركات</span>';
              } else {
                const sortedMovements = [...record.movements].sort((a, b) => a.entryNumber - b.entryNumber);
                sortedMovements.forEach((movement, index) => {
                  const nextMovement = sortedMovements[index + 1];
                  
                  htmlContent += '<div class="movement-item">';
                  htmlContent += '<strong>#' + movement.entryNumber + '</strong> ';
                  htmlContent += 'دخول: ' + formatTime(movement.checkInTime) + ' ';
                  
              if (movement.checkOutTime) {
                    htmlContent += 'خروج: ' + formatTime(movement.checkOutTime) + ' ';
                    
                    // حساب مدة الخروج
                    let breakDuration = '';
                    const checkOut = new Date(movement.checkOutTime);
                let breakHours = 0;

                if (nextMovement && nextMovement.checkInTime) {
                  const nextCheckIn = new Date(nextMovement.checkInTime);
                      breakHours = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
                  if (breakHours > 0) {
                        breakDuration = formatDurationForPrint(breakHours);
                  }
                } else {
                  const firstMovement = sortedMovements[0];
                  if (firstMovement.checkInTime) {
                    const checkIn = new Date(firstMovement.checkInTime);
                        const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
                    const workingHours = settings?.workingHours || 7;
                    
                    if (actualWorkHours < workingHours) {
                      breakHours = workingHours - actualWorkHours;
                      if (breakHours > 0) {
                            breakDuration = formatDurationForPrint(breakHours);
                          }
                        }
                      }
                    }
              
              if (breakDuration) {
                      htmlContent += '<span class="break-duration">(' + breakDuration + ')</span> ';
                    }
                  }

                  if (movement.exitType) {
                    const exitTypeClass = movement.exitType === 'OFFICIAL' ? 'exit-official' :
                                        movement.exitType === 'WORK' ? 'exit-work' :
                                        movement.exitType === 'HEALTH' ? 'exit-health' : 'exit-personal';
                    
                    const exitTypeLabel = movement.exitType === 'OFFICIAL' ? 'رسمي' :
                                        movement.exitType === 'WORK' ? 'عمل' :
                                        movement.exitType === 'HEALTH' ? 'صحي' : 'شخصي';
                    
                    htmlContent += '<span class="exit-type ' + exitTypeClass + '">' + exitTypeLabel + '</span>';
                  }
                  
                  htmlContent += '</div>';
                });
              }
              htmlContent += '</td>';
          
          htmlContent += '</tr>';
        });

            htmlContent += '</tbody></table>';
      });
      }

      htmlContent += '<p style="text-align: center; margin-top: 20px; color: #666; font-style: italic;">تم إنشاء هذا التقرير تلقائياً من نظام إدارة الحضور والانصراف</p>';
      htmlContent += '</body></html>';

      // كتابة المحتوى وطباعته
      printWindow.document.open();
      printWindow.document.write(htmlContent);
      printWindow.document.close();

      // انتظار تحميل المحتوى ثم الطباعة
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
      }, 1000);

    } catch (error) {
      console.error('خطأ في إنشاء تقرير الطباعة:', error);
      alert('حدث خطأ في إنشاء تقرير الطباعة: ' + (error as Error).message);
    }
  };

  const statusSummary = getStatusSummary();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-white/20 rounded-full">
            <FaCalendarAlt size={32} />
          </div>
          <div>
            <h1 className="text-3xl font-bold">كشف الحضور والانصراف</h1>
            <p className="text-indigo-100 mt-1">
              تقرير شامل لحضور جميع الموظفين - من {new Date(filters.startDate).toLocaleDateString('en-GB')} إلى {new Date(filters.endDate).toLocaleDateString('en-GB')}
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
              <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 text-lg font-semibold text-gray-900 hover:text-indigo-600 transition-colors"
          >
            <FaSearch size={16} />
            فلاتر البحث
            <span className={`transform transition-transform ${showFilters ? 'rotate-180' : ''}`}>
              ▼
            </span>
              </button>
          </div>

        {showFilters && (
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* فترة التاريخ */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
              <input
                type="date"
                value={filters.startDate}
                  onChange={(e) => setFilters({...filters, startDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
              <input
                type="date"
                value={filters.endDate}
                  onChange={(e) => setFilters({...filters, endDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>

              {/* القسم */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">القسم</label>
              <select
                value={filters.department}
                  onChange={(e) => setFilters({...filters, department: e.target.value, employee: ''})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="">جميع الأقسام</option>
                  {departments.map(department => (
                    <option key={department.id} value={department.name}>
                      {department.name}
                  </option>
                ))}
                  {departments.length === 0 && (
                    <option disabled>لا توجد أقسام</option>
                  )}
              </select>
            </div>

              {/* الموظف - يظهر للإدارة والموارد البشرية والمدراء فقط */}
            {userRole !== "EMPLOYEE" && (
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الموظف</label>
              <select
                value={filters.employee}
                  onChange={(e) => setFilters({...filters, employee: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="">جميع الموظفين</option>
                  {filteredEmployees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.name} - {employee.employeeNumber}
                  </option>
                ))}
              </select>
            </div>
            )}

              {/* الحالة */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
              <select
                value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                  <option value="">جميع الحالات</option>
                <option value="PRESENT">حاضر</option>
                <option value="ABSENT">غائب</option>
                <option value="PARTIAL">جزئي</option>
                <option value="ON_LEAVE">إجازة</option>
              </select>
            </div>

              {/* نوع الخروج */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الاستئذان</label>
              <select
                value={filters.exitType}
                  onChange={(e) => setFilters({...filters, exitType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                  <option value="">جميع الأنواع</option>
                <option value="OFFICIAL">رسمي</option>
                <option value="PERSONAL">شخصي</option>
                <option value="WORK">عمل</option>
                <option value="HEALTH">صحي</option>
              </select>
            </div>
          </div>

            <div className="flex gap-2 pt-4">
              <button
                onClick={() => setFilters({
                  startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
                  endDate: new Date().toISOString().split('T')[0],
                  department: '',
                  employee: '',
                  status: '',
                  exitType: ''
                })}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إعادة تعيين
              </button>
              <button
                onClick={handlePrintReport}
                disabled={attendanceData.length === 0}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <FaPrint size={14} />
                طباعة التقرير
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">

        {/* Summary */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص الحضور والاستئذان</h3>
          
          {/* إحصائيات الحضور */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">حالات الحضور</h4>
          <div className="flex items-center justify-center gap-8">
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold text-green-600">{statusSummary.PRESENT}</div>
              <div className="text-sm text-gray-600">حاضر</div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold text-yellow-600">{statusSummary.PARTIAL}</div>
              <div className="text-sm text-gray-600">جزئي</div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold text-red-600">{statusSummary.ABSENT}</div>
              <div className="text-sm text-gray-600">غائب</div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold text-blue-600">{statusSummary.ON_LEAVE}</div>
              <div className="text-sm text-gray-600">إجازة</div>
              </div>
            </div>
          </div>

          {/* خط فاصل */}
          <div className="border-t border-gray-200 my-4"></div>

          {/* إحصائيات أنواع الاستئذان */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">أنواع الاستئذان</h4>
            <div className="flex items-center justify-center gap-8">
              <div className="flex items-center gap-2">
                <div className="text-xl font-bold text-orange-600">{statusSummary.PERSONAL}</div>
                <div className="text-sm text-gray-600">
                  شخصي
                  <div className="text-xs text-orange-500 mt-1">
                    {formatDuration(statusSummary.PERSONAL_HOURS)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xl font-bold text-blue-700">{statusSummary.WORK}</div>
                <div className="text-sm text-gray-600">
                  عمل
                  <div className="text-xs text-blue-600 mt-1">
                    {formatDuration(statusSummary.WORK_HOURS)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xl font-bold text-purple-600">{statusSummary.HEALTH}</div>
                <div className="text-sm text-gray-600">
                  صحي
                  <div className="text-xs text-purple-500 mt-1">
                    {formatDuration(statusSummary.HEALTH_HOURS)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">تفاصيل الحضور</h2>
          {canModifyAttendance && (
          <button
            onClick={() => {
              setEditingRecord(null);
              setManualEntryData({
                employeeId: '',
                date: new Date().toISOString().split('T')[0],
                checkInTime: '',
                checkOutTime: '',
                exitType: 'OFFICIAL',
                entryNumber: 1,
              });
              setShowManualEntry(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <FaPlus size={14} />
            إدخال حضور/انصراف يدوي
          </button>
          )}
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            {/* عرض البيانات مجمعة حسب التاريخ أو الموظف */}
            {filters.employee ? (
              // إذا كان هناك فلتر للموظف، اجمع حسب الموظف
              Object.entries(
                attendanceData.reduce((groups: any, item) => {
                  const employeeKey = `${item.employeeName}-${item.employeeNumber}`;
                  if (!groups[employeeKey]) {
                    groups[employeeKey] = {
                      employee: item,
                      records: []
                    };
                  }
                  groups[employeeKey].records.push(item);
                  return groups;
                }, {})
              ).map(([employeeKey, groupData]: [string, any]) => (
                <div key={employeeKey} className="mb-6">
                  {/* عنوان الموظف */}
                  <div className="bg-indigo-50 px-6 py-3 border-b border-indigo-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-indigo-100 rounded-full">
                          <FaUser className="text-indigo-600" size={20} />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-indigo-900">
                            {groupData.employee.employeeName}
                          </h3>
                          <div className="text-sm text-indigo-600">
                            رقم الموظف: {groupData.employee.employeeNumber} | القسم: {groupData.employee.department || 'غير محدد'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-indigo-600">
                          إجمالي الأيام: {groupData.records.length}
                        </span>
                        <span className="text-green-600">
                          حاضر: {groupData.records.filter((r: any) => r.status === 'PRESENT').length}
                        </span>
                        <span className="text-yellow-600">
                          جزئي: {groupData.records.filter((r: any) => r.status === 'PARTIAL').length}
                        </span>
                        <span className="text-red-600">
                          غائب: {groupData.records.filter((r: any) => r.status === 'ABSENT').length}
                        </span>
                        <span className="text-blue-600">
                          إجازة: {groupData.records.filter((r: any) => r.status === 'ON_LEAVE').length}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* جدول أيام الحضور للموظف */}
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          التاريخ
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحالة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          إجمالي الخروج
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحركات
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {groupData.records
                        .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
                        .map((row: AttendanceData, rowIndex: number) => (
                        <tr key={rowIndex} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {(() => {
                              const date = new Date(row.date);
                              const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                              const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                              const weekday = weekdays[date.getDay()];
                              const day = date.getDate();
                              const month = months[date.getMonth()];
                              const year = date.getFullYear();
                              return (
                                <span className="font-medium text-gray-900">
                                  {weekday} {day} {month} {year}م
                                </span>
                              );
                            })()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {(() => {
                              const statusConfig = {
                                PRESENT: { label: 'حاضر', color: 'bg-green-100 text-green-800' },
                                ABSENT: { label: 'غائب', color: 'bg-red-100 text-red-800' },
                                PARTIAL: { label: 'جزئي', color: 'bg-yellow-100 text-yellow-800' },
                                ON_LEAVE: { label: 'إجازة', color: 'bg-blue-100 text-blue-800' },
                              };
                              const config = statusConfig[row.status as keyof typeof statusConfig];
                              return (
                                <div>
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
                                    {config.label}
                                  </span>
                                  {row.leaveReason && row.status === 'ON_LEAVE' && (
                                    <div className="text-xs text-gray-600 mt-1">
                                      {row.leaveReason}
                                    </div>
                                  )}
                                </div>
                              );
                            })()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {(() => {
                              const totalBreakTime = calculateTotalBreakTime(
                                row.movements, 
                                settings?.workEndTime || '14:30',
                                settings?.workingHours || 7
                              );
                              return (
                                <span className="font-medium text-orange-600">
                                  {formatDuration(totalBreakTime)}
                                </span>
                              );
                            })()}
                          </td>
                          <td className="px-6 py-4 text-sm">
                            {columns[5].render ? columns[5].render(row.movements, row) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {columns[6].render ? columns[6].render(null, row) : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ))
            ) : (
              // إذا لم يكن هناك فلتر للموظف، اجمع حسب التاريخ (السلوك الافتراضي)
              Object.entries(
              attendanceData.reduce((groups: any, item) => {
                const date = item.date;
                if (!groups[date]) {
                  groups[date] = [];
                }
                groups[date].push(item);
                return groups;
              }, {})
            )
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime()) // ترتيب من الأحدث للأقدم
            .map(([date, records]: [string, any]) => (
              <div key={date} className="mb-6">
                {/* عنوان التاريخ */}
                <div className="bg-indigo-50 px-6 py-3 border-b border-indigo-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-indigo-900">
                      {(() => {
                        const dateObj = new Date(date);
                        const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                        const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                        return `${weekdays[dateObj.getDay()]} ${dateObj.getDate()} ${months[dateObj.getMonth()]} ${dateObj.getFullYear()}م`;
                      })()}
                    </h3>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-indigo-600">
                        إجمالي: {records.length} موظف
                      </span>
                      <span className="text-green-600">
                        حاضر: {records.filter((r: any) => r.status === 'PRESENT').length}
                      </span>
                      <span className="text-yellow-600">
                        جزئي: {records.filter((r: any) => r.status === 'PARTIAL').length}
                      </span>
                      <span className="text-red-600">
                        غائب: {records.filter((r: any) => r.status === 'ABSENT').length}
                      </span>
                      <span className="text-blue-600">
                        إجازة: {records.filter((r: any) => r.status === 'ON_LEAVE').length}
                      </span>
                    </div>
                  </div>
                </div>

                {/* جدول الموظفين لهذا التاريخ */}
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {columns.slice(1).map((column, index) => ( // تخطي عمود التاريخ
                        <th
                          key={index}
                          className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {column.title}
                        </th>
                      ))}
                </tr>
              </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {records.map((row: AttendanceData, rowIndex: number) => (
                      <tr key={rowIndex} className="hover:bg-gray-50">
                        {columns.slice(1).map((column, colIndex) => ( // تخطي عمود التاريخ
                          <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm">
                            {column.render ? column.render(row[column.key as keyof AttendanceData], row) : '-'}
                    </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              ))
            )}

            {attendanceData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد بيانات حضور للفترة المحددة
              </div>
            )}
                                  </div>
        )}
      </div>

      {/* نموذج الإدخال اليدوي */}
      {showManualEntry && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                {editingRecord && editingRecord.id ? 'تعديل سجل الحضور' :
                 editingRecord && editingRecord.movements ? 'اختيار الحركة للتعديل' :
                 'إدخال حضور/انصراف يدوي'}
              </h3>
                      <button
                onClick={() => {
                  setShowManualEntry(false);
                  setEditingRecord(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FaTimes size={20} />
                      </button>
            </div>

            {/* إذا كان هناك عدة حركات، اعرض قائمة للاختيار */}
            {editingRecord && editingRecord.movements && !editingRecord.id && (
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900">اختر الحركة المراد تعديلها:</h4>
                <div className="grid gap-3">
                  {editingRecord.movements
                    .sort((a: any, b: any) => a.entryNumber - b.entryNumber)
                    .map((movement: any, index: number) => (
                    <div
                      key={movement.id}
                      onClick={() => handleSelectMovement(movement)}
                      className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="text-lg font-semibold text-indigo-600">
                            #{movement.entryNumber}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              دخول: {movement.checkInTime ? new Date(movement.checkInTime).toLocaleTimeString('ar-SA', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                              }) : 'غير محدد'}
                            </div>
                            <div className="text-sm text-gray-600">
                              خروج: {movement.checkOutTime ? new Date(movement.checkOutTime).toLocaleTimeString('ar-SA', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                              }) : 'لم يخرج بعد'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                            movement.exitType === 'OFFICIAL' ? 'bg-green-100 text-green-800' :
                            movement.exitType === 'WORK' ? 'bg-blue-100 text-blue-800' :
                            movement.exitType === 'HEALTH' ? 'bg-purple-100 text-purple-800' :
                            'bg-orange-100 text-orange-800'
                          }`}>
                            {movement.exitType === 'OFFICIAL' ? 'رسمي' :
                             movement.exitType === 'WORK' ? 'عمل' :
                             movement.exitType === 'HEALTH' ? 'صحي' : 'شخصي'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
          </div>
        </div>
            )}

            {/* نموذج التعديل أو الإدخال الجديد */}
            {(!editingRecord || editingRecord.id) && (
              <div className="space-y-4">
                {/* اختيار الموظف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموظف *</label>
                <select
                  value={manualEntryData.employeeId}
                    onChange={(e) => setManualEntryData({...manualEntryData, employeeId: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    required
                    disabled={editingRecord}
                  >
                    <option value="">اختر الموظف</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.name} - {employee.employeeNumber}
                    </option>
                  ))}
                </select>
              </div>

                {/* التاريخ */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ *</label>
                <input
                  type="date"
                  value={manualEntryData.date}
                    onChange={(e) => setManualEntryData({...manualEntryData, date: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    required
                    disabled={editingRecord}
                />
              </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* وقت الدخول */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">وقت الدخول</label>
                <input
                  type="time"
                  value={manualEntryData.checkInTime}
                      onChange={(e) => setManualEntryData({...manualEntryData, checkInTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>

                  {/* وقت الخروج */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">وقت الخروج</label>
                <input
                  type="time"
                  value={manualEntryData.checkOutTime}
                      onChange={(e) => setManualEntryData({...manualEntryData, checkOutTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* نوع الاستئذان */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع الاستئذان</label>
                <select
                  value={manualEntryData.exitType}
                      onChange={(e) => setManualEntryData({...manualEntryData, exitType: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="OFFICIAL">رسمي</option>
                  <option value="PERSONAL">شخصي</option>
                  <option value="WORK">عمل</option>
                  <option value="HEALTH">صحي</option>
                </select>
              </div>

                  {/* رقم الحركة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الحركة</label>
                <input
                  type="number"
                      min="1"
                      max="4"
                  value={manualEntryData.entryNumber}
                      onChange={(e) => setManualEntryData({...manualEntryData, entryNumber: parseInt(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      disabled={editingRecord}
                />
              </div>
                </div>
              </div>
            )}

            {/* أزرار الإجراءات */}
            {(!editingRecord || editingRecord.id) && (
              <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowManualEntry(false);
                    setEditingRecord(null);
                  }}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  disabled={submitting}
                >
                  إلغاء
                </button>
                <button
                  onClick={handleManualEntry}
                  disabled={submitting || !manualEntryData.employeeId || !manualEntryData.date}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {submitting ? 'جاري الحفظ...' : (editingRecord && editingRecord.id ? 'تحديث' : 'حفظ')}
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
