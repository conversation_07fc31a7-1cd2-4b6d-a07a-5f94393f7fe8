"use client";

import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { ReactNode, useState, useEffect } from "react";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";

export default function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { data: session, status } = useSession();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // التحقق من دور المستخدم لإخفاء الشريط الجانبي عن موظف الأمن
  const isSecurityUser = session?.user?.role === "SECURITY";

  // تحديد الحالة الافتراضية للقائمة الجانبية حسب حجم الشاشة
  useEffect(() => {
    // إخفاء الشريط الجانبي تماماً لموظف الأمن
    if (isSecurityUser) {
      setSidebarOpen(false);
      return;
    }

    const checkScreenSize = () => {
      // للشاشات الكبيرة (1024px وأكثر) تكون القائمة مفتوحة افتراضياً
      if (window.innerWidth >= 1024) {
        setSidebarOpen(true);
      } else {
        setSidebarOpen(false);
      }
    };

    // فحص الحجم عند التحميل الأولي
    checkScreenSize();

    // إضافة مستمع لتغيير حجم الشاشة
    window.addEventListener('resize', checkScreenSize);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [isSecurityUser]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    redirect("/login");
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* إخفاء الشريط الجانبي تماماً لموظف الأمن */}
      {!isSecurityUser && (
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      )}
      <div className={`transition-all duration-300 ${
        isSecurityUser ? "" : (sidebarOpen ? "mr-64" : "mr-20")
      }`}>
        <Header 
          sidebarOpen={sidebarOpen} 
          setSidebarOpen={setSidebarOpen} 
          user={session?.user}
          hideSidebarToggle={isSecurityUser} // إخفاء زر تبديل الشريط الجانبي لموظف الأمن
        />
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
