"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FaPlus, FaEdit, FaTrash, FaCalendarAlt, FaCheck, FaTimes, FaClock } from "react-icons/fa";
import Link from "next/link";
import { DataTable, Column } from "@/components/ui/DataTable";
import { formatDate } from "@/lib/utils/dateUtils";

interface LeaveRequest {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
    employeeNumber: string | null;
  };
  startDate: string;
  endDate: string;
  reason: string;
  status: "PENDING" | "APPROVED" | "REJECTED" | "CANCELLED";
  createdAt: string;
  updatedAt: string;
}

const statusLabels = {
  PENDING: "في الانتظار",
  APPROVED: "موافق عليها",
  REJECTED: "مرفوضة",
  CANCELLED: "ملغاة",
};

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
};

export default function LeavesPage() {
  const { data: session } = useSession();
  const [leaves, setLeaves] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // تعريف أعمدة الجدول
  const columns: Column<LeaveRequest>[] = [
    {
      key: 'user.name',
      title: 'الموظف',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.user.employeeNumber && (
            <div className="text-sm text-gray-500">#{row.user.employeeNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'startDate',
      title: 'تاريخ البداية',
      sortable: true,
      render: (value) => <span className="text-blue-600">{formatDate(value)}</span>,
    },
    {
      key: 'endDate',
      title: 'تاريخ النهاية',
      sortable: true,
      render: (value) => <span className="text-blue-600">{formatDate(value)}</span>,
    },
    {
      key: 'reason',
      title: 'السبب',
      searchable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      key: 'status',
      title: 'الحالة',
      sortable: true,
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-sm ${statusColors[value as keyof typeof statusColors]}`}>
          {statusLabels[value as keyof typeof statusLabels]}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: 'تاريخ الطلب',
      sortable: true,
      render: (value) => <span className="text-gray-600">{formatDate(value)}</span>,
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_, row) => (
        <div className="flex justify-center gap-2">
          {row.status === 'PENDING' && canApproveLeaves && (
            <>
              <button
                onClick={() => handleStatusUpdate(row.id, 'APPROVED')}
                disabled={actionLoading === row.id}
                className="text-green-600 hover:text-green-800 p-2 disabled:opacity-50"
                title="موافقة"
              >
                <FaCheck size={16} />
              </button>
              <button
                onClick={() => handleStatusUpdate(row.id, 'REJECTED')}
                disabled={actionLoading === row.id}
                className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
                title="رفض"
              >
                <FaTimes size={16} />
              </button>
            </>
          )}
          {(canManageLeaves || row.userId === session?.user?.id) && (
            <Link
              href={`/dashboard/leaves/${row.id}/edit`}
              className="text-blue-600 hover:text-blue-800 p-2"
              title="تعديل"
            >
              <FaEdit size={16} />
            </Link>
          )}
          {(canManageLeaves || row.userId === session?.user?.id) && row.status === 'PENDING' && (
            <button
              onClick={() => handleDelete(row.id)}
              disabled={actionLoading === row.id}
              className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
              title="حذف"
            >
              {actionLoading === row.id ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
              ) : (
                <FaTrash size={16} />
              )}
            </button>
          )}
        </div>
      ),
    },
  ];

  // جلب طلبات الإجازة
  const fetchLeaves = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/leaves");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب طلبات الإجازة");
      }

      const data = await response.json();
      setLeaves(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب طلبات الإجازة:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // تحديث حالة الطلب
  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/leaves/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تحديث حالة الطلب");
      }

      await fetchLeaves();
      alert(`تم ${status === 'APPROVED' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`);
    } catch (error) {
      console.error("خطأ في تحديث حالة الطلب:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في تحديث حالة الطلب");
    } finally {
      setActionLoading(null);
    }
  };

  // حذف طلب الإجازة
  const handleDelete = async (id: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا الطلب؟")) {
      return;
    }

    try {
      setActionLoading(id);
      const response = await fetch(`/api/leaves/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف الطلب");
      }

      await fetchLeaves();
      alert("تم حذف الطلب بنجاح");
    } catch (error) {
      console.error("خطأ في حذف الطلب:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف الطلب");
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchLeaves();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManageLeaves = userRole === "ADMIN" || userRole === "HR" || userRole === "MANAGER";
  const canApproveLeaves = userRole === "ADMIN" || userRole === "HR"; // فقط الأدمن والموارد البشرية

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة الإجازات</h1>
        <Link
          href="/dashboard/leaves/new"
          className="btn-primary flex items-center gap-2"
        >
          <FaPlus size={16} />
          طلب إجازة جديد
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {leaves.length === 0 && !loading ? (
        <div className="card">
          <div className="text-center py-8">
            <FaCalendarAlt size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد طلبات إجازة</p>
            <Link
              href="/dashboard/leaves/new"
              className="btn-primary"
            >
              إضافة أول طلب إجازة
            </Link>
          </div>
        </div>
      ) : (
        <DataTable
          data={leaves}
          columns={columns}
          loading={loading}
          searchPlaceholder="البحث في طلبات الإجازة..."
          pageSize={10}
        />
      )}
    </div>
  );
}
