"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";
import { FaArrowRight, FaCalendarAlt } from "react-icons/fa";

const leaveSchema = z.object({
  startDate: z.string().min(1, "تاريخ البداية مطلوب"),
  endDate: z.string().min(1, "تاريخ النهاية مطلوب"),
  reason: z.string().min(1, "سبب الإجازة مطلوب").max(500, "السبب طويل جداً"),
});

type LeaveFormData = z.infer<typeof leaveSchema>;

export default function NewLeavePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LeaveFormData>({
    resolver: zodResolver(leaveSchema),
    defaultValues: {
      startDate: "",
      endDate: "",
      reason: "",
    },
  });

  const startDate = watch("startDate");
  const endDate = watch("endDate");

  // حساب عدد الأيام
  const calculateDays = () => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays;
    }
    return 0;
  };

  const onSubmit = async (data: LeaveFormData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/leaves", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في إنشاء طلب الإجازة");
      }

      router.push("/dashboard/leaves");
      router.refresh();
    } catch (error) {
      console.error("خطأ في إنشاء طلب الإجازة:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // تحديد التاريخ الأدنى (اليوم)
  const today = new Date().toISOString().split('T')[0];

  return (
    <div>
      <div className="flex items-center gap-4 mb-6">
        <Link
          href="/dashboard/leaves"
          className="text-gray-600 hover:text-gray-800"
        >
          <FaArrowRight size={20} />
        </Link>
        <h1 className="text-2xl font-bold">طلب إجازة جديد</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="card max-w-2xl">
        <div className="flex items-center gap-3 mb-6">
          <FaCalendarAlt className="text-blue-500" size={24} />
          <h2 className="text-xl font-semibold">تفاصيل الإجازة</h2>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                تاريخ البداية *
              </label>
              <input
                type="date"
                className="input"
                min={today}
                {...register("startDate")}
                disabled={loading}
              />
              {errors.startDate && (
                <p className="text-red-500 text-sm mt-1">{errors.startDate.message}</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                className="input"
                min={startDate || today}
                {...register("endDate")}
                disabled={loading}
              />
              {errors.endDate && (
                <p className="text-red-500 text-sm mt-1">{errors.endDate.message}</p>
              )}
            </div>
          </div>

          {startDate && endDate && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-blue-700 dark:text-blue-300 font-medium">
                عدد أيام الإجازة: {calculateDays()} يوم
              </p>
            </div>
          )}

          <div className="mt-6">
            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
              سبب الإجازة *
            </label>
            <textarea
              className="input min-h-[120px] resize-vertical"
              placeholder="اكتب سبب طلب الإجازة..."
              {...register("reason")}
              disabled={loading}
            />
            {errors.reason && (
              <p className="text-red-500 text-sm mt-1">{errors.reason.message}</p>
            )}
          </div>

          <div className="flex gap-4 mt-8">
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex-1"
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  جاري الإرسال...
                </div>
              ) : (
                "إرسال الطلب"
              )}
            </button>
            <Link
              href="/dashboard/leaves"
              className="btn-secondary flex-1 text-center"
            >
              إلغاء
            </Link>
          </div>
        </form>
      </div>

      <div className="card mt-6 max-w-2xl">
        <h3 className="text-lg font-semibold mb-4">ملاحظات مهمة</h3>
        <ul className="space-y-2 text-gray-600 dark:text-gray-400">
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-1">•</span>
            <span>يجب تقديم طلب الإجازة قبل التاريخ المطلوب بوقت كافٍ</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-1">•</span>
            <span>سيتم مراجعة الطلب من قبل  الموارد البشرية</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-1">•</span>
            <span>يمكنك تعديل أو إلغاء الطلب قبل الموافقة عليه</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-1">•</span>
            <span>ستصلك إشعارات عند تغيير حالة الطلب</span>
          </li>
        </ul>
      </div>
    </div>
  );
}
