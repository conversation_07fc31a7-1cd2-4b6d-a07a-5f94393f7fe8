"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  Fa<PERSON><PERSON><PERSON>,
  FaClock,
  FaCalendarAlt,
  FaClipboardList,
  FaChartLine,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaUserCheck,
  FaUserTimes,
  FaBuilding,
  FaCalendarCheck,
  FaUserClock,
  FaUserSlash
} from "react-icons/fa";

interface ManagerDashboardStats {
  teamOverview: {
    totalEmployees: number;
    fullAttendanceToday: number;     // حضور (ساعات العمل >= الحد الأدنى)
    partialAttendanceToday: number;  // حضور جزئي (ساعات العمل < الحد الأدنى)
    onLeaveToday: number;            // في إجازة معتمدة
    absentToday: number;             // غياب بدون إذن
    lateToday: number;               // متأخرون
    isWeekend: boolean;
    isOfficialHoliday: boolean;
    holidayName: string | null;
    minWorkingHours?: number;        // الحد الأدنى لساعات العمل
    workingHours?: number;           // إجمالي ساعات العمل اليومية
  };
  pendingApprovals: {
    leaves: number;
    visitors: number;
    afterHours: number;
    total: number;
  };
  departmentStats: {
    attendanceRate: number;
    avgWorkingHours: number;
    overtimeHours: number;
  };
  recentActivity: {
    newRequests: number;
    approvedToday: number;
    rejectedToday: number;
  };
  upcomingEvents: {
    scheduledLeaves: number;
    expectedVisitors: number;
    afterHoursWork: number;
  };
  details?: {
    fullAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      status?: string;
      exitTime?: string;
      exitType?: string;
    }>;
    partialAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      exitTime: string;
      exitType: string;
    }>;
    employeesOnLeave: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      leaveReason: string;
    }>;
    absentEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
    }>;
    lateEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      minutesLate: number;
    }>;
  };
}

export default function ManagerDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<ManagerDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/dashboard/manager-stats");
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("خطأ في جلب إحصائيات المدير:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // Auto-refresh every 5 minutes for real-time statistics
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-white/20 rounded-full">
            <FaBuilding size={32} />
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">لوحة تحكم المدير</h1>
            <p className="text-purple-100 mt-1">
              إدارة الفريق والموافقات - {new Date().toLocaleDateString('en-GB')}
            </p>
          </div>
          {/* Weekend/Holiday Indicator */}
          {(stats?.teamOverview.isWeekend || stats?.teamOverview.isOfficialHoliday) && (
            <div className="bg-white/20 rounded-lg p-3 text-center">
              <div className="text-sm font-medium">
                {stats.teamOverview.isOfficialHoliday ? 'عطلة رسمية' : 'عطلة نهاية أسبوع'}
              </div>
              {stats.teamOverview.holidayName && (
                <div className="text-xs text-purple-200 mt-1">
                  {stats.teamOverview.holidayName}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Summary Overview */}
      {stats && !stats.teamOverview.isWeekend && !stats.teamOverview.isOfficialHoliday && (
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">ملخص الحضور اليوم</h2>
          <div className="flex items-center justify-between text-sm">
            <span>إجمالي الموظفين: {stats.teamOverview.totalEmployees}</span>
            <span className="text-gray-600">
              المحاسب عليهم: {stats.teamOverview.fullAttendanceToday + stats.teamOverview.partialAttendanceToday + stats.teamOverview.onLeaveToday + stats.teamOverview.absentToday}
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            النسب المئوية محسوبة من إجمالي الموظفين في الفريق (بناءً على ساعات العمل الفعلية)
          </div>
          {stats.teamOverview.minWorkingHours && (
            <div className="mt-2 bg-blue-50 text-blue-800 px-3 py-1 rounded-full text-sm inline-block">
              الحد الأدنى: {stats.teamOverview.minWorkingHours} ساعات | المطلوب: {stats.teamOverview.workingHours} ساعات
            </div>
          )}
          <div className="mt-3 w-full bg-gray-200 rounded-full h-3">
            <div className="flex h-3 rounded-full overflow-hidden">
              {/* Attendance */}
              <div 
                className="bg-green-500" 
                style={{ 
                  width: `${stats.teamOverview.totalEmployees > 0 ? (stats.teamOverview.fullAttendanceToday / stats.teamOverview.totalEmployees) * 100 : 0}%` 
                }}
                title={`حضور: ${stats.teamOverview.fullAttendanceToday}`}
              ></div>
              {/* Partial Attendance */}
              <div 
                className="bg-orange-500" 
                style={{ 
                  width: `${stats.teamOverview.totalEmployees > 0 ? (stats.teamOverview.partialAttendanceToday / stats.teamOverview.totalEmployees) * 100 : 0}%` 
                }}
                title={`حضور جزئي: ${stats.teamOverview.partialAttendanceToday}`}
              ></div>
              {/* On Leave */}
              <div 
                className="bg-blue-500" 
                style={{ 
                  width: `${stats.teamOverview.totalEmployees > 0 ? (stats.teamOverview.onLeaveToday / stats.teamOverview.totalEmployees) * 100 : 0}%` 
                }}
                title={`في إجازة: ${stats.teamOverview.onLeaveToday}`}
              ></div>
              {/* Absent */}
              <div 
                className="bg-red-500" 
                style={{ 
                  width: `${stats.teamOverview.totalEmployees > 0 ? (stats.teamOverview.absentToday / stats.teamOverview.totalEmployees) * 100 : 0}%` 
                }}
                title={`غائب: ${stats.teamOverview.absentToday}`}
              ></div>
            </div>
          </div>
          <div className="flex justify-between text-xs text-gray-600 mt-2">
            <span className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
                                حضور
            </span>
            <span className="flex items-center gap-1">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              حضور جزئي
            </span>
            <span className="flex items-center gap-1">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              في إجازة
            </span>
            <span className="flex items-center gap-1">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              غائب
            </span>
          </div>
        </div>
      )}

      {/* Team Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        {/* Total Employees */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">إجمالي الموظفين</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.totalEmployees || 0}
              </p>
              <p className="text-sm text-blue-600 mt-1">في الفريق</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <FaUsers className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        {/* Attendance Today */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">حضور</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.fullAttendanceToday || 0}
              </p>
              <p className="text-sm text-green-600 mt-1">
                {stats?.teamOverview.totalEmployees && !stats?.teamOverview.isWeekend && !stats?.teamOverview.isOfficialHoliday ? 
                  `${Math.round(((stats?.teamOverview.fullAttendanceToday || 0) / stats.teamOverview.totalEmployees) * 100)}% من الفريق`
                  : stats?.teamOverview.isWeekend ? 'عطلة نهاية أسبوع'
                  : stats?.teamOverview.isOfficialHoliday ? 'عطلة رسمية'
                  : 'من الفريق'
                }
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <FaUserCheck className="text-green-600" size={24} />
            </div>
          </div>
        </div>

        {/* Partial Attendance Today */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">حضور جزئي</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.partialAttendanceToday || 0}
              </p>
              <p className="text-sm text-orange-600 mt-1">
                {stats?.teamOverview.totalEmployees && !stats?.teamOverview.isWeekend && !stats?.teamOverview.isOfficialHoliday ? 
                  `${Math.round(((stats?.teamOverview.partialAttendanceToday || 0) / stats.teamOverview.totalEmployees) * 100)}% من الفريق`
                  : stats?.teamOverview.isWeekend ? 'عطلة نهاية أسبوع'
                  : stats?.teamOverview.isOfficialHoliday ? 'عطلة رسمية'
                  : 'خروج مبكر'
                }
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <FaUserClock className="text-orange-600" size={24} />
            </div>
          </div>
        </div>

        {/* On Leave Today */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">في إجازة</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.onLeaveToday || 0}
              </p>
              <p className="text-sm text-blue-600 mt-1">
                {stats?.teamOverview.totalEmployees && !stats?.teamOverview.isWeekend && !stats?.teamOverview.isOfficialHoliday ? 
                  `${Math.round(((stats?.teamOverview.onLeaveToday || 0) / stats.teamOverview.totalEmployees) * 100)}% من الفريق`
                  : stats?.teamOverview.isWeekend ? 'عطلة نهاية أسبوع'
                  : stats?.teamOverview.isOfficialHoliday ? 'عطلة رسمية'
                  : 'إجازة معتمدة'
                }
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <FaCalendarAlt className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        {/* Absent Today */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">غياب</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.absentToday || 0}
              </p>
              <p className="text-sm text-red-600 mt-1">
                {stats?.teamOverview.totalEmployees && !stats?.teamOverview.isWeekend && !stats?.teamOverview.isOfficialHoliday ? 
                  `${Math.round(((stats?.teamOverview.absentToday || 0) / stats.teamOverview.totalEmployees) * 100)}% من الفريق`
                  : stats?.teamOverview.isWeekend ? 'عطلة نهاية أسبوع'
                  : stats?.teamOverview.isOfficialHoliday ? 'عطلة رسمية'
                  : 'بدون إذن'
                }
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <FaUserSlash className="text-red-600" size={24} />
            </div>
          </div>
        </div>

        {/* Late Today */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">متأخرون</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.teamOverview.lateToday || 0}
              </p>
              <p className="text-sm text-yellow-600 mt-1">
                {stats?.teamOverview.totalEmployees && !stats?.teamOverview.isWeekend && !stats?.teamOverview.isOfficialHoliday ? 
                  `${Math.round(((stats?.teamOverview.lateToday || 0) / stats.teamOverview.totalEmployees) * 100)}% من الفريق`
                  : stats?.teamOverview.isWeekend ? 'عطلة نهاية أسبوع'
                  : stats?.teamOverview.isOfficialHoliday ? 'عطلة رسمية'
                  : 'دخول متأخر'
                }
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <FaClock className="text-yellow-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Details */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaUserCheck className="text-green-600" />
            الحضور ({stats?.teamOverview.fullAttendanceToday || 0})
          </h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {stats?.details?.fullAttendanceEmployees.map((emp) => (
              <div key={emp.id} className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                <div>
                  <p className="font-medium text-green-700">{emp.name}</p>
                  <p className="text-sm text-green-600">
                    {emp.employeeNumber} - دخول: {new Date(emp.checkInTime).toLocaleTimeString('en-GB')}
                  </p>
                </div>
                <div className="text-xs text-green-600">
                  {emp.status || (emp.exitTime ? `خرج: ${new Date(emp.exitTime).toLocaleTimeString('en-GB')}` : 'في المكتب')}
                </div>
              </div>
            ))}
            {(!stats?.details?.fullAttendanceEmployees || stats.details.fullAttendanceEmployees.length === 0) && (
              <p className="text-gray-500 text-center py-4">لا يوجد موظفين في حضور</p>
            )}
          </div>
        </div>

        {/* Partial Attendance Details */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaUserClock className="text-orange-600" />
            الحضور الجزئي ({stats?.teamOverview.partialAttendanceToday || 0})
          </h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {stats?.details?.partialAttendanceEmployees.map((emp) => (
              <div key={emp.id} className="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
                <div>
                  <p className="font-medium text-orange-700">{emp.name}</p>
                  <p className="text-sm text-orange-600">
                    {emp.employeeNumber} - دخول: {new Date(emp.checkInTime).toLocaleTimeString('en-GB')}
                  </p>
                </div>
                <div className="text-xs text-orange-600">
                  خرج: {new Date(emp.exitTime).toLocaleTimeString('en-GB')}
                </div>
              </div>
            ))}
            {(!stats?.details?.partialAttendanceEmployees || stats.details.partialAttendanceEmployees.length === 0) && (
              <p className="text-gray-500 text-center py-4">لا يوجد موظفين في حضور جزئي</p>
            )}
          </div>
        </div>
      </div>

      {/* Leave and Absent Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* On Leave Details */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaCalendarAlt className="text-blue-600" />
            في إجازة ({stats?.teamOverview.onLeaveToday || 0})
          </h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {stats?.details?.employeesOnLeave.map((emp) => (
              <div key={emp.id} className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                <div>
                  <p className="font-medium text-blue-700">{emp.name}</p>
                  <p className="text-sm text-blue-600">{emp.employeeNumber}</p>
                </div>
                <div className="text-xs text-blue-600">
                  {emp.leaveReason}
                </div>
              </div>
            ))}
            {(!stats?.details?.employeesOnLeave || stats.details.employeesOnLeave.length === 0) && (
              <p className="text-gray-500 text-center py-4">لا يوجد موظفين في إجازة</p>
            )}
          </div>
        </div>

        {/* Absent Details */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaUserSlash className="text-red-600" />
            غائبون ({stats?.teamOverview.absentToday || 0})
          </h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {stats?.details?.absentEmployees.map((emp) => (
              <div key={emp.id} className="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                <div>
                  <p className="font-medium text-red-700">{emp.name}</p>
                  <p className="text-sm text-red-600">{emp.employeeNumber}</p>
                </div>
                <div className="text-xs text-red-600">
                  غياب بدون إذن
                </div>
              </div>
            ))}
            {(!stats?.details?.absentEmployees || stats.details.absentEmployees.length === 0) && (
              <p className="text-gray-500 text-center py-4">لا يوجد موظفين غائبين</p>
            )}
          </div>
        </div>
      </div>

      {/* Pending Approvals Summary */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">طلبات الموافقة المعلقة</h2>
          <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
            {stats?.pendingApprovals.total || 0} طلب معلق
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
            <FaCalendarAlt className="text-green-600" size={20} />
            <div>
              <p className="font-medium text-green-700">طلبات الإجازات</p>
              <p className="text-sm text-green-600">{stats?.pendingApprovals.leaves || 0} طلب معلق</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
            <FaUsers className="text-blue-600" size={20} />
            <div>
              <p className="font-medium text-blue-700">طلبات الزيارات</p>
              <p className="text-sm text-blue-600">{stats?.pendingApprovals.visitors || 0} طلب معلق</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-orange-50 rounded-lg">
            <FaClock className="text-orange-600" size={20} />
            <div>
              <p className="font-medium text-orange-700">العمل بعد الدوام</p>
              <p className="text-sm text-orange-600">{stats?.pendingApprovals.afterHours || 0} طلب معلق</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/dashboard/attendance"
            className="flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
          >
            <FaCalendarCheck className="text-purple-600" size={20} />
            <div>
              <span className="font-medium text-purple-700 block">سجل الحضور</span>
              <span className="text-sm text-purple-600">عرض كشف الحضور</span>
            </div>
          </Link>
          
          <Link
            href="/dashboard/leaves"
            className="flex items-center gap-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
          >
            <FaCalendarAlt className="text-green-600" size={20} />
            <div>
              <span className="font-medium text-green-700 block">طلبات الإجازات</span>
              <span className="text-sm text-green-600">{stats?.pendingApprovals.leaves || 0} معلق</span>
            </div>
          </Link>
          
          <Link
            href="/dashboard/visitors"
            className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
          >
            <FaUsers className="text-blue-600" size={20} />
            <div>
              <span className="font-medium text-blue-700 block">طلبات الزيارات</span>
              <span className="text-sm text-blue-600">{stats?.pendingApprovals.visitors || 0} معلق</span>
            </div>
          </Link>
          
          <Link
            href="/dashboard/after-hours"
            className="flex items-center gap-3 p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors"
          >
            <FaClock className="text-orange-600" size={20} />
            <div>
              <span className="font-medium text-orange-700 block">العمل بعد الدوام</span>
              <span className="text-sm text-orange-600">{stats?.pendingApprovals.afterHours || 0} معلق</span>
            </div>
          </Link>
        </div>
      </div>

      {/* Department Stats & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Performance */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">أداء القسم</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaChartLine className="text-blue-600" size={16} />
                <span className="font-medium text-blue-700">نسبة الحضور</span>
              </div>
              <span className="text-xl font-bold text-blue-600">
                {stats?.departmentStats.attendanceRate || 0}%
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaClock className="text-green-600" size={16} />
                <span className="font-medium text-green-700">متوسط ساعات العمل</span>
              </div>
              <span className="text-xl font-bold text-green-600">
                {stats?.departmentStats.avgWorkingHours || 0}h
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FaExclamationTriangle className="text-orange-600" size={16} />
                <span className="font-medium text-orange-700">ساعات إضافية</span>
              </div>
              <span className="text-xl font-bold text-orange-600">
                {stats?.departmentStats.overtimeHours || 0}h
              </span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">النشاط الأخير</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
              <FaClipboardList className="text-yellow-600" size={16} />
              <div>
                <p className="font-medium text-yellow-700">طلبات جديدة</p>
                <p className="text-sm text-yellow-600">
                  {stats?.recentActivity.newRequests || 0} طلب اليوم
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <FaCheckCircle className="text-green-600" size={16} />
              <div>
                <p className="font-medium text-green-700">تمت الموافقة</p>
                <p className="text-sm text-green-600">
                  {stats?.recentActivity.approvedToday || 0} طلب اليوم
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
              <FaTimesCircle className="text-red-600" size={16} />
              <div>
                <p className="font-medium text-red-700">تم الرفض</p>
                <p className="text-sm text-red-600">
                  {stats?.recentActivity.rejectedToday || 0} طلب اليوم
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">الأحداث القادمة (الأسبوع القادم)</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg">
            <FaCalendarAlt className="text-purple-600" size={20} />
            <div>
              <p className="font-medium text-purple-700">إجازات مجدولة</p>
              <p className="text-sm text-purple-600">
                {stats?.upcomingEvents.scheduledLeaves || 0} موظف
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-indigo-50 rounded-lg">
            <FaUsers className="text-indigo-600" size={20} />
            <div>
              <p className="font-medium text-indigo-700">زيارات متوقعة</p>
              <p className="text-sm text-indigo-600">
                {stats?.upcomingEvents.expectedVisitors || 0} زائر
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-teal-50 rounded-lg">
            <FaClock className="text-teal-600" size={20} />
            <div>
              <p className="font-medium text-teal-700">عمل بعد الدوام</p>
              <p className="text-sm text-teal-600">
                {stats?.upcomingEvents.afterHoursWork || 0} تصريح
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
