"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { FaCalendarAlt, FaCalendarDay, FaArrowLeft, FaUserShield } from "react-icons/fa";

interface OfficialHoliday {
  id: string;
  name: string;
  date: string;
}

export default function EditOfficialHolidayPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    startDate: "",
    endDate: "",
  });

  // جلب بيانات الإجازة الرسمية
  const fetchHoliday = async (id: string) => {
    try {
      const response = await fetch(`/api/official-holidays/${id}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب بيانات الإجازة الرسمية");
      }
      const data = await response.json();
      setFormData({
        name: data.name || "",
        startDate: data.startDate ? new Date(data.startDate).toISOString().split('T')[0] : "",
        endDate: data.endDate ? new Date(data.endDate).toISOString().split('T')[0] : "",
      });
    } catch (error) {
      console.error("خطأ في جلب الإجازة الرسمية:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setPageLoading(false);
    }
  };

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
      fetchHoliday(resolvedParamsData.id);
    };
    loadParams();
  }, [params]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`/api/official-holidays/${resolvedParams?.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/dashboard/official-holidays");
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في تحديث الإجازة الرسمية");
      }
    } catch (error) {
      console.error("خطأ في تحديث الإجازة الرسمية:", error);
      alert("حدث خطأ في تحديث الإجازة الرسمية");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام والموارد البشرية فقط</p>
      </div>
    );
  }

  if (pageLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3 text-gray-600">جاري تحميل بيانات الإجازة الرسمية...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <button
              onClick={() => router.back()}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <FaArrowLeft size={20} />
            </button>
            <h1 className="text-3xl font-bold text-gray-900">تعديل الإجازة الرسمية</h1>
          </div>
          <p className="text-gray-600 mr-8">تعديل بيانات ومعلومات الإجازة الرسمية</p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <FaCalendarAlt className="text-blue-600" />
            معلومات الإجازة الرسمية
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* اسم الإجازة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaCalendarDay className="inline ml-2 text-blue-500" />
              اسم الإجازة الرسمية *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="أدخل اسم الإجازة الرسمية"
            />
            <p className="text-sm text-gray-500 mt-1">
              مثال: عيد الفطر المبارك، اليوم الوطني، رأس السنة الميلادية
            </p>
          </div>

          {/* تاريخ بداية الإجازة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaCalendarAlt className="inline ml-2 text-green-500" />
              تاريخ بداية الإجازة *
            </label>
            <input
              type="date"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-1">
              اختر تاريخ بداية الإجازة الرسمية
            </p>
          </div>

          {/* تاريخ نهاية الإجازة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaCalendarAlt className="inline ml-2 text-red-500" />
              تاريخ نهاية الإجازة (اختياري)
            </label>
            <input
              type="date"
              name="endDate"
              value={formData.endDate}
              onChange={handleChange}
              min={formData.startDate}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-1">
              اختر تاريخ نهاية الإجازة الرسمية (إذا تركت فارغاً، ستكون إجازة يوم واحد)
            </p>
          </div>

          {/* معاينة */}
          {formData.name && formData.startDate && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">معاينة الإجازة الرسمية:</h4>
              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-center gap-3">
                  <FaCalendarDay className="text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">{formData.name}</p>
                    <p className="text-sm text-gray-600">
                      {(() => {
                        const startDate = new Date(formData.startDate);
                        const endDate = formData.endDate ? new Date(formData.endDate) : startDate;
                        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                        if (diffDays === 1) {
                          return startDate.toLocaleDateString('ar-EG', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) + ' م';
                        } else {
                          return `${startDate.toLocaleDateString('ar-EG', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })} - ${endDate.toLocaleDateString('ar-EG', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })} م (${diffDays} أيام)`;
                        }
                      })()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* ملاحظة */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <FaCalendarAlt className="text-orange-600 mt-1" />
              <div>
                <h4 className="text-sm font-medium text-orange-800">ملاحظة مهمة</h4>
                <p className="text-sm text-orange-700 mt-1">
                  • تعديل تاريخ الإجازة الرسمية قد يؤثر على التقارير والحسابات المرتبطة بها<br/>
                  • تأكد من صحة التاريخ الجديد قبل الحفظ<br/>
                  • سيتم إشعار جميع الموظفين بأي تغييرات في الإجازات الرسمية
                </p>
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 pt-6 border-t border-gray-100">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري التحديث...
                </>
              ) : (
                <>
                  <FaCalendarAlt />
                  حفظ التغييرات
                </>
              )}
            </button>
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
