"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  FaCalendarAlt,
  FaPlus,
  FaEdit,
  FaTrash,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarCheck
} from "react-icons/fa";
import { DataTable } from "@/components/ui/DataTable";

interface OfficialHoliday {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
}

export default function OfficialHolidaysPage() {
  const { data: session } = useSession();
  const [holidays, setHolidays] = useState<OfficialHoliday[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // أعمدة الجدول
  const columns = [
    {
      key: 'name',
      title: 'اسم الإجازة',
      searchable: true,
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <FaCalendarDay className="text-blue-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'dateRange',
      title: 'فترة الإجازة',
      sortable: false,
      render: (_value: any, row: OfficialHoliday) => {
        const startDate = new Date(row.startDate);
        const endDate = new Date(row.endDate);
        const today = new Date();

        // تحديد حالة الإجازة
        const isActive = today >= startDate && today <= endDate;
        const isPast = endDate < today;
        const isFuture = startDate > today;

        // حساب عدد الأيام
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

        return (
          <div className="flex items-center gap-2">
            <FaCalendarAlt className={`${
              isActive ? 'text-green-500' :
              isPast ? 'text-gray-400' :
              'text-blue-500'
            }`} />
            <div>
              <div className={`font-medium ${
                isActive ? 'text-green-600' :
                isPast ? 'text-gray-500' :
                'text-gray-900'
              }`}>
                {startDate.toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
                {diffDays > 1 && (
                  <>
                    {' - '}
                    {endDate.toLocaleDateString('en-GB', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </>
                )}
              </div>
              <div className="text-xs text-gray-500">
                {diffDays === 1 ? 'يوم واحد' : `${diffDays} أيام`}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      key: 'status',
      title: 'الحالة',
      render: (_value: any, row: OfficialHoliday) => {
        const startDate = new Date(row.startDate);
        const endDate = new Date(row.endDate);
        const today = new Date();

        const isActive = today >= startDate && today <= endDate;
        const isPast = endDate < today;

        if (isActive) {
          return (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200">
              <FaCalendarCheck className="inline ml-1" size={12} />
              جارية
            </span>
          );
        } else if (isPast) {
          return (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600 border border-gray-200">
              انتهت
            </span>
          );
        } else {
          return (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
              قادمة
            </span>
          );
        }
      },
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_value: any, row: OfficialHoliday) => (
        <div className="flex justify-center gap-2">
          <Link
            href={`/dashboard/official-holidays/${row.id}/edit`}
            className="text-green-600 hover:text-green-800 p-2"
            title="تعديل"
          >
            <FaEdit size={16} />
          </Link>
          <button
            onClick={() => handleDelete(row.id)}
            disabled={actionLoading === row.id}
            className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
            title="حذف"
          >
            {actionLoading === row.id ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
            ) : (
              <FaTrash size={16} />
            )}
          </button>
        </div>
      ),
    },
  ];

  // جلب الإجازات الرسمية
  const fetchHolidays = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/official-holidays");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب الإجازات الرسمية");
      }

      const data = await response.json();
      setHolidays(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب الإجازات الرسمية:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // حذف إجازة رسمية
  const handleDelete = async (holidayId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذه الإجازة الرسمية؟ هذا الإجراء لا يمكن التراجع عنه.")) {
      return;
    }

    try {
      setActionLoading(holidayId);
      const response = await fetch(`/api/official-holidays/${holidayId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف الإجازة الرسمية");
      }

      await fetchHolidays();
      alert("تم حذف الإجازة الرسمية بنجاح");
    } catch (error) {
      console.error("خطأ في حذف الإجازة الرسمية:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف الإجازة الرسمية");
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchHolidays();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  // إحصائيات سريعة
  const today = new Date();
  const activeHolidays = holidays.filter(h => {
    const startDate = new Date(h.startDate);
    const endDate = new Date(h.endDate);
    return today >= startDate && today <= endDate;
  });
  const upcomingHolidays = holidays.filter(h => new Date(h.startDate) > today);
  const pastHolidays = holidays.filter(h => new Date(h.endDate) < today);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FaCalendarAlt className="text-blue-600" />
            الإجازات الرسمية
          </h1>
          <p className="text-gray-600 mt-1">إدارة العطل والإجازات الرسمية ة</p>
        </div>
        {canManage && (
          <Link
            href="/dashboard/official-holidays/new"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <FaPlus size={16} />
            إضافة إجازة رسمية
          </Link>
        )}
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الإجازات</p>
              <p className="text-2xl font-bold text-gray-900">{holidays.length}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100">
              <FaCalendarAlt className="text-blue-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجازات جارية</p>
              <p className="text-2xl font-bold text-gray-900">{activeHolidays.length}</p>
            </div>
            <div className="p-3 rounded-full bg-green-100">
              <FaCalendarCheck className="text-green-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجازات قادمة</p>
              <p className="text-2xl font-bold text-gray-900">{upcomingHolidays.length}</p>
            </div>
            <div className="p-3 rounded-full bg-purple-100">
              <FaCalendarWeek className="text-purple-600" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-gray-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجازات منتهية</p>
              <p className="text-2xl font-bold text-gray-900">{pastHolidays.length}</p>
            </div>
            <div className="p-3 rounded-full bg-gray-100">
              <FaCalendarDay className="text-gray-600" size={20} />
            </div>
          </div>
        </div>
      </div>

      {/* رسائل الخطأ */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* الإجازات الجارية */}
      {activeHolidays.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            <FaCalendarCheck className="text-green-600" size={24} />
            <h2 className="text-xl font-semibold text-green-800">إجازات رسمية جارية</h2>
          </div>
          <div className="space-y-2">
            {activeHolidays.map((holiday) => {
              const startDate = new Date(holiday.startDate);
              const endDate = new Date(holiday.endDate);
              const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

              return (
                <div key={holiday.id} className="bg-white rounded-lg p-4 border border-green-200">
                  <h3 className="font-medium text-green-800">{holiday.name}</h3>
                  <p className="text-sm text-green-600">
                    {startDate.toLocaleDateString('en-GB', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                    {diffDays > 1 && (
                      <>
                        {' - '}
                        {endDate.toLocaleDateString('en-GB', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </>
                    )}
                    <span className="mr-2 text-xs">
                      ({diffDays === 1 ? 'يوم واحد' : `${diffDays} أيام`})
                    </span>
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* جدول الإجازات الرسمية */}
      {holidays.length === 0 && !loading ? (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
          <div className="text-center">
            <FaCalendarAlt size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا توجد إجازات رسمية مسجلة</p>
            {canManage && (
              <Link
                href="/dashboard/official-holidays/new"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة أول إجازة رسمية
              </Link>
            )}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <DataTable
            data={holidays}
            columns={columns}
            loading={loading}
            searchPlaceholder="البحث في الإجازات الرسمية..."
            pageSize={10}
          />
        </div>
      )}

      {/* معلومات إضافية */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex items-start gap-3">
          <FaCalendarAlt className="text-blue-600 mt-1" />
          <div>
            <h4 className="text-lg font-semibold text-blue-800 mb-2">معلومات حول الإجازات الرسمية</h4>
            <div className="text-sm text-blue-700 space-y-2">
              <p>• <strong>الإجازات الرسمية</strong> هي العطل المعتمدة والتي لا يُحتسب فيها غياب</p>
              <p>• <strong>يمكن للأدمن والموارد البشرية</strong> إضافة وتعديل وحذف الإجازات الرسمية</p>
              <p>• <strong>جميع الموظفين</strong> يمكنهم عرض الإجازات الرسمية للتخطيط المسبق</p>
              <p>• <strong>الإجازات الرسمية</strong> تؤثر على حساب أيام العمل في التقارير</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
