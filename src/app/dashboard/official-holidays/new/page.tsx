"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { FaCalendarAlt, FaCalendarDay, FaArrowLeft, FaUserShield } from "react-icons/fa";

export default function NewOfficialHolidayPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    startDate: "",
    endDate: ""
  });

  // أمثلة للإجازات الرسمية الشائعة
  const commonHolidays = [
    "رأس السنة الميلادية",
    "عيد الفطر المبارك",
    "عيد الأضحى المبارك",
    "اليوم الوطني",
    "يوم التأسيس",
    "عيد العمال",
    "رأس السنة الهجرية",
    "المولد النبوي الشريف",
    "يوم عرفة",
    "عطلة نهاية الأسبوع الممتدة"
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/official-holidays", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/dashboard/official-holidays");
      } else {
        const error = await response.json();
        alert(error.error || "حدث خطأ في إنشاء الإجازة الرسمية");
      }
    } catch (error) {
      console.error("خطأ في إنشاء الإجازة الرسمية:", error);
      alert("حدث خطأ في إنشاء الإجازة الرسمية");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleQuickSelect = (holidayName: string) => {
    setFormData({
      ...formData,
      name: holidayName
    });
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <FaUserShield size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-red-500 text-lg">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        <p className="text-gray-500 mt-2">هذه الصفحة مخصصة لمديري النظام والموارد البشرية فقط</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <button
              onClick={() => router.back()}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <FaArrowLeft size={20} />
            </button>
            <h1 className="text-3xl font-bold text-gray-900">إضافة إجازة رسمية جديدة</h1>
          </div>
          <p className="text-gray-600 mr-8">إضافة عطلة أو إجازة رسمية جديدة </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* النموذج */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <FaCalendarAlt className="text-blue-600" />
                معلومات الإجازة الرسمية
              </h2>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* اسم الإجازة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaCalendarDay className="inline ml-2 text-blue-500" />
                  اسم الإجازة الرسمية *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل اسم الإجازة الرسمية"
                />
                <p className="text-sm text-gray-500 mt-1">
                  مثال: عيد الفطر المبارك، اليوم الوطني، رأس السنة الميلادية
                </p>
              </div>

              {/* تاريخ بداية الإجازة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaCalendarAlt className="inline ml-2 text-green-500" />
                  تاريخ بداية الإجازة *
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-sm text-gray-500 mt-1">
                  اختر تاريخ بداية الإجازة الرسمية
                </p>
              </div>

              {/* تاريخ نهاية الإجازة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaCalendarAlt className="inline ml-2 text-red-500" />
                  تاريخ نهاية الإجازة (اختياري)
                </label>
                <input
                  type="date"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  min={formData.startDate}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-sm text-gray-500 mt-1">
                  اختر تاريخ نهاية الإجازة الرسمية (إذا تركت فارغاً، ستكون إجازة يوم واحد)
                </p>
              </div>

              {/* معاينة */}
              {formData.name && formData.startDate && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">معاينة الإجازة الرسمية:</h4>
                  <div className="bg-white rounded-lg p-3 border border-blue-200">
                    <div className="flex items-center gap-3">
                      <FaCalendarDay className="text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">{formData.name}</p>
                        <p className="text-sm text-gray-600">
                          {(() => {
                            const startDate = new Date(formData.startDate);
                            const endDate = formData.endDate ? new Date(formData.endDate) : startDate;
                            const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
                            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                            if (diffDays === 1) {
                                                      return startDate.toLocaleDateString('ar-EG', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        }) + ' م';
                            } else {
                                                      return `${startDate.toLocaleDateString('ar-EG', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })} - ${endDate.toLocaleDateString('ar-EG', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })} م (${diffDays} أيام)`;
                            }
                          })()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* أزرار الإجراءات */}
              <div className="flex gap-4 pt-6 border-t border-gray-100">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>
                      <FaCalendarAlt />
                      إضافة الإجازة الرسمية
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* الاختيارات السريعة */}
        <div className="space-y-6">
          {/* الإجازات الشائعة */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FaCalendarDay className="text-purple-600" />
                الإجازات الشائعة
              </h3>
              <p className="text-sm text-gray-600 mt-1">اختر من الإجازات الرسمية الشائعة</p>
            </div>
            <div className="p-6">
              <div className="space-y-2">
                {commonHolidays.map((holiday, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleQuickSelect(holiday)}
                    className="w-full text-right p-3 rounded-lg border border-gray-200 hover:bg-blue-50 hover:border-blue-300 transition-colors text-sm"
                  >
                    {holiday}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* نصائح */}
          <div className="bg-green-50 border border-green-200 rounded-xl p-6">
            <div className="flex items-start gap-3">
              <FaCalendarAlt className="text-green-600 mt-1" />
              <div>
                <h4 className="text-lg font-semibold text-green-800 mb-2">نصائح مهمة</h4>
                <div className="text-sm text-green-700 space-y-2">
                  <p>• <strong>تأكد من التاريخ:</strong> راجع التاريخ الصحيح للإجازة الرسمية</p>
                  <p>• <strong>اسم واضح:</strong> استخدم اسماً واضحاً ومفهوماً للإجازة</p>
                  <p>• <strong>التخطيط المسبق:</strong> أضف الإجازات مبكراً للتخطيط الأفضل</p>
                  <p>• <strong>التحديث:</strong> يمكن تعديل أو حذف الإجازة لاحقاً إذا لزم الأمر</p>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-start gap-3">
              <FaUserShield className="text-blue-600 mt-1" />
              <div>
                <h4 className="text-lg font-semibold text-blue-800 mb-2">من يمكنه الإدارة؟</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>• <strong>مدير النظام:</strong> صلاحيات كاملة</p>
                  <p>• <strong>الموارد البشرية:</strong> إضافة وتعديل وحذف</p>
                  <p>• <strong>باقي الموظفين:</strong> عرض فقط</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
