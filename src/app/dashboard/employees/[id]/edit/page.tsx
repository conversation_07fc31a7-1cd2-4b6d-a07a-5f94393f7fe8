"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { FaArrowRight, FaSave } from "react-icons/fa";

const employeeSchema = z.object({
  employeeNumber: z.string().optional(),
  name: z.string().min(1, "اسم الموظف مطلوب").max(100, "الاسم طويل جداً"),
  email: z.string().email("البريد الإلكتروني غير صالح"),
  phone: z.string().min(1, "رقم الهاتف مطلوب"),
  position: z.string().optional(),
  password: z.string().optional(),
  role: z.enum(["ADMIN", "HR", "MANAGER", "<PERSON><PERSON><PERSON>OY<PERSON>", "SECURITY"]),
  departmentId: z.string().optional(),
  managerId: z.string().optional(),
});

type EmployeeFormValues = z.infer<typeof employeeSchema>;

interface Employee {
  id: string;
  employeeNumber: string | null;
  name: string;
  email: string;
  phone: string;
  position: string | null;
  role: string;
  departmentId: string | null;
  managerId: string | null;
  department: {
    id: string;
    name: string;
  } | null;
  manager: {
    id: string;
    name: string;
  } | null;
}

interface Department {
  id: string;
  name: string;
}

interface Manager {
  id: string;
  name: string;
  email: string;
}

const roleOptions = [
  { value: "EMPLOYEE", label: "موظف" },
  { value: "MANAGER", label: "مدير" },
  { value: "HR", label: "موارد بشرية" },
  { value: "SECURITY", label: "أمن" },
  { value: "ADMIN", label: "مدير النظام" },
];

export default function EditEmployeePage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [managers, setManagers] = useState<Manager[]>([]);
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<EmployeeFormValues>({
    resolver: zodResolver(employeeSchema),
  });

  const selectedDepartmentId = watch("departmentId");

  // دالة جلب بيانات الموظف
  const fetchEmployee = async (id: string) => {
    try {
      const response = await fetch(`/api/employees/${id}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب بيانات الموظف");
      }
      const data = await response.json();
      setEmployee(data);
      
      // تعبئة النموذج بالبيانات الحالية
      setValue("employeeNumber", data.employeeNumber || "");
      setValue("name", data.name);
      setValue("email", data.email);
      setValue("phone", data.phone || "");
      setValue("position", data.position || "");
      setValue("role", data.role);
      setValue("departmentId", data.departmentId || "");
      setValue("managerId", data.managerId || "");
    } catch (error) {
      console.error("خطأ في جلب الموظف:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    }
  };

  // جلب الأقسام والمدراء
  const fetchData = async () => {
    try {
      // جلب الأقسام
      const departmentsResponse = await fetch("/api/departments");
      if (departmentsResponse.ok) {
        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData);
      }

      // جلب الموظفين لقائمة المدراء
      const employeesResponse = await fetch("/api/employees");
      if (employeesResponse.ok) {
        const employeesData = await employeesResponse.json();
        // API يُرجع مصفوفة مباشرة وليس كائن يحتوي على employees
        if (Array.isArray(employeesData)) {
          // فلترة المدراء فقط واستبعاد الموظف الحالي (إذا كان متوفراً)
          const managersData = employeesData.filter((emp: any) => {
            // استبعاد الموظف الحالي إذا كان resolvedParams متوفراً
            const isCurrentEmployee = resolvedParams?.id && emp.id === resolvedParams.id;
            // تضمين المدراء والموارد البشرية والمدراء العامين فقط
            const isManager = emp.role === "MANAGER" || emp.role === "HR" || emp.role === "ADMIN";
            return !isCurrentEmployee && isManager;
          });
          setManagers(managersData);
        }
      }
    } catch (error) {
      console.error("خطأ في جلب البيانات:", error);
    }
  };

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParamsData = await params;
      setResolvedParams(resolvedParamsData);
    };
    loadParams();
  }, [params]);

  useEffect(() => {
    if (!resolvedParams?.id) return;
    
    const loadData = async () => {
      setPageLoading(true);
      // جلب بيانات الموظف أولاً
      await fetchEmployee(resolvedParams.id);
      // ثم جلب الأقسام والمدراء
      await fetchData();
      setPageLoading(false);
    };

    loadData();
  }, [resolvedParams?.id]);

  const onSubmit = async (data: EmployeeFormValues) => {
    if (!resolvedParams?.id) return;
    
    setLoading(true);
    setError(null);

    try {
      const updateData: any = {
        employeeNumber: data.employeeNumber || null,
        name: data.name,
        email: data.email,
        phone: data.phone,
        position: data.position || null,
        role: data.role,
        departmentId: data.departmentId || null,
        managerId: data.managerId || null,
      };

      // إضافة كلمة المرور فقط إذا تم إدخالها
      if (data.password && data.password.trim() !== "") {
        if (data.password.length < 6) {
          setError("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
          setLoading(false);
          return;
        }
        updateData.password = data.password;
      }

      const response = await fetch(`/api/employees/${resolvedParams.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تعديل الموظف");
      }

      // إظهار رسالة نجاح
      alert("تم تحديث الموظف بنجاح" + (data.password && data.password.trim() !== "" ? " وتم تغيير كلمة المرور" : ""));
      
      router.push("/dashboard/employees");
      router.refresh();
    } catch (error) {
      console.error("خطأ في تعديل الموظف:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  if (pageLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">الموظف غير موجود</p>
        <Link href="/dashboard/employees" className="btn-primary mt-4">
          العودة للموظفين
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-6">
        <Link
          href="/dashboard/employees"
          className="text-blue-600 hover:text-blue-800"
        >
          <FaArrowRight size={20} />
        </Link>
        <h1 className="text-2xl font-bold">تعديل الموظف: {employee.name}</h1>
      </div>

      <div className="card max-w-2xl">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                الرقم الوظيفي (اختياري)
              </label>
              <input
                type="text"
                className="input"
                placeholder="أدخل الرقم الوظيفي"
                {...register("employeeNumber")}
                disabled={loading}
              />
              {errors.employeeNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.employeeNumber.message}</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                اسم الموظف *
              </label>
              <input
                type="text"
                className="input"
                placeholder="أدخل اسم الموظف"
                {...register("name")}
                disabled={loading}
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                className="input"
                placeholder="أدخل البريد الإلكتروني"
                {...register("email")}
                disabled={loading}
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                className="input"
                placeholder="أدخل رقم الهاتف"
                {...register("phone")}
                disabled={loading}
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                المنصب (اختياري)
              </label>
              <input
                type="text"
                className="input"
                placeholder="أدخل المنصب"
                {...register("position")}
                disabled={loading}
              />
              {errors.position && (
                <p className="text-red-500 text-sm mt-1">{errors.position.message}</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                كلمة المرور الجديدة (اختياري)
              </label>
              <input
                type="password"
                className="input"
                placeholder="اتركه فارغاً للاحتفاظ بكلمة المرور الحالية"
                {...register("password")}
                disabled={loading}
              />
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                الدور *
              </label>
              <select
                className="input"
                {...register("role")}
                disabled={loading}
              >
                {roleOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.role && (
                <p className="text-red-500 text-sm mt-1">{errors.role.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                القسم (اختياري)
              </label>
              <select
                className="input"
                {...register("departmentId")}
                disabled={loading}
              >
                <option value="">اختر القسم</option>
                {departments.map((department) => (
                  <option key={department.id} value={department.id}>
                    {department.name}
                  </option>
                ))}
              </select>
              {errors.departmentId && (
                <p className="text-red-500 text-sm mt-1">{errors.departmentId.message}</p>
              )}
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                المدير المباشر (اختياري)
              </label>
              <select
                className="input"
                {...register("managerId")}
                disabled={loading}
              >
                <option value="">اختر المدير</option>
                {managers.map((manager) => (
                  <option key={manager.id} value={manager.id}>
                    {manager.name} - {manager.email}
                  </option>
                ))}
              </select>
              {errors.managerId && (
                <p className="text-red-500 text-sm mt-1">{errors.managerId.message}</p>
              )}
            </div>
          </div>

          <div className="flex gap-4 mt-8">
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center gap-2 disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              ) : (
                <FaSave size={16} />
              )}
              {loading ? "جاري الحفظ..." : "حفظ التعديلات"}
            </button>

            <Link
              href="/dashboard/employees"
              className="btn-secondary"
            >
              إلغاء
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
