"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { FaPlus, FaEdit, FaTrash, Fa<PERSON>sers, FaUser<PERSON>ie, FaPrint } from "react-icons/fa";
import Link from "next/link";
import { DataTable, Column } from "@/components/ui/DataTable";
import dynamic from "next/dynamic";

const EmployeesPrintReport = dynamic(() => import("../../../components/reports/EmployeesPrintReport").then(mod => ({ default: mod.EmployeesPrintReport })), {
  ssr: false
});

interface Employee {
  id: string;
  employeeNumber: string | null;
  name: string;
  email: string;
  phone: string;
  position: string | null;
  role: string;
  departmentId: string | null;
  managerId: string | null;
  createdAt: string;
  updatedAt: string;
  department: {
    id: string;
    name: string;
  } | null;
  manager: {
    id: string;
    name: string;
  } | null;
  _count: {
    employees: number;
  };
}

const roleLabels = {
  ADMIN: "مدير النظام",
  HR: "موارد بشرية",
  MANAGER: "مدير",
  EMPLOYEE: "موظف",
  SECURITY: "أمن",
};

const roleColors = {
  ADMIN: "bg-red-100 text-red-800",
  HR: "bg-purple-100 text-purple-800",
  MANAGER: "bg-blue-100 text-blue-800",
  EMPLOYEE: "bg-green-100 text-green-800",
  SECURITY: "bg-yellow-100 text-yellow-800",
};

export default function EmployeesPage() {
  const { data: session } = useSession();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [showPrintReport, setShowPrintReport] = useState(false);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const dataTableRef = useRef<any>(null);

  // تعريف أعمدة الجدول
  const columns: Column<Employee>[] = [
    {
      key: 'name',
      title: 'الاسم',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.employeeNumber && (
            <div className="text-sm text-gray-500">#{row.employeeNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'email',
      title: 'البريد الإلكتروني',
      sortable: true,
      searchable: true,
      render: (value) => <span className="text-gray-600">{value}</span>,
    },
    {
      key: 'position',
      title: 'المنصب',
      sortable: true,
      searchable: true,
      render: (value) => (
        value ? (
          <span className="text-purple-600">{value}</span>
        ) : (
          <span className="text-gray-400">غير محدد</span>
        )
      ),
    },
    {
      key: 'phone',
      title: 'رقم الهاتف',
      sortable: true,
      searchable: true,
      render: (value) => <span className="text-gray-600">{value}</span>,
    },
    {
      key: 'role',
      title: 'الدور',
      sortable: true,
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-sm ${roleColors[value as keyof typeof roleColors]}`}>
          {roleLabels[value as keyof typeof roleLabels]}
        </span>
      ),
    },
    {
      key: 'department.name',
      title: 'القسم',
      sortable: true,
      searchable: true,
      render: (value) => (
        value ? (
          <span className="text-blue-600">{value}</span>
        ) : (
          <span className="text-gray-400">غير محدد</span>
        )
      ),
    },
    {
      key: 'manager.name',
      title: 'المدير المباشر',
      sortable: true,
      searchable: true,
      render: (value, row) => (
        value ? (
          <div className="flex items-center gap-2">
            <FaUserTie className="text-indigo-500" size={14} />
            <span className="text-indigo-600">{value}</span>
          </div>
        ) : (
          <span className="text-gray-400">غير محدد</span>
        )
      ),
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      className: 'text-center',
      render: (_, row) => (
        <div className="flex justify-center gap-2">
          <Link
            href={`/dashboard/employees/${row.id}/edit`}
            className="text-blue-600 hover:text-blue-800 p-2"
            title="تعديل"
          >
            <FaEdit size={16} />
          </Link>
          <button
            onClick={() => handleDelete(row.id, row.name)}
            disabled={deleteLoading === row.id}
            className="text-red-600 hover:text-red-800 p-2 disabled:opacity-50"
            title="حذف"
          >
            {deleteLoading === row.id ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-600"></div>
            ) : (
              <FaTrash size={16} />
            )}
          </button>
        </div>
      ),
    },
  ];

  // جلب الموظفين
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/employees");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في جلب الموظفين");
      }

      const data = await response.json();
      setEmployees(data);
      setError(null);
    } catch (error) {
      console.error("خطأ في جلب الموظفين:", error);
      setError(error instanceof Error ? error.message : "حدث خطأ غير متوقع");
    } finally {
      setLoading(false);
    }
  };

  // حذف موظف
  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`هل أنت متأكد من حذف الموظف "${name}"؟`)) {
      return;
    }

    try {
      setDeleteLoading(id);
      const response = await fetch(`/api/employees/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في حذف الموظف");
      }

      // إعادة جلب الموظفين بعد الحذف
      await fetchEmployees();
      alert("تم حذف الموظف بنجاح");
    } catch (error) {
      console.error("خطأ في حذف الموظف:", error);
      alert(error instanceof Error ? error.message : "حدث خطأ في حذف الموظف");
    } finally {
      setDeleteLoading(null);
    }
  };

  // طباعة التقرير
  const handlePrintReport = () => {
    // الحصول على البيانات المفلترة من DataTable
    const currentFilteredData = dataTableRef.current?.getFilteredData() || employees;
    setFilteredEmployees(currentFilteredData);
    setShowPrintReport(true);
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  // التحقق من الصلاحيات
  const userRole = session?.user?.role;
  const canManage = userRole === "ADMIN" || userRole === "HR";

  if (!canManage) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة الموظفين</h1>
        <div className="flex gap-3">
          <button
            onClick={handlePrintReport}
            className="btn-secondary flex items-center gap-2"
            disabled={loading || employees.length === 0}
          >
            <FaPrint size={16} />
            طباعة التقرير
          </button>
          <Link
            href="/dashboard/employees/new"
            className="btn-primary flex items-center gap-2"
          >
            <FaPlus size={16} />
            إضافة موظف جديد
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {employees.length === 0 && !loading ? (
        <div className="card">
          <div className="text-center py-8">
            <FaUsers size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">لا يوجد موظفين مسجلين</p>
            <Link
              href="/dashboard/employees/new"
              className="btn-primary"
            >
              إضافة أول موظف
            </Link>
          </div>
        </div>
      ) : (
        <DataTable
          ref={dataTableRef}
          data={employees}
          columns={columns}
          loading={loading}
          searchPlaceholder="البحث في الموظفين..."
          pageSize={10}
        />
      )}

      {/* مكون طباعة التقرير */}
      {showPrintReport && (
        <EmployeesPrintReport
          employees={filteredEmployees}
          onClose={() => setShowPrintReport(false)}
        />
      )}
    </div>
  );
}
