import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

const prisma = new PrismaClient();

// GET - جلب قسم واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;
    const department = await prisma.department.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    if (!department) {
      return NextResponse.json({ error: "القسم غير موجود" }, { status: 404 });
    }

    // جلب معلومات رئيس القسم إذا كان موجوداً
    let head = null;
    if (department.headId) {
      head = await prisma.user.findUnique({
        where: { id: department.headId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    }

    const departmentWithHead = {
      ...department,
      head,
    };

    return NextResponse.json(departmentWithHead);
  } catch (error) {
    console.error("خطأ في جلب القسم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب القسم" },
      { status: 500 }
    );
  }
}

// PUT - تعديل قسم
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, headId } = body;

    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم القسم مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم
    const existingDepartment = await prisma.department.findUnique({
      where: { id },
    });

    if (!existingDepartment) {
      return NextResponse.json({ error: "القسم غير موجود" }, { status: 404 });
    }

    // التحقق من عدم تكرار اسم القسم (باستثناء القسم الحالي)
    const duplicateDepartment = await prisma.department.findFirst({
      where: {
        name: name.trim(),
        id: { not: id },
      },
    });

    if (duplicateDepartment) {
      return NextResponse.json(
        { error: "اسم القسم موجود بالفعل" },
        { status: 400 }
      );
    }

    const department = await prisma.department.update({
      where: { id },
      data: {
        name: name.trim(),
        headId: headId || null,
      },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    // جلب معلومات رئيس القسم إذا كان موجوداً
    let head = null;
    if (department.headId) {
      head = await prisma.user.findUnique({
        where: { id: department.headId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    }

    const departmentWithHead = {
      ...department,
      head,
    };

    return NextResponse.json(departmentWithHead);
  } catch (error) {
    console.error("خطأ في تعديل القسم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تعديل القسم" },
      { status: 500 }
    );
  }
}

// DELETE - حذف قسم
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;

    // التحقق من وجود القسم
    const department = await prisma.department.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    if (!department) {
      return NextResponse.json({ error: "القسم غير موجود" }, { status: 404 });
    }

    // التحقق من عدم وجود موظفين في القسم
    if (department._count.users > 0) {
      return NextResponse.json(
        { error: "لا يمكن حذف القسم لأنه يحتوي على موظفين" },
        { status: 400 }
      );
    }

    await prisma.department.delete({
      where: { id },
    });

    return NextResponse.json({ message: "تم حذف القسم بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف القسم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف القسم" },
      { status: 500 }
    );
  }
}
