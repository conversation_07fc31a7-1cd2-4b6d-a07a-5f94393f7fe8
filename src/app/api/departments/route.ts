import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

const prisma = new PrismaClient();

// GET - جلب جميع الأقسام
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    // التحقق من الصلاحيات - السماح لجميع المستخدمين المسجلين بعرض الأقسام
    const userRole = session.user?.role;
    if (!userRole) {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const departments = await prisma.department.findMany({
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // جلب معلومات رؤساء الأقسام بشكل منفصل
    const departmentsWithHeads = await Promise.all(
      departments.map(async (department) => {
        let head = null;
        if (department.headId) {
          head = await prisma.user.findUnique({
            where: { id: department.headId },
            select: {
              id: true,
              name: true,
              email: true,
            },
          });
        }
        return {
          ...department,
          head,
        };
      })
    );

    return NextResponse.json(departmentsWithHeads);
  } catch (error) {
    console.error("خطأ في جلب الأقسام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الأقسام" },
      { status: 500 }
    );
  }
}

// POST - إنشاء قسم جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { name, headId } = body;

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم القسم مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار اسم القسم
    const existingDepartment = await prisma.department.findFirst({
      where: {
        name: name.trim(),
      },
    });

    if (existingDepartment) {
      return NextResponse.json(
        { error: "اسم القسم موجود بالفعل" },
        { status: 400 }
      );
    }

    // إنشاء القسم الجديد
    const department = await prisma.department.create({
      data: {
        name: name.trim(),
        headId: headId || null,
      },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    // جلب معلومات رئيس القسم إذا كان موجوداً
    let head = null;
    if (department.headId) {
      head = await prisma.user.findUnique({
        where: { id: department.headId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    }

    const departmentWithHead = {
      ...department,
      head,
    };

    return NextResponse.json(departmentWithHead, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء القسم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء القسم" },
      { status: 500 }
    );
  }
}
