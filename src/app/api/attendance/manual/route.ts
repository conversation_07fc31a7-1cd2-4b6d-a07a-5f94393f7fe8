import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { employeeId, date, checkInTime, checkOutTime, exitType, entryNumber } = body;

    if (!employeeId || !date || !checkInTime) {
      return NextResponse.json({ error: "الموظف والتاريخ ووقت الدخول مطلوبان" }, { status: 400 });
    }

    // جلب الإعدادات للحصول على ساعات العمل
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // التحقق من وجود الموظف
    const employee = await prisma.user.findUnique({
      where: { id: employeeId }
    });

    if (!employee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    // التحقق من عدم وجود سجل بنفس رقم الحركة في نفس التاريخ
    const existingRecord = await prisma.attendanceRecord.findFirst({
      where: {
        userId: employeeId,
        date: new Date(date),
        entryNumber: entryNumber || 1,
      },
    });

    if (existingRecord) {
      return NextResponse.json({ 
        error: `يوجد سجل حضور برقم الحركة ${entryNumber || 1} لهذا الموظف في هذا التاريخ` 
      }, { status: 400 });
    }

    // حساب وقت انتهاء العمل المتوقع = وقت الدخول + ساعات العمل
    const checkIn = new Date(checkInTime);
    const expectedEndTime = new Date(checkIn.getTime() + (workingHours * 60 * 60 * 1000));

    // إنشاء سجل الحضور مع وقت انتهاء العمل المتوقع
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        userId: employeeId,
        date: new Date(date),
        checkInTime: checkIn,
        checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
        exitType: exitType || 'OFFICIAL',
        entryNumber: entryNumber || 1,
        emp_working_hrs: expectedEndTime.getTime(), // حفظ وقت انتهاء العمل المتوقع كـ timestamp
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم إنشاء سجل الحضور بنجاح",
      record: {
        ...attendanceRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
    });
  } catch (error) {
    console.error("خطأ في إنشاء سجل الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء سجل الحضور" },
      { status: 500 }
    );
  }
}
