import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const recordId = resolvedParams.id;
    const body = await request.json();
    const { checkInTime, checkOutTime, exitType } = body;

    // جلب الإعدادات للحصول على ساعات العمل الحالية
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // التحقق من وجود السجل
    const existingRecord = await prisma.attendanceRecord.findUnique({
      where: { id: recordId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!existingRecord) {
      return NextResponse.json({ error: "سجل الحضور غير موجود" }, { status: 404 });
    }

    // تحديد وقت الدخول (الجديد أو الموجود)
    const finalCheckInTime = checkInTime ? new Date(checkInTime) : existingRecord.checkInTime;
    
    // حساب وقت انتهاء العمل المتوقع الجديد = وقت الدخول + ساعات العمل
    const expectedEndTime = new Date(finalCheckInTime.getTime() + (workingHours * 60 * 60 * 1000));

    // تحديث السجل مع وقت انتهاء العمل المتوقع الجديد
    const updatedRecord = await prisma.attendanceRecord.update({
      where: { id: recordId },
      data: {
        checkInTime: finalCheckInTime,
        checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
        exitType: exitType || existingRecord.exitType,
        emp_working_hrs: expectedEndTime.getTime(), // تحديث وقت انتهاء العمل المتوقع
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تحديث سجل الحضور بنجاح",
      record: {
        ...updatedRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
    });
  } catch (error) {
    console.error("خطأ في تحديث سجل الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث سجل الحضور" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const recordId = resolvedParams.id;

    // التحقق من وجود السجل
    const existingRecord = await prisma.attendanceRecord.findUnique({
      where: { id: recordId },
    });

    if (!existingRecord) {
      return NextResponse.json({ error: "سجل الحضور غير موجود" }, { status: 404 });
    }

    // حذف السجل
    await prisma.attendanceRecord.delete({
      where: { id: recordId },
    });

    return NextResponse.json({
      message: "تم حذف سجل الحضور بنجاح",
    });
  } catch (error) {
    console.error("خطأ في حذف سجل الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف سجل الحضور" },
      { status: 500 }
    );
  }
}
