import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const departmentFilter = searchParams.get('department');
    const employeeFilter = searchParams.get('employee');
    const statusFilter = searchParams.get('status');
    const exitTypeFilter = searchParams.get('exitType');

    if (!startDateParam || !endDateParam) {
      return NextResponse.json({ error: "تاريخ البداية والنهاية مطلوبان" }, { status: 400 });
    }

    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);
    endDate.setHours(23, 59, 59, 999); // نهاية اليوم

    // جلب الإعدادات
    const settings = await prisma.settings.findFirst();
    const minWorkingHours = settings?.workHoursRequired || 8;
    const workingHours = (settings as any)?.workingHours || 7;

    // جلب الإجازات الرسمية في الفترة
    const officialHolidays = await prisma.officialHoliday.findMany({
      where: {
        OR: [
          {
            startDate: {
              gte: startDate,
              lte: endDate,
            },
          },
          {
            endDate: {
              gte: startDate,
              lte: endDate,
            },
          },
          {
            AND: [
              { startDate: { lte: startDate } },
              { endDate: { gte: endDate } },
            ],
          },
        ],
      },
    });

    // جلب جميع الموظفين مع تطبيق فلاتر
    const employeeWhere: Record<string, any> = {
      role: {
        in: ["EMPLOYEE", "MANAGER", "HR"]
      }
    };

    if (departmentFilter) {
      employeeWhere.department = {
        name: {
          contains: departmentFilter
        }
      };
    }

    if (employeeFilter) {
      employeeWhere.OR = [
        { name: { contains: employeeFilter } },
        { employeeNumber: { contains: employeeFilter } }
      ];
    }

    const allEmployees = await prisma.user.findMany({
      where: employeeWhere,
      include: {
        department: {
          select: {
            name: true,
          },
        },
      },
    });

    // إنشاء مصفوفة التواريخ في الفترة المحددة
    const dateRange = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // جلب الإجازات المعتمدة في الفترة
    const approvedLeaves = await prisma.leaveRequest.findMany({
      where: {
        status: 'APPROVED',
        OR: [
          {
            startDate: {
              gte: startDate,
              lte: endDate,
            },
          },
          {
            endDate: {
              gte: startDate,
              lte: endDate,
            },
          },
          {
            AND: [
              { startDate: { lte: startDate } },
              { endDate: { gte: endDate } },
            ],
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
          },
        },
      },
    });

    // جلب سجلات الحضور في الفترة
    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: [
        { date: 'desc' },
        { userId: 'asc' },
        { entryNumber: 'asc' },
      ],
    });

    // تجميع البيانات حسب التاريخ والموظف
    const attendanceData: Record<string, any>[] = [];

    // معالجة كل تاريخ في الفترة
    for (const selectedDate of dateRange) {
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      // التحقق من العطل
      const isWeekend = selectedDate.getDay() === 5 || selectedDate.getDay() === 6; // الجمعة والسبت
      const officialHoliday = officialHolidays.find(holiday =>
        selectedDate >= new Date(holiday.startDate) && selectedDate <= new Date(holiday.endDate)
      );

      // الموظفون في إجازة في هذا التاريخ
      const employeesOnLeave = new Set(
        approvedLeaves
          .filter(leave =>
            selectedDate >= new Date(leave.startDate) && selectedDate <= new Date(leave.endDate)
          )
          .map(leave => leave.userId)
      );

      // معالجة كل موظف
      for (const employee of allEmployees) {
        let status: 'PRESENT' | 'ABSENT' | 'PARTIAL' | 'ON_LEAVE' | 'HOLIDAY' = 'ABSENT';
        let leaveReason: string | undefined;

        // إذا كان اليوم عطلة أسبوعية
        if (isWeekend) {
          status = 'HOLIDAY';
        }
        // إذا كان اليوم إجازة رسمية
        else if (officialHoliday) {
          status = 'HOLIDAY';
        }
        // إذا كان الموظف في إجازة معتمدة
        else if (employeesOnLeave.has(employee.id)) {
          status = 'ON_LEAVE';
          const leave = approvedLeaves.find(l =>
            l.userId === employee.id &&
            selectedDate >= new Date(l.startDate) &&
            selectedDate <= new Date(l.endDate)
          );
          leaveReason = leave?.reason;
        }
        // للأيام العادية
        else {
          const employeeRecords = attendanceRecords.filter(record =>
            record.userId === employee.id &&
            record.date >= startOfDay &&
            record.date <= endOfDay
          );

          if (employeeRecords.length === 0) {
            status = 'ABSENT';
          } else {
            // حساب إجمالي ساعات العمل
            const totalWorkingHours = employeeRecords.reduce((total, record) => {
              if (record.checkInTime && record.checkOutTime) {
                const checkIn = new Date(record.checkInTime);
                const checkOut = new Date(record.checkOutTime);
                const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
                return total + hours;
              }
              return total;
            }, 0);

            // تحديد الحالة بناءً على ساعات العمل
            if (totalWorkingHours >= minWorkingHours) {
              status = 'PRESENT'; // حاضر: ساعات العمل >= الحد الأدنى
            } else if (totalWorkingHours > 0) {
              status = 'PARTIAL'; // جزئي: ساعات العمل < الحد الأدنى وأكبر من 0
            } else {
              status = 'ABSENT'; // غائب: لا توجد ساعات عمل
            }
          }
        }

        // إعداد بيانات الحركات
        const movements = attendanceRecords
          .filter(record =>
            record.userId === employee.id &&
            record.date >= startOfDay &&
            record.date <= endOfDay
          )
          .map(record => ({
            id: record.id,
            checkInTime: record.checkInTime?.toISOString() || null,
            checkOutTime: record.checkOutTime?.toISOString() || null,
            exitType: record.exitType,
            entryNumber: record.entryNumber,
            emp_working_hrs: record.emp_working_hrs,
          }));

        // حساب إجمالي ساعات العمل ومدة الخروج
        const totalWorkingHours = movements.reduce((total, movement) => {
          if (movement.checkInTime && movement.checkOutTime) {
            const checkIn = new Date(movement.checkInTime);
            const checkOut = new Date(movement.checkOutTime);
            const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
            return total + hours;
          }
          return total;
        }, 0);

        // حساب مدة الخروج (الفترات بين الخروج والدخول التالي)
        const totalBreakTime = calculateBreakTime(movements, settings);

        // تطبيق الفلاتر
        if (statusFilter && status !== statusFilter) continue;
        if (exitTypeFilter && !movements.some(m => m.exitType === exitTypeFilter)) continue;

        attendanceData.push({
          employeeId: employee.id,
          employeeName: employee.name,
          employeeNumber: employee.employeeNumber,
          department: employee.department?.name || 'غير محدد',
          date: selectedDate.toISOString().split('T')[0],
          status,
          totalWorkingHours,
          totalBreakTime,
          movements,
          leaveReason,
        });
      }
    }

    return NextResponse.json({
      attendanceData,
      settings: {
        minWorkingHours,
        workStartTime: settings?.workStartTime || '08:00',
        workEndTime: settings?.workEndTime || '17:00',
        workingHours: settings?.workingHours || 7,
      },
      summary: {
        totalEmployees: allEmployees.length,
        totalRecords: attendanceData.length,
      },
    });
  } catch (error) {
    console.error("خطأ في جلب تقرير الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب تقرير الحضور" },
      { status: 500 }
    );
  }
}

// حساب مدة الخروج من العمل
function calculateBreakTime(movements: Record<string, any>[], settings: Record<string, any> | null): number {
  if (movements.length === 0) return 0;

  let totalBreakTime = 0;
  const workEndTime = settings?.workEndTime || '17:00';
  const workingHours = settings?.workingHours || 7;

  // ترتيب الحركات حسب entryNumber
  const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);

  // حساب مدة الخروج بين الحركات
  for (let i = 0; i < sortedMovements.length - 1; i++) {
    const currentMovement = sortedMovements[i];
    const nextMovement = sortedMovements[i + 1];

    // إذا كان الموظف خرج في الحركة الحالية ودخل في الحركة التالية
    if (currentMovement.checkOutTime && nextMovement.checkInTime) {
      const checkOut = new Date(currentMovement.checkOutTime);
      const nextCheckIn = new Date(nextMovement.checkInTime);

      // حساب الفترة بين الخروج والدخول التالي
      const breakDuration = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
      if (breakDuration > 0) {
        totalBreakTime += breakDuration;
      }
    }
  }

  // للحركة الأخيرة: إذا خرج الموظف ولم يعد
  const lastMovement = sortedMovements[sortedMovements.length - 1];
  if (lastMovement.checkOutTime) {
    const firstMovement = sortedMovements[0];
    
    // إذا كان هناك دخول في الحركة الأولى وخروج في الحركة الأخيرة ولم يعد
    if (firstMovement.checkInTime) {
      // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
      if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
        // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
        const expectedEndTime = new Date(firstMovement.emp_working_hrs);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // إذا خرج قبل الوقت المتوقع
        if (checkOut < expectedEndTime) {
          const breakTime = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      } else {
        // النظام القديم: حساب بناءً على ساعات العمل الثابتة
        const checkIn = new Date(firstMovement.checkInTime);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // حساب الوقت الفعلي الذي قضاه الموظف في العمل
        const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        
        // إذا كان الوقت الفعلي أقل من عدد ساعات العمل المطلوبة
        if (actualWorkHours < workingHours) {
          // مدة الخروج = عدد ساعات العمل - الوقت الفعلي
          const breakTime = workingHours - actualWorkHours;
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      }
    } else {
      // إذا لم يكن هناك دخول، استخدم المنطق القديم
      const lastCheckOut = new Date(lastMovement.checkOutTime);
      
      // إنشاء وقت انتهاء العمل لنفس التاريخ
      const workEnd = new Date(lastCheckOut);
      const [hours, minutes] = workEndTime.split(':');
      workEnd.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // إذا كان الخروج قبل انتهاء العمل ولم يعد
      if (lastCheckOut < workEnd) {
        const remainingTime = (workEnd.getTime() - lastCheckOut.getTime()) / (1000 * 60 * 60);
        totalBreakTime += remainingTime;
      }
    }
  }

  return totalBreakTime;
}
