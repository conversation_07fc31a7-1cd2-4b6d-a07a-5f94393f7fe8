import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR" && userRole !== "EMPLOYEE" && userRole !== "MANAGER") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const departmentFilter = searchParams.get('department') || '';
    const employeeFilter = searchParams.get('employee') || '';
    const statusFilter = searchParams.get('status') || '';
    const exitTypeFilter = searchParams.get('exitType') || '';
    const managerFilter = searchParams.get('managerFilter') || '';

    if (!startDateParam || !endDateParam) {
      return NextResponse.json({ error: "تاريخ البداية والنهاية مطلوبان" }, { status: 400 });
    }

    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);
    endDate.setHours(23, 59, 59, 999);

    // جلب الإعدادات
    const settings = await prisma.settings.findFirst();
    const minWorkingHours = settings?.workHoursRequired || 8;

    // جلب الموظفين مع الفلاتر
    const employeeWhere: Record<string, any> = {
      role: { in: ["EMPLOYEE", "MANAGER", "HR"] }
    };

    if (userRole === "EMPLOYEE") {
      employeeWhere.id = session.user.id;
    } else if (userRole === "MANAGER") {
      // المدير يرى نفسه والموظفين التابعين له مباشرة
      employeeWhere.OR = [
        { id: session.user.id }, // المدير نفسه
        { managerId: session.user.id } // الموظفين التابعين له
      ];
    }

    if (departmentFilter) {
      employeeWhere.department = { name: departmentFilter };
    }

    if (employeeFilter) {
      employeeWhere.id = employeeFilter;
    }

    const employees = await prisma.user.findMany({
      where: employeeWhere,
      include: { department: { select: { name: true } } },
    });

    // جلب الإجازات المعتمدة في الفترة
    const approvedLeaves = await prisma.leaveRequest.findMany({
      where: {
        status: 'APPROVED',
        userId: { in: employees.map(e => e.id) },
        OR: [
          { startDate: { gte: startDate, lte: endDate } },
          { endDate: { gte: startDate, lte: endDate } },
          { startDate: { lte: startDate }, endDate: { gte: endDate } }
        ]
      }
    });

    // جلب العطل الرسمية في الفترة
    const officialHolidays = await prisma.officialHoliday.findMany({
      where: {
        OR: [
          { startDate: { gte: startDate, lte: endDate } },
          { endDate: { gte: startDate, lte: endDate } },
          { startDate: { lte: startDate }, endDate: { gte: endDate } }
        ]
      }
    });

    // جلب جميع سجلات الحضور في الفترة
    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        date: { gte: startDate, lte: endDate },
        userId: { in: employees.map(e => e.id) }
      },
      include: { user: { select: { id: true, name: true, employeeNumber: true } } },
      orderBy: [{ date: 'desc' }, { userId: 'asc' }, { entryNumber: 'asc' }],
    });

    // تجميع البيانات حسب التاريخ والموظف
    const attendanceData: Record<string, any>[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dayOfWeek = currentDate.getDay();
      const isWeekend = dayOfWeek === 5 || dayOfWeek === 6;

      for (const employee of employees) {
        // التحقق من العطل الرسمية
        const isOfficialHoliday = officialHolidays.some(holiday => {
          const holidayStart = new Date(holiday.startDate);
          const holidayEnd = new Date(holiday.endDate);
          return currentDate >= holidayStart && currentDate <= holidayEnd;
        });

        // تخطي العطل الرسمية - لا تظهر في الكشف
        if (isOfficialHoliday) {
          continue;
        }

        // التحقق من الإجازات المعتمدة
        const employeeLeave = approvedLeaves.find(leave => {
          const leaveStart = new Date(leave.startDate);
          const leaveEnd = new Date(leave.endDate);
          return leave.userId === employee.id && 
                 currentDate >= leaveStart && currentDate <= leaveEnd;
        });

        // جلب سجلات الحضور لهذا الموظف في هذا التاريخ
        const dayRecords = attendanceRecords.filter(record => {
          const recordDate = new Date(record.date);
          return record.userId === employee.id &&
                 recordDate.toDateString() === currentDate.toDateString();
        });

        // تحديد الحالة
        let status = 'ABSENT';
        let leaveReason = '';

        // تخطي أيام الويكند
        if (isWeekend) {
          continue; // تخطي هذا اليوم ولا تضيفه للبيانات
        }

        // إذا كان الموظف في إجازة معتمدة
        if (employeeLeave) {
          status = 'ON_LEAVE';
          leaveReason = employeeLeave.reason || '';
        } else if (dayRecords.length > 0) {
          // حساب ساعات العمل للأيام العادية
          const totalHours = dayRecords.reduce((total, record) => {
            if (record.checkInTime && record.checkOutTime) {
              const checkIn = new Date(record.checkInTime);
              const checkOut = new Date(record.checkOutTime);
              return total + (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
            }
            return total;
          }, 0);

          status = totalHours >= minWorkingHours ? 'PRESENT' : 'PARTIAL';
        }

        // تطبيق فلاتر الحالة ونوع الخروج
        if (statusFilter && status !== statusFilter) continue;
        if (exitTypeFilter && !dayRecords.some(r => r.exitType === exitTypeFilter)) continue;

        // حساب مدة الخروج
        const totalBreakTime = calculateBreakTime(dayRecords, settings);

        // حساب ساعات العمل
        const totalWorkingHours = dayRecords.reduce((total, record) => {
          if (record.checkInTime && record.checkOutTime) {
            const checkIn = new Date(record.checkInTime);
            const checkOut = new Date(record.checkOutTime);
            return total + (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
          }
          return total;
        }, 0);

        attendanceData.push({
          employeeId: employee.id,
          employeeName: employee.name,
          employeeNumber: employee.employeeNumber,
          department: employee.department?.name || 'غير محدد',
          date: currentDate.toISOString().split('T')[0],
          status,
          totalWorkingHours,
          totalBreakTime,
          movements: dayRecords.map(record => ({
            id: record.id,
            checkInTime: record.checkInTime.toISOString(),
            checkOutTime: record.checkOutTime?.toISOString() || null,
            exitType: record.exitType,
            entryNumber: record.entryNumber,
            emp_working_hrs: record.emp_working_hrs,
          })),
          leaveReason,
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // تجميع البيانات حسب التاريخ
    const groupedData = attendanceData.reduce((groups: Record<string, any>, item) => {
      const date = item.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(item);
      return groups;
    }, {});

    return NextResponse.json({
      attendanceData: groupedData,
      settings: {
        minWorkingHours,
        workStartTime: settings?.workStartTime || '08:00',
        workEndTime: settings?.workEndTime || '17:00',
        workingHours: settings?.workingHours || 7,
      },
      summary: {
        totalRecords: attendanceData.length,
        dateRange: { startDate: startDateParam, endDate: endDateParam },
      },
    });
  } catch (error) {
    console.error("خطأ في جلب تقرير الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب تقرير الحضور" },
      { status: 500 }
    );
  }
}

// حساب مدة الخروج
function calculateBreakTime(movements: Record<string, any>[], settings: Record<string, any> | null): number {
  if (movements.length === 0) return 0;

  let totalBreakTime = 0;
  const workEndTime = settings?.workEndTime || '17:00';
  const workingHours = (settings as any)?.workingHours || 7;

  // ترتيب الحركات حسب entryNumber
  const sortedMovements = [...movements].sort((a, b) => a.entryNumber - b.entryNumber);

  // حساب مدة الخروج بين الحركات
  for (let i = 0; i < sortedMovements.length - 1; i++) {
    const current = sortedMovements[i];
    const next = sortedMovements[i + 1];

    // إذا كان الموظف خرج في الحركة الحالية ودخل في الحركة التالية
    if (current.checkOutTime && next.checkInTime) {
      const checkOut = new Date(current.checkOutTime);
      const nextCheckIn = new Date(next.checkInTime);
      const breakDuration = (nextCheckIn.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
      if (breakDuration > 0) {
        totalBreakTime += breakDuration;
      }
    }
  }

  // للحركة الأخيرة: إذا خرج الموظف ولم يعد
  const lastMovement = sortedMovements[sortedMovements.length - 1];
  if (lastMovement.checkOutTime) {
    const firstMovement = sortedMovements[0];
    
    // إذا كان هناك دخول في الحركة الأولى وخروج في الحركة الأخيرة ولم يعد
    if (firstMovement.checkInTime) {
      // استخدام النظام المرن: التحقق من وجود emp_working_hrs كوقت انتهاء متوقع
      if (firstMovement.emp_working_hrs && typeof firstMovement.emp_working_hrs === 'number') {
        // emp_working_hrs يحتوي على timestamp لوقت انتهاء العمل المتوقع
        const expectedEndTime = new Date(firstMovement.emp_working_hrs);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // إذا خرج قبل الوقت المتوقع
        if (checkOut < expectedEndTime) {
          const breakTime = (expectedEndTime.getTime() - checkOut.getTime()) / (1000 * 60 * 60);
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      } else {
        // النظام القديم: حساب بناءً على ساعات العمل الثابتة
        const checkIn = new Date(firstMovement.checkInTime);
        const checkOut = new Date(lastMovement.checkOutTime);
        
        // حساب الوقت الفعلي الذي قضاه الموظف في العمل
        const actualWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        
        // إذا كان الوقت الفعلي أقل من عدد ساعات العمل المطلوبة
        if (actualWorkHours < workingHours) {
          // مدة الخروج = عدد ساعات العمل - الوقت الفعلي
          const breakTime = workingHours - actualWorkHours;
          if (breakTime > 0) {
            totalBreakTime += breakTime;
          }
        }
      }
    } else {
      // إذا لم يكن هناك دخول، استخدم المنطق القديم
      const lastCheckOut = new Date(lastMovement.checkOutTime);
      
      // إنشاء وقت انتهاء العمل لنفس التاريخ
      const workEnd = new Date(lastCheckOut);
      const [hours, minutes] = workEndTime.split(':');
      workEnd.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // إذا كان الخروج قبل انتهاء العمل ولم يعد
      if (lastCheckOut < workEnd) {
        const remainingTime = (workEnd.getTime() - lastCheckOut.getTime()) / (1000 * 60 * 60);
        totalBreakTime += remainingTime;
      }
    }
  }

  return totalBreakTime;
}
