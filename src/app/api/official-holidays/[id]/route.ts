import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب إجازة رسمية واحدة
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const resolvedParams = await params;
    const holiday = await prisma.officialHoliday.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!holiday) {
      return NextResponse.json({ error: "الإجازة الرسمية غير موجودة" }, { status: 404 });
    }

    return NextResponse.json(holiday);
  } catch (error) {
    console.error("خطأ في جلب الإجازة الرسمية:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإجازة الرسمية" },
      { status: 500 }
    );
  }
}

// PUT - تعديل إجازة رسمية
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const body = await request.json();
    const { name, startDate, endDate } = body;

    // التحقق من وجود الإجازة الرسمية
    const existingHoliday = await prisma.officialHoliday.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!existingHoliday) {
      return NextResponse.json({ error: "الإجازة الرسمية غير موجودة" }, { status: 404 });
    }

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم الإجازة مطلوب" },
        { status: 400 }
      );
    }

    if (!startDate) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة مطلوب" },
        { status: 400 }
      );
    }

    // إذا لم يتم تحديد تاريخ النهاية، استخدم تاريخ البداية (إجازة يوم واحد)
    const finalEndDate = endDate || startDate;

    // التحقق من صحة التواريخ
    const holidayStartDate = new Date(startDate);
    const holidayEndDate = new Date(finalEndDate);

    if (isNaN(holidayStartDate.getTime())) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة غير صحيح" },
        { status: 400 }
      );
    }

    if (isNaN(holidayEndDate.getTime())) {
      return NextResponse.json(
        { error: "تاريخ نهاية الإجازة غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من أن تاريخ البداية قبل أو يساوي تاريخ النهاية
    if (holidayStartDate > holidayEndDate) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة يجب أن يكون قبل أو يساوي تاريخ النهاية" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود تداخل مع إجازات أخرى (باستثناء الإجازة الحالية)
    const conflictingHoliday = await prisma.officialHoliday.findFirst({
      where: {
        id: { not: resolvedParams.id },
        OR: [
          // الإجازة المحدثة تبدأ أثناء إجازة موجودة
          {
            AND: [
              { startDate: { lte: holidayStartDate } },
              { endDate: { gte: holidayStartDate } }
            ]
          },
          // الإجازة المحدثة تنتهي أثناء إجازة موجودة
          {
            AND: [
              { startDate: { lte: holidayEndDate } },
              { endDate: { gte: holidayEndDate } }
            ]
          },
          // الإجازة المحدثة تحتوي على إجازة موجودة
          {
            AND: [
              { startDate: { gte: holidayStartDate } },
              { endDate: { lte: holidayEndDate } }
            ]
          }
        ]
      },
    });

    if (conflictingHoliday) {
      return NextResponse.json(
        { error: "يوجد تداخل مع إجازة رسمية أخرى في هذه الفترة" },
        { status: 400 }
      );
    }

    // تحديث الإجازة الرسمية
    const updatedHoliday = await prisma.officialHoliday.update({
      where: { id: resolvedParams.id },
      data: {
        name: name.trim(),
        startDate: holidayStartDate,
        endDate: holidayEndDate,
      },
    });

    return NextResponse.json(updatedHoliday);
  } catch (error) {
    console.error("خطأ في تعديل الإجازة الرسمية:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تعديل الإجازة الرسمية" },
      { status: 500 }
    );
  }
}

// DELETE - حذف إجازة رسمية
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    // التحقق من وجود الإجازة الرسمية
    const existingHoliday = await prisma.officialHoliday.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!existingHoliday) {
      return NextResponse.json({ error: "الإجازة الرسمية غير موجودة" }, { status: 404 });
    }

    // حذف الإجازة الرسمية
    await prisma.officialHoliday.delete({
      where: { id: resolvedParams.id },
    });

    return NextResponse.json({ message: "تم حذف الإجازة الرسمية بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف الإجازة الرسمية:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف الإجازة الرسمية" },
      { status: 500 }
    );
  }
}
