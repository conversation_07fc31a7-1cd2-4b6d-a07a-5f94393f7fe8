import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب جميع الإجازات الرسمية
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const holidays = await prisma.officialHoliday.findMany({
      orderBy: {
        startDate: "asc",
      },
    });

    return NextResponse.json(holidays);
  } catch (error) {
    console.error("خطأ في جلب الإجازات الرسمية:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإجازات الرسمية" },
      { status: 500 }
    );
  }
}

// POST - إنشاء إجازة رسمية جديدة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { name, startDate, endDate } = body;

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم الإجازة مطلوب" },
        { status: 400 }
      );
    }

    if (!startDate) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة مطلوب" },
        { status: 400 }
      );
    }

    // إذا لم يتم تحديد تاريخ النهاية، استخدم تاريخ البداية (إجازة يوم واحد)
    const finalEndDate = endDate || startDate;

    // التحقق من صحة التواريخ
    const holidayStartDate = new Date(startDate);
    const holidayEndDate = new Date(finalEndDate);

    if (isNaN(holidayStartDate.getTime())) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة غير صحيح" },
        { status: 400 }
      );
    }

    if (isNaN(holidayEndDate.getTime())) {
      return NextResponse.json(
        { error: "تاريخ نهاية الإجازة غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من أن تاريخ البداية قبل أو يساوي تاريخ النهاية
    if (holidayStartDate > holidayEndDate) {
      return NextResponse.json(
        { error: "تاريخ بداية الإجازة يجب أن يكون قبل أو يساوي تاريخ النهاية" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود تداخل مع إجازات أخرى
    const existingHoliday = await prisma.officialHoliday.findFirst({
      where: {
        OR: [
          // الإجازة الجديدة تبدأ أثناء إجازة موجودة
          {
            AND: [
              { startDate: { lte: holidayStartDate } },
              { endDate: { gte: holidayStartDate } }
            ]
          },
          // الإجازة الجديدة تنتهي أثناء إجازة موجودة
          {
            AND: [
              { startDate: { lte: holidayEndDate } },
              { endDate: { gte: holidayEndDate } }
            ]
          },
          // الإجازة الجديدة تحتوي على إجازة موجودة
          {
            AND: [
              { startDate: { gte: holidayStartDate } },
              { endDate: { lte: holidayEndDate } }
            ]
          }
        ]
      },
    });

    if (existingHoliday) {
      return NextResponse.json(
        { error: "يوجد تداخل مع إجازة رسمية أخرى في هذه الفترة" },
        { status: 400 }
      );
    }

    // إنشاء الإجازة الرسمية الجديدة
    const holiday = await prisma.officialHoliday.create({
      data: {
        name: name.trim(),
        startDate: holidayStartDate,
        endDate: holidayEndDate,
      },
    });

    return NextResponse.json(holiday, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء الإجازة الرسمية:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء الإجازة الرسمية" },
      { status: 500 }
    );
  }
}
