import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { whatsappService } from "@/lib/services/whatsappService";

// POST - اختبار اتصال WhatsApp
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من الصلاحيات - فقط الأدمن يمكنه اختبار الاتصال
    if (session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية لاختبار الاتصال" }, { status: 403 });
    }

    const body = await request.json();
    const { apiUrl, apiSecret, accountKey, testPhone } = body;

    if (!apiUrl || !apiSecret || !accountKey) {
      return NextResponse.json({ error: "جميع بيانات الاتصال مطلوبة" }, { status: 400 });
    }

    // تنظيف رابط API - إزالة /send/whatsapp إذا كان موجوداً
    let cleanApiUrl = apiUrl;

    // إزالة جميع أجزاء /send/whatsapp
    cleanApiUrl = cleanApiUrl.replace(/\/send\/whatsapp.*$/g, '');

    // إزالة الشرطة المائلة في النهاية
    cleanApiUrl = cleanApiUrl.replace(/\/$/, '');

    // التأكد من أن الرابط صحيح
    if (!cleanApiUrl.startsWith('http')) {
      cleanApiUrl = 'https://w.gcccons.org/api';
    }

    // Debug: removed console.log
// تحديث إعدادات الخدمة مؤقتاً للاختبار
    await whatsappService.updateSettings(cleanApiUrl, apiSecret, accountKey, true);

    // اختبار الاتصال
    // Debug: removed console.log
const testResult = await whatsappService.testConnection();

    // Debug: removed console.log
if (!testResult.success) {
      return NextResponse.json({
        success: false,
        error: testResult.error,
        details: testResult.details
      }, { status: 400 });
    }

    // إذا تم توفير رقم هاتف للاختبار، إرسال رسالة تجريبية
    if (testPhone) {
      // Debug: removed console.log
const testMessage = `🎉 رسالة تجريبية من نظام إدارة الحضور

✅ تم اختبار الاتصال مع CloudText بنجاح!

📅 الوقت: ${new Date().toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })} م
🔗 API: ${apiUrl}
👤 الحساب: ${accountKey}

هذه رسالة تجريبية لتأكيد عمل النظام.`;

      const sendResult = await whatsappService.sendMessage({
        to: testPhone,
        message: testMessage
      });

      // Debug: removed console.log
if (!sendResult.success) {
        return NextResponse.json({
          success: false,
          error: `فشل في إرسال الرسالة التجريبية: ${sendResult.error}`,
          details: sendResult.details,
          connectionSuccess: true,
          credits: testResult.data
        }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        message: "تم اختبار الاتصال وإرسال الرسالة التجريبية بنجاح",
        data: {
          credits: testResult.data,
          messageResult: sendResult.data
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: "تم اختبار الاتصال بنجاح",
      data: testResult.data
    });

  } catch (error) {
    console.error("خطأ في اختبار اتصال WhatsApp:", error);
    return NextResponse.json(
      {
        success: false,
        error: "حدث خطأ في اختبار الاتصال"
      },
      { status: 500 }
    );
  }
}
