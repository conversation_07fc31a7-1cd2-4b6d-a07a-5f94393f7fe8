import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// Cache settings for 5 minutes
let settingsCache: any = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// الإعدادات الافتراضية
const DEFAULT_SETTINGS = {
  companyName: "النظام الذكي للحضور والانصراف",
  logo: null,
  favicon: null,
  primaryColor: "#1e40af",
  secondaryColor: "#3b82f6",
  accentColor: "#10b981",
  timezone: "Asia/Muscat",
  workStartTime: "07:30",
  workEndTime: "14:30",
};

// GET - جلب الإعدادات العامة (بدون صلاحيات)
export async function GET() {
  try {
    const now = Date.now();
    
    // Check if we have valid cached data
    if (settingsCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return NextResponse.json(settingsCache, {
        headers: {
          'Cache-Control': 'public, max-age=300, stale-while-revalidate=60',
          'ETag': `"settings-${cacheTimestamp}"`,
        },
      });
    }

    // Fetch fresh data from database
    const settings = await prisma.settings.findFirst({
      select: {
        companyName: true,
        logo: true,
        favicon: true,
        primaryColor: true,
        secondaryColor: true,
        accentColor: true,
        timezone: true,
        workStartTime: true,
        workEndTime: true,
      }
    });

    const result = {
      companyName: settings?.companyName || DEFAULT_SETTINGS.companyName,
      logo: settings?.logo,
      favicon: settings?.favicon,
      primaryColor: settings?.primaryColor || DEFAULT_SETTINGS.primaryColor,
      secondaryColor: settings?.secondaryColor || DEFAULT_SETTINGS.secondaryColor,
      accentColor: settings?.accentColor || DEFAULT_SETTINGS.accentColor,
      timezone: settings?.timezone || DEFAULT_SETTINGS.timezone,
      workStartTime: settings?.workStartTime || DEFAULT_SETTINGS.workStartTime,
      workEndTime: settings?.workEndTime || DEFAULT_SETTINGS.workEndTime,
    };

    // Update cache
    settingsCache = result;
    cacheTimestamp = now;

    return NextResponse.json(result, {
      headers: {
        'Cache-Control': 'public, max-age=300, stale-while-revalidate=60',
        'ETag': `"settings-${cacheTimestamp}"`,
      },
    });
  } catch (error) {
    console.error("خطأ في جلب الإعدادات العامة:", error);
    
    // Return cached data if available, even if stale
    if (settingsCache) {
      return NextResponse.json(settingsCache, {
        headers: {
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=30',
        },
      });
    }
    
    // Fallback to default settings
    return NextResponse.json(DEFAULT_SETTINGS, {
      headers: {
        'Cache-Control': 'public, max-age=60',
      },
    });
  }
}
