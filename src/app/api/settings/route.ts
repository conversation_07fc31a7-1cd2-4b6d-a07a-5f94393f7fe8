import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب الإعدادات
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من الصلاحيات - فقط الأدمن يمكنه الوصول للإعدادات
    if (session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول إلى الإعدادات" }, { status: 403 });
    }

    // جلب الإعدادات من قاعدة البيانات
    const settings = await prisma.settings.findFirst();

    // إذا لم توجد إعدادات، إنشاء إعدادات افتراضية
    if (!settings) {
      const defaultSettings = await prisma.settings.create({
        data: {
          companyName: "نظام إدارة الحضور",
          workStartTime: "07:30",
          workEndTime: "14:30",
          workHoursRequired: 4,
          workingHours: 7,
          timezone: "Asia/Muscat",
          whatsappApiUrl: "https://w.gcccons.org/api",
          whatsappEnabled: false,
          otpTemplate: "رمز التحقق الخاص بك هو: {otp}",
          earlyExitTemplate: "الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.",
          visitorArrivalTemplate: "وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}",
          permitApprovalTemplate: "تم الموافقة على طلبك. التفاصيل: {details}",
        },
      });
      return NextResponse.json(defaultSettings);
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("خطأ في جلب الإعدادات:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإعدادات" },
      { status: 500 }
    );
  }
}

// PUT - تحديث الإعدادات
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من الصلاحيات - فقط الأدمن يمكنه تعديل الإعدادات
    if (session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية لتعديل الإعدادات" }, { status: 403 });
    }

    const body = await request.json();
    const {
      companyName,
      logo,
      favicon,
      primaryColor,
      secondaryColor,
      accentColor,
      workStartTime,
      workEndTime,
      workHoursRequired,
      workingHours,
      timezone,
      whatsappApiUrl,
      whatsappApiSecret,
      whatsappAccountKey,
      whatsappEnabled,
      otpTemplate,
      earlyExitTemplate,
      visitorArrivalTemplate,
      permitApprovalTemplate,
    } = body;

    // تنظيف رابط WhatsApp API تلقائياً
    let cleanWhatsappApiUrl = whatsappApiUrl;
    if (cleanWhatsappApiUrl) {
      // إزالة جميع أجزاء /send/whatsapp
      cleanWhatsappApiUrl = cleanWhatsappApiUrl.replace(/\/send\/whatsapp.*$/g, '');

      // إزالة الشرطة المائلة في النهاية
      cleanWhatsappApiUrl = cleanWhatsappApiUrl.replace(/\/$/, '');

      // التأكد من أن الرابط صحيح
      if (!cleanWhatsappApiUrl.startsWith('http')) {
        cleanWhatsappApiUrl = 'https://w.gcccons.org/api';
      }
    }

    // التحقق من البيانات المطلوبة
    if (!companyName || !workStartTime || !workEndTime) {
      return NextResponse.json(
        { error: "جميع الحقول المطلوبة يجب تعبئتها" },
        { status: 400 }
      );
    }

    // التحقق من صحة ساعات العمل
    if (workHoursRequired < 1 || workHoursRequired > 12) {
      return NextResponse.json(
        { error: "الحد الأدنى لساعات العمل يجب أن يكون بين 1 و 12 ساعة" },
        { status: 400 }
      );
    }

    // التحقق من صحة عدد ساعات العمل (إذا تم توفيره)
    if (workingHours && (workingHours < 1 || workingHours > 12)) {
      return NextResponse.json(
        { error: "عدد ساعات العمل يجب أن يكون بين 1 و 12 ساعة" },
        { status: 400 }
      );
    }

    // البحث عن الإعدادات الحالية
    const existingSettings = await prisma.settings.findFirst();

    let updatedSettings;

    if (existingSettings) {
      // تحديث الإعدادات الموجودة
      updatedSettings = await prisma.settings.update({
        where: { id: existingSettings.id },
        data: {
          companyName: companyName.trim(),
          logo: logo || null,
          favicon: favicon || null,
          primaryColor: primaryColor || "#1e40af",
          secondaryColor: secondaryColor || "#3b82f6",
          accentColor: accentColor || "#10b981",
          workStartTime,
          workEndTime,
          workHoursRequired,
          ...(workingHours !== undefined && { workingHours }),
          timezone,
          whatsappApiUrl: cleanWhatsappApiUrl || "https://w.gcccons.org/api",
          whatsappApiSecret: whatsappApiSecret || null,
          whatsappAccountKey: whatsappAccountKey || null,
          whatsappEnabled: whatsappEnabled || false,
          otpTemplate: otpTemplate || "رمز التحقق الخاص بك هو: {otp}",
          earlyExitTemplate: earlyExitTemplate || "الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.",
          visitorArrivalTemplate: visitorArrivalTemplate || "وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}",
          permitApprovalTemplate: permitApprovalTemplate || "تم الموافقة على طلبك. التفاصيل: {details}",
          updatedAt: new Date(),
        },
      });
    } else {
      // إنشاء إعدادات جديدة
      updatedSettings = await prisma.settings.create({
        data: {
          companyName: companyName.trim(),
          logo: logo || null,
          favicon: favicon || null,
          primaryColor: primaryColor || "#1e40af",
          secondaryColor: secondaryColor || "#3b82f6",
          accentColor: accentColor || "#10b981",
          workStartTime,
          workEndTime,
          workHoursRequired,
          ...(workingHours !== undefined && { workingHours }),
          timezone,
          whatsappApiUrl: cleanWhatsappApiUrl || "https://w.gcccons.org/api",
          whatsappApiSecret: whatsappApiSecret || null,
          whatsappAccountKey: whatsappAccountKey || null,
          whatsappEnabled: whatsappEnabled || false,
          otpTemplate: otpTemplate || "رمز التحقق الخاص بك هو: {otp}",
          earlyExitTemplate: earlyExitTemplate || "الموظف {employeeName} غادر العمل في {exitTime} قبل انتهاء الدوام الرسمي.",
          visitorArrivalTemplate: visitorArrivalTemplate || "وصل الزائر {visitorName} إلى البوابة. الغرض من الزيارة: {purpose}",
          permitApprovalTemplate: permitApprovalTemplate || "تم الموافقة على طلبك. التفاصيل: {details}",
        },
      });
    }

    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error("خطأ في تحديث الإعدادات:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث الإعدادات" },
      { status: 500 }
    );
  }
}
