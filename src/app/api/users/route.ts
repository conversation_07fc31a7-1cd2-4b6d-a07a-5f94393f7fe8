import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

// GET - جلب جميع المستخدمين
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error("خطأ في جلب المستخدمين:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب المستخدمين" },
      { status: 500 }
    );
  }
}

// POST - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { 
      employeeNumber, 
      name, 
      email, 
      phone, 
      position, 
      password, 
      role, 
      departmentId, 
      managerId 
    } = body;

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم المستخدم مطلوب" },
        { status: 400 }
      );
    }

    if (!email || email.trim() === "") {
      return NextResponse.json(
        { error: "البريد الإلكتروني مطلوب" },
        { status: 400 }
      );
    }

    if (!password || password.trim() === "") {
      return NextResponse.json(
        { error: "كلمة المرور مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return NextResponse.json(
        { error: "البريد الإلكتروني غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email: email.trim().toLowerCase() },
    });

    if (existingUserByEmail) {
      return NextResponse.json(
        { error: "البريد الإلكتروني مستخدم مسبقاً" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود رقم الموظف مسبقاً (إذا تم توفيره)
    if (employeeNumber && employeeNumber.trim() !== "") {
      const existingUserByEmployeeNumber = await prisma.user.findUnique({
        where: { employeeNumber: employeeNumber.trim() },
      });

      if (existingUserByEmployeeNumber) {
        return NextResponse.json(
          { error: "رقم الموظف مستخدم مسبقاً" },
          { status: 400 }
        );
      }
    }

    // التحقق من صحة الدور
    const validRoles = ["ADMIN", "HR", "MANAGER", "EMPLOYEE", "SECURITY"];
    if (role && !validRoles.includes(role)) {
      return NextResponse.json(
        { error: "الدور المحدد غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم (إذا تم توفيره)
    if (departmentId) {
      const department = await prisma.department.findUnique({
        where: { id: departmentId },
      });

      if (!department) {
        return NextResponse.json(
          { error: "القسم المحدد غير موجود" },
          { status: 400 }
        );
      }
    }

    // التحقق من وجود المدير (إذا تم توفيره)
    if (managerId) {
      const manager = await prisma.user.findUnique({
        where: { id: managerId },
      });

      if (!manager) {
        return NextResponse.json(
          { error: "المدير المحدد غير موجود" },
          { status: 400 }
        );
      }
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password.trim(), 10);

    // إنشاء المستخدم الجديد
    const user = await prisma.user.create({
      data: {
        employeeNumber: employeeNumber && employeeNumber.trim() !== "" ? employeeNumber.trim() : null,
        name: name.trim(),
        email: email.trim().toLowerCase(),
        phone: phone?.trim() || "",
        position: position && position.trim() !== "" ? position.trim() : null,
        password: hashedPassword,
        role: role || "EMPLOYEE",
        departmentId: departmentId || null,
        managerId: managerId || null,
      },
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء المستخدم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء المستخدم" },
      { status: 500 }
    );
  }
}
