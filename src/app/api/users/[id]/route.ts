import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

// GET - جلب مستخدم واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const user = await prisma.user.findUnique({
      where: { id: resolvedParams.id },
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        employees: {
          select: {
            id: true,
            name: true,
            employeeNumber: true,
            position: true,
          },
        },
        _count: {
          select: {
            employees: true,
            attendanceRecords: true,
            leaveRequests: true,
            visitorRequests: true,
            afterHoursPermits: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "المستخدم غير موجود" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("خطأ في جلب المستخدم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب المستخدم" },
      { status: 500 }
    );
  }
}

// PUT - تعديل مستخدم
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const body = await request.json();
    const { 
      employeeNumber, 
      name, 
      email, 
      phone, 
      position, 
      role, 
      departmentId, 
      managerId,
      password 
    } = body;

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "المستخدم غير موجود" }, { status: 404 });
    }

    // منع تعديل الأدمن الرئيسي
    if (existingUser.role === "ADMIN" && existingUser.email === "<EMAIL>") {
      return NextResponse.json({ error: "لا يمكن تعديل المدير الرئيسي" }, { status: 403 });
    }

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم المستخدم مطلوب" },
        { status: 400 }
      );
    }

    if (!email || email.trim() === "") {
      return NextResponse.json(
        { error: "البريد الإلكتروني مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return NextResponse.json(
        { error: "البريد الإلكتروني غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود البريد الإلكتروني مسبقاً (باستثناء المستخدم الحالي)
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email: email.trim().toLowerCase() },
    });

    if (existingUserByEmail && existingUserByEmail.id !== resolvedParams.id) {
      return NextResponse.json(
        { error: "البريد الإلكتروني مستخدم مسبقاً" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود رقم الموظف مسبقاً (إذا تم توفيره)
    if (employeeNumber && employeeNumber.trim() !== "") {
      const existingUserByEmployeeNumber = await prisma.user.findUnique({
        where: { employeeNumber: employeeNumber.trim() },
      });

      if (existingUserByEmployeeNumber && existingUserByEmployeeNumber.id !== resolvedParams.id) {
        return NextResponse.json(
          { error: "رقم الموظف مستخدم مسبقاً" },
          { status: 400 }
        );
      }
    }

    // التحقق من صحة الدور
    const validRoles = ["ADMIN", "HR", "MANAGER", "EMPLOYEE", "SECURITY"];
    if (role && !validRoles.includes(role)) {
      return NextResponse.json(
        { error: "الدور المحدد غير صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم (إذا تم توفيره)
    if (departmentId) {
      const department = await prisma.department.findUnique({
        where: { id: departmentId },
      });

      if (!department) {
        return NextResponse.json(
          { error: "القسم المحدد غير موجود" },
          { status: 400 }
        );
      }
    }

    // التحقق من وجود المدير (إذا تم توفيره)
    if (managerId) {
      const manager = await prisma.user.findUnique({
        where: { id: managerId },
      });

      if (!manager) {
        return NextResponse.json(
          { error: "المدير المحدد غير موجود" },
          { status: 400 }
        );
      }

      // منع المستخدم من أن يكون مديراً لنفسه
      if (managerId === resolvedParams.id) {
        return NextResponse.json(
          { error: "لا يمكن للمستخدم أن يكون مديراً لنفسه" },
          { status: 400 }
        );
      }
    }

    // تحضير بيانات التحديث
    const updateData: any = {
      employeeNumber: employeeNumber && employeeNumber.trim() !== "" ? employeeNumber.trim() : null,
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone?.trim() || "",
      position: position && position.trim() !== "" ? position.trim() : null,
      role: role || existingUser.role,
      departmentId: departmentId || null,
      managerId: managerId || null,
    };

    // إضافة كلمة المرور المشفرة إذا تم توفيرها
    if (password && password.trim() !== "") {
      if (password.length < 6) {
        return NextResponse.json(
          { error: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" },
          { status: 400 }
        );
      }
      updateData.password = await bcrypt.hash(password, 12);
    }

    // تحديث المستخدم
    const updatedUser = await prisma.user.update({
      where: { id: resolvedParams.id },
      data: updateData,
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("خطأ في تعديل المستخدم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تعديل المستخدم" },
      { status: 500 }
    );
  }
}

// DELETE - حذف مستخدم
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: resolvedParams.id },
      include: {
        _count: {
          select: {
            employees: true,
            attendanceRecords: true,
            leaveRequests: true,
            visitorRequests: true,
            afterHoursPermits: true,
          },
        },
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "المستخدم غير موجود" }, { status: 404 });
    }

    // منع حذف الأدمن الرئيسي
    if (existingUser.role === "ADMIN" && existingUser.email === "<EMAIL>") {
      return NextResponse.json({ error: "لا يمكن حذف المدير الرئيسي" }, { status: 403 });
    }

    // منع حذف المستخدم إذا كان له مرؤوسين
    if (existingUser._count.employees > 0) {
      return NextResponse.json(
        { error: "لا يمكن حذف المستخدم لأن له مرؤوسين. يرجى نقل المرؤوسين أولاً." },
        { status: 400 }
      );
    }

    // حذف المستخدم
    await prisma.user.delete({
      where: { id: resolvedParams.id },
    });

    return NextResponse.json({ message: "تم حذف المستخدم بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف المستخدم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف المستخدم" },
      { status: 500 }
    );
  }
}
