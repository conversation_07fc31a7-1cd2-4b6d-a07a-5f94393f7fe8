import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

// POST - إعادة تعيين كلمة المرور
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: resolvedParams.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "المستخدم غير موجود" }, { status: 404 });
    }

    // منع إعادة تعيين كلمة مرور الأدمن الرئيسي
    if (existingUser.role === "ADMIN" && existingUser.email === "<EMAIL>") {
      return NextResponse.json({ error: "لا يمكن إعادة تعيين كلمة مرور المدير الرئيسي" }, { status: 403 });
    }

    // توليد كلمة مرور جديدة
    const newPassword = generateRandomPassword();
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // تحديث كلمة المرور في قاعدة البيانات
    await prisma.user.update({
      where: { id: resolvedParams.id },
      data: {
        password: hashedPassword,
      },
    });

    // في التطبيق الحقيقي، يجب إرسال كلمة المرور الجديدة عبر البريد الإلكتروني
    // هنا سنعيدها في الاستجابة لأغراض التطوير فقط
    return NextResponse.json({
      message: "تم إعادة تعيين كلمة المرور بنجاح",
      newPassword: newPassword, // في الإنتاج، يجب إرسالها عبر البريد الإلكتروني
      user: {
        id: existingUser.id,
        name: existingUser.name,
        email: existingUser.email,
      },
    });
  } catch (error) {
    console.error("خطأ في إعادة تعيين كلمة المرور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إعادة تعيين كلمة المرور" },
      { status: 500 }
    );
  }
}

// دالة لتوليد كلمة مرور عشوائية
function generateRandomPassword(): string {
  const length = 8;
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  
  // ضمان وجود حرف كبير واحد على الأقل
  password += charset.charAt(Math.floor(Math.random() * 26) + 26);
  
  // ضمان وجود حرف صغير واحد على الأقل
  password += charset.charAt(Math.floor(Math.random() * 26));
  
  // ضمان وجود رقم واحد على الأقل
  password += charset.charAt(Math.floor(Math.random() * 10) + 52);
  
  // ضمان وجود رمز خاص واحد على الأقل
  password += charset.charAt(Math.floor(Math.random() * 8) + 62);
  
  // إكمال باقي الأحرف عشوائياً
  for (let i = password.length; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  // خلط الأحرف
  return password.split('').sort(() => Math.random() - 0.5).join('');
}
