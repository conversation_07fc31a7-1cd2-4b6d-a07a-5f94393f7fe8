import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب جميع تصاريح العمل بعد الدوام
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    const userId = session.user.id;

    let whereClause = {};

    // إذا كان المستخدم موظف عادي، يرى طلباته فقط
    if (userRole === "EMPLOYEE") {
      whereClause = { userId };
    }
    // إذا كان المستخدم مدير، يرى طلباته وطلبات الموظفين التابعين له
    else if (userRole === "MANAGER") {
      whereClause = {
        OR: [
          { userId }, // طلبات المدير نفسه
          {
            user: {
              managerId: userId, // طلبات الموظفين التابعين لهذا المدير
            },
          },
        ],
      };
    }
    // HR والأدمن والأمن يرون جميع الطلبات

    const permits = await prisma.afterHoursPermit.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(permits);
  } catch (error) {
    console.error("خطأ في جلب تصاريح العمل بعد الدوام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب تصاريح العمل بعد الدوام" },
      { status: 500 }
    );
  }
}

// POST - إنشاء تصريح عمل بعد الدوام جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { date, startTime, endTime, reason } = body;

    // التحقق من البيانات المطلوبة
    if (!date || !startTime || !endTime || !reason) {
      return NextResponse.json(
        { error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من صحة التاريخ
    const workDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (workDate < today) {
      return NextResponse.json(
        { error: "لا يمكن أن يكون تاريخ العمل في الماضي" },
        { status: 400 }
      );
    }

    // التحقق من صحة الأوقات
    const start = new Date(`${date}T${startTime}`);
    const end = new Date(`${date}T${endTime}`);

    if (end <= start) {
      return NextResponse.json(
        { error: "وقت النهاية يجب أن يكون بعد وقت البداية" },
        { status: 400 }
      );
    }

    // التحقق من عدم تداخل التصاريح
    const overlappingPermit = await prisma.afterHoursPermit.findFirst({
      where: {
        userId: session.user.id,
        date: workDate,
        status: {
          in: ["PENDING", "APPROVED"],
        },
        OR: [
          {
            AND: [
              { startTime: { lte: start } },
              { endTime: { gte: start } },
            ],
          },
          {
            AND: [
              { startTime: { lte: end } },
              { endTime: { gte: end } },
            ],
          },
          {
            AND: [
              { startTime: { gte: start } },
              { endTime: { lte: end } },
            ],
          },
        ],
      },
    });

    if (overlappingPermit) {
      return NextResponse.json(
        { error: "يوجد تصريح متداخل مع هذه الأوقات في نفس التاريخ" },
        { status: 400 }
      );
    }

    // إنشاء تصريح العمل بعد الدوام
    const permit = await prisma.afterHoursPermit.create({
      data: {
        userId: session.user.id,
        date: workDate,
        startTime: start,
        endTime: end,
        reason: reason.trim(),
        status: "PENDING",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(permit, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء تصريح العمل بعد الدوام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء تصريح العمل بعد الدوام" },
      { status: 500 }
    );
  }
}
