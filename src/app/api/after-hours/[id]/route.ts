import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { whatsappService } from "@/lib/services/whatsappService";

// GET - جلب تصريح عمل بعد الدوام واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const { id } = await params;
    const permit = await prisma.afterHoursPermit.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!permit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const userId = session.user.id;

    if (userRole === "EMPLOYEE" && permit.userId !== userId) {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    return NextResponse.json(permit);
  } catch (error) {
    console.error("خطأ في جلب التصريح:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب التصريح" },
      { status: 500 }
    );
  }
}

// PATCH - تحديث حالة تصريح العمل بعد الدوام
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;

    // فقط الموارد البشرية والمدراء والأدمن والأمن يمكنهم تحديث الحالة
    if (userRole !== "HR" && userRole !== "ADMIN" && userRole !== "MANAGER" && userRole !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية لتحديث حالة التصريح" }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !["APPROVED", "REJECTED"].includes(status)) {
      return NextResponse.json({ error: "حالة غير صحيحة" }, { status: 400 });
    }

    const { id } = await params;

    // التحقق من وجود التصريح
    const existingPermit = await prisma.afterHoursPermit.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!existingPermit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    if (existingPermit.status !== "PENDING") {
      return NextResponse.json({ error: "لا يمكن تحديث حالة التصريح" }, { status: 400 });
    }

    // تحديث حالة التصريح
    const updatedPermit = await prisma.afterHoursPermit.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
            phone: true,
          },
        },
      },
    });

    // إرسال إشعار في حالة الموافقة على التصريح
    if (status === "APPROVED") {
      try {
        const settings = await prisma.settings.findFirst();
        if (settings?.whatsappEnabled && updatedPermit.user.phone) {
          const permitDetails = `تصريح العمل بعد الدوام في ${new Date(updatedPermit.date).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })} م من ${new Date(updatedPermit.startTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })} إلى ${new Date(updatedPermit.endTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}`;

          await whatsappService.sendPermitApprovalNotification(
            updatedPermit.user.phone,
            permitDetails
          );

          // Debug: removed console.log
}
      } catch (notificationError) {
        console.error("خطأ في إرسال إشعار الموافقة على التصريح:", notificationError);
      }
    }

    return NextResponse.json(updatedPermit);
  } catch (error) {
    console.error("خطأ في تحديث حالة التصريح:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث حالة التصريح" },
      { status: 500 }
    );
  }
}

// PUT - تحديث تصريح العمل بعد الدوام
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { date, startTime, endTime, reason } = body;

    // التحقق من البيانات المطلوبة
    if (!date || !startTime || !endTime || !reason) {
      return NextResponse.json(
        { error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }

    const { id } = await params;

    // التحقق من وجود التصريح
    const existingPermit = await prisma.afterHoursPermit.findUnique({
      where: { id },
    });

    if (!existingPermit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const userId = session.user.id;

    if (userRole === "EMPLOYEE" && existingPermit.userId !== userId) {
      return NextResponse.json({ error: "ليس لديك صلاحية لتعديل هذا التصريح" }, { status: 403 });
    }

    if (existingPermit.status !== "PENDING") {
      return NextResponse.json({ error: "لا يمكن تعديل التصريح بعد الموافقة أو الرفض" }, { status: 400 });
    }

    // التحقق من صحة التاريخ
    const workDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (workDate < today) {
      return NextResponse.json(
        { error: "لا يمكن أن يكون تاريخ العمل في الماضي" },
        { status: 400 }
      );
    }

    // التحقق من صحة الأوقات
    const start = new Date(`${date}T${startTime}`);
    const end = new Date(`${date}T${endTime}`);

    if (end <= start) {
      return NextResponse.json(
        { error: "وقت النهاية يجب أن يكون بعد وقت البداية" },
        { status: 400 }
      );
    }

    // تحديث التصريح
    const updatedPermit = await prisma.afterHoursPermit.update({
      where: { id },
      data: {
        date: workDate,
        startTime: start,
        endTime: end,
        reason: reason.trim(),
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(updatedPermit);
  } catch (error) {
    console.error("خطأ في تحديث التصريح:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث التصريح" },
      { status: 500 }
    );
  }
}

// DELETE - حذف تصريح العمل بعد الدوام
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const { id } = await params;

    // التحقق من وجود التصريح
    const existingPermit = await prisma.afterHoursPermit.findUnique({
      where: { id },
    });

    if (!existingPermit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const userId = session.user.id;

    if (userRole === "EMPLOYEE" && existingPermit.userId !== userId) {
      return NextResponse.json({ error: "ليس لديك صلاحية لحذف هذا التصريح" }, { status: 403 });
    }

    if (existingPermit.status === "APPROVED" && userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "لا يمكن حذف التصريح المعتمد" }, { status: 400 });
    }

    // حذف التصريح
    await prisma.afterHoursPermit.delete({
      where: { id },
    });

    return NextResponse.json({ message: "تم حذف التصريح بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف التصريح:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف التصريح" },
      { status: 500 }
    );
  }
}
