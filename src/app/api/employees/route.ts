import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// GET - جلب جميع الموظفين
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR" && userRole !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const employees = await prisma.user.findMany({
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(employees);
  } catch (error) {
    console.error("خطأ في جلب الموظفين:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الموظفين" },
      { status: 500 }
    );
  }
}

// POST - إنشاء موظف جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { employeeNumber, name, email, phone, position, password, role, departmentId, managerId } = body;

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم الموظف مطلوب" },
        { status: 400 }
      );
    }

    if (!email || email.trim() === "") {
      return NextResponse.json(
        { error: "البريد الإلكتروني مطلوب" },
        { status: 400 }
      );
    }

    if (!phone || phone.trim() === "") {
      return NextResponse.json(
        { error: "رقم الهاتف مطلوب" },
        { status: 400 }
      );
    }

    if (!password || password.length < 6) {
      return NextResponse.json(
        { error: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "البريد الإلكتروني غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    const existingUser = await prisma.user.findUnique({
      where: { email: email.trim().toLowerCase() },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "البريد الإلكتروني موجود بالفعل" },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار الرقم الوظيفي إذا تم توفيره
    if (employeeNumber && employeeNumber.trim() !== "") {
      const existingEmployee = await prisma.user.findUnique({
        where: { employeeNumber: employeeNumber.trim() },
      });

      if (existingEmployee) {
        return NextResponse.json(
          { error: "الرقم الوظيفي موجود بالفعل" },
          { status: 400 }
        );
      }
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);

    // إنشاء الموظف الجديد
    const employee = await prisma.user.create({
      data: {
        employeeNumber: employeeNumber && employeeNumber.trim() !== "" ? employeeNumber.trim() : null,
        name: name.trim(),
        email: email.trim().toLowerCase(),
        phone: phone.trim(),
        position: position && position.trim() !== "" ? position.trim() : null,
        password: hashedPassword,
        role: role || "EMPLOYEE",
        departmentId: departmentId || null,
        managerId: managerId || null,
      },
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
    });

    return NextResponse.json(employee, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء الموظف" },
      { status: 500 }
    );
  }
}
