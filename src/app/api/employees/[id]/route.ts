import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// GET - جلب موظف واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;
    const employee = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        employees: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
    });

    if (!employee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    return NextResponse.json(employee);
  } catch (error) {
    console.error("خطأ في جلب الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الموظف" },
      { status: 500 }
    );
  }
}

// PUT - تعديل موظف
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { employeeNumber, name, email, phone, position, password, role, departmentId, managerId } = body;

    // التحقق من البيانات المطلوبة
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "اسم الموظف مطلوب" },
        { status: 400 }
      );
    }

    if (!email || email.trim() === "") {
      return NextResponse.json(
        { error: "البريد الإلكتروني مطلوب" },
        { status: 400 }
      );
    }

    if (!phone || phone.trim() === "") {
      return NextResponse.json(
        { error: "رقم الهاتف مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "البريد الإلكتروني غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الموظف
    const existingEmployee = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingEmployee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    // التحقق من عدم تكرار البريد الإلكتروني (باستثناء الموظف الحالي)
    const duplicateUser = await prisma.user.findFirst({
      where: {
        email: email.trim().toLowerCase(),
        id: { not: id },
      },
    });

    if (duplicateUser) {
      return NextResponse.json(
        { error: "البريد الإلكتروني موجود بالفعل" },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار الرقم الوظيفي إذا تم توفيره (باستثناء الموظف الحالي)
    if (employeeNumber && employeeNumber.trim() !== "") {
      const duplicateEmployee = await prisma.user.findFirst({
        where: {
          employeeNumber: employeeNumber.trim(),
          id: { not: id },
        },
      });

      if (duplicateEmployee) {
        return NextResponse.json(
          { error: "الرقم الوظيفي موجود بالفعل" },
          { status: 400 }
        );
      }
    }

    // إعداد البيانات للتحديث
    const updateData: any = {
      employeeNumber: employeeNumber && employeeNumber.trim() !== "" ? employeeNumber.trim() : null,
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone.trim(),
      position: position && position.trim() !== "" ? position.trim() : null,
      role: role || existingEmployee.role,
      departmentId: departmentId || null,
      managerId: managerId || null,
    };

    // إضافة كلمة المرور إذا تم توفيرها
    if (password && password.trim() !== "") {
      if (password.length < 6) {
        return NextResponse.json(
          { error: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" },
          { status: 400 }
        );
      }
      updateData.password = await bcrypt.hash(password, 10);
    }

    const employee = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        email: true,
        phone: true,
        position: true,
        role: true,
        departmentId: true,
        managerId: true,
        createdAt: true,
        updatedAt: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
    });

    return NextResponse.json(employee);
  } catch (error) {
    console.error("خطأ في تعديل الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تعديل الموظف" },
      { status: 500 }
    );
  }
}

// DELETE - حذف موظف
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { id } = await params;

    // التحقق من وجود الموظف
    const employee = await prisma.user.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            employees: true,
            attendanceRecords: true,
            leaveRequests: true,
            visitorRequests: true,
            afterHoursPermits: true,
            headOfDepartment: true,
          },
        },
      },
    });

    if (!employee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    // منع حذف المدير إذا كان لديه موظفين
    if (employee._count.employees > 0) {
      return NextResponse.json(
        { error: "لا يمكن حذف الموظف لأنه مدير لموظفين آخرين" },
        { status: 400 }
      );
    }

    // منع حذف رئيس القسم إذا كان رئيساً لأي قسم
    if (employee._count.headOfDepartment > 0) {
      return NextResponse.json(
        { error: "لا يمكن حذف الموظف لأنه رئيس لقسم. يرجى تعيين رئيس قسم آخر أولاً" },
        { status: 400 }
      );
    }

    // حذف جميع السجلات المرتبطة
    await prisma.$transaction([
      // حذف سجلات الحضور
      prisma.attendanceRecord.deleteMany({
        where: { userId: id },
      }),
      // حذف طلبات الإجازة
      prisma.leaveRequest.deleteMany({
        where: { userId: id },
      }),
      // حذف طلبات الزوار
      prisma.visitorRequest.deleteMany({
        where: { userId: id },
      }),
      // حذف تصاريح العمل بعد الدوام
      prisma.afterHoursPermit.deleteMany({
        where: { userId: id },
      }),
      // حذف الموظف
      prisma.user.delete({
        where: { id },
      }),
    ]);

    return NextResponse.json({ message: "تم حذف الموظف بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف الموظف" },
      { status: 500 }
    );
  }
}
