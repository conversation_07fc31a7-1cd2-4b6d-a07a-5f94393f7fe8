import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب جميع طلبات الزوار
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    const userId = session.user.id;

    let whereClause = {};

    // إذا كان المستخدم موظف عادي، يرى طلباته فقط
    if (userRole === "EMPLOYEE") {
      whereClause = { userId };
    }
    // إذا كان المستخدم مدير، يرى طلباته وطلبات الموظفين التابعين له
    else if (userRole === "MANAGER") {
      whereClause = {
        OR: [
          { userId }, // طلبات المدير نفسه
          {
            user: {
              managerId: userId, // طلبات الموظفين التابعين لهذا المدير
            },
          },
        ],
      };
    }
    // HR والأدمن والأمن يرون جميع الطلبات

    const visitors = await prisma.visitorRequest.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(visitors);
  } catch (error) {
    console.error("خطأ في جلب طلبات الزوار:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب طلبات الزوار" },
      { status: 500 }
    );
  }
}

// POST - إنشاء طلب زائر جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { visitorName, visitorCompany, purpose, visitDate, visitTime, companions, vehicleInfo } = body;

    // التحقق من البيانات المطلوبة
    if (!visitorName || !purpose || !visitDate) {
      return NextResponse.json(
        { error: "اسم الزائر والغرض وتاريخ الزيارة مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من صحة التاريخ
    const visit = new Date(visitDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (visit < today) {
      return NextResponse.json(
        { error: "لا يمكن أن يكون تاريخ الزيارة في الماضي" },
        { status: 400 }
      );
    }

    // إنشاء طلب الزائر
    const visitor = await prisma.visitorRequest.create({
      data: {
        userId: session.user.id,
        visitorName: visitorName.trim(),
        visitorCompany: visitorCompany?.trim() || null,
        purpose: purpose.trim(),
        visitDate: visit,
        visitTime: visitTime?.trim() || null,
        companions: companions?.trim() || null,
        vehicleInfo: vehicleInfo?.trim() || null,
        status: "PENDING",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(visitor, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء طلب الزائر:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء طلب الزائر" },
      { status: 500 }
    );
  }
}
