import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userId = session.user.id;
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    
    // بداية ونهاية الشهر الحالي
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // جلب إعدادات النظام
    const settings = await prisma.settings.findFirst();
    const workStartTime = settings?.workStartTime || '08:30';
    const minWorkingHours = settings?.workHoursRequired || 4;
    const workingHours = settings?.workingHours || 7;

    // التحقق من العطل الرسمية اليوم
    const isOfficialHoliday = await prisma.officialHoliday.findFirst({
      where: {
        startDate: { lte: today },
        endDate: { gte: today },
      },
    });

    // التحقق من أن اليوم ليس عطلة نهاية أسبوع (الجمعة والسبت)
    const isWeekend = today.getDay() === 5 || today.getDay() === 6;

    // التحقق من وجود إجازة معتمدة اليوم
    const currentLeave = await prisma.leaveRequest.findFirst({
      where: {
        userId,
        status: 'APPROVED',
        startDate: { lte: today },
        endDate: { gte: today },
      },
    });

    // حضور اليوم - جلب جميع السجلات
    const todayAttendance = await prisma.attendanceRecord.findMany({
      where: {
        userId,
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      orderBy: {
        entryNumber: 'asc',
      },
    });

    // تحديد حالة الحضور اليوم بناءً على مجموع ساعات العمل
    let attendanceStatus: 'FULL_ATTENDANCE' | 'PARTIAL_ATTENDANCE' | 'ON_LEAVE' | 'ABSENT';
    let totalWorkingHours = 0;

    if (currentLeave) {
      attendanceStatus = 'ON_LEAVE';
    } else if (todayAttendance.length === 0) {
      attendanceStatus = 'ABSENT';
    } else {
      // حساب إجمالي ساعات العمل من جميع السجلات
      totalWorkingHours = todayAttendance.reduce((total, record) => {
        if (record.checkInTime && record.checkOutTime) {
          const checkIn = new Date(record.checkInTime);
          const checkOut = new Date(record.checkOutTime);
          const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
          return total + hours;
        }
        return total;
      }, 0);

      // للموظف الذي لا يزال في المكتب (آخر سجل بدون خروج)
      const lastRecord = todayAttendance[todayAttendance.length - 1];
      if (!lastRecord.checkOutTime) {
        // حساب ساعات العمل حتى الآن
        const now = new Date();
        const checkIn = new Date(lastRecord.checkInTime);
        const currentHours = (now.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        totalWorkingHours += currentHours;
      }

      // تحديد نوع الحضور بناءً على مجموع ساعات العمل
      if (totalWorkingHours >= minWorkingHours) {
        attendanceStatus = 'FULL_ATTENDANCE'; // حضور: ساعات العمل >= الحد الأدنى
      } else if (totalWorkingHours > 0) {
        attendanceStatus = 'PARTIAL_ATTENDANCE'; // حضور جزئي: ساعات العمل < الحد الأدنى
      } else {
        attendanceStatus = 'ABSENT'; // غائب: لا توجد ساعات عمل
      }

      totalWorkingHours = Math.round(totalWorkingHours * 10) / 10;
    }

    // إحصائيات الشهر الحالي
    const monthlyAttendance = await prisma.attendanceRecord.findMany({
      where: {
        userId,
        date: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    // حساب أيام العمل في الشهر (استثناء الجمعة والسبت والعطل الرسمية)
    const workDaysInMonth = await getWorkDaysInMonth(today.getFullYear(), today.getMonth());
    
    // تصنيف أيام الحضور بناءً على مجموع ساعات العمل لكل يوم
    const attendanceDaysByType = {
      fullAttendance: 0,
      partialAttendance: 0,
      late: 0
    };

    // تجميع السجلات حسب التاريخ
    const recordsByDate = new Map();
    monthlyAttendance.forEach(record => {
      const dateKey = record.date.toISOString().split('T')[0];
      if (!recordsByDate.has(dateKey)) {
        recordsByDate.set(dateKey, []);
      }
      recordsByDate.get(dateKey).push(record);
    });

    // حساب وقت التأخير
    const lateThreshold = new Date();
    const [startHours, startMinutes] = workStartTime.split(':');
    lateThreshold.setHours(parseInt(startHours), parseInt(startMinutes), 0, 0);

    recordsByDate.forEach((dayRecords, dateKey) => {
      const recordDate = new Date(dateKey);
      
      // تحديث وقت التأخير لنفس تاريخ السجل
      const dayLateThreshold = new Date(recordDate);
      dayLateThreshold.setHours(parseInt(startHours), parseInt(startMinutes), 0, 0);
      
      // البحث عن أول دخول في اليوم
      const firstRecord = dayRecords.find((r: any) => r.checkInTime);
      if (firstRecord) {
        const checkInTime = new Date(firstRecord.checkInTime);
        const isLate = checkInTime > dayLateThreshold;
        if (isLate) {
          attendanceDaysByType.late++;
        }
      }

      // حساب إجمالي ساعات العمل لهذا اليوم
      const dayTotalHours = dayRecords.reduce((total: number, record: any) => {
        if (record.checkInTime && record.checkOutTime) {
          const checkIn = new Date(record.checkInTime);
          const checkOut = new Date(record.checkOutTime);
          const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
          return total + hours;
        }
        return total;
      }, 0);

      // تصنيف نوع الحضور بناءً على مجموع ساعات العمل
      if (dayTotalHours >= minWorkingHours) {
        attendanceDaysByType.fullAttendance++;
      } else if (dayTotalHours > 0) {
        attendanceDaysByType.partialAttendance++;
      }
      // إذا كان dayTotalHours = 0، فهذا يعني أن الموظف دخل ولم يخرج
      // في هذه الحالة نعتبره حضور كامل (لا يزال في المكتب)
      else if (dayRecords.some((r: any) => r.checkInTime && !r.checkOutTime)) {
        attendanceDaysByType.fullAttendance++;
      }
    });

    // أيام الحضور الفريدة
    const presentDays = recordsByDate.size;

    // الإجازات المعتمدة في الشهر الحالي
    const approvedLeavesThisMonth = await prisma.leaveRequest.findMany({
      where: {
        userId,
        status: 'APPROVED',
        OR: [
          {
            startDate: { gte: startOfMonth, lte: endOfMonth },
          },
          {
            endDate: { gte: startOfMonth, lte: endOfMonth },
          },
          {
            AND: [
              { startDate: { lte: startOfMonth } },
              { endDate: { gte: endOfMonth } },
            ],
          },
        ],
      },
    });

    // حساب أيام الإجازة في الشهر الحالي
    let leaveDaysThisMonth = 0;
    approvedLeavesThisMonth.forEach(leave => {
      const leaveStart = new Date(Math.max(leave.startDate.getTime(), startOfMonth.getTime()));
      const leaveEnd = new Date(Math.min(leave.endDate.getTime(), endOfMonth.getTime()));
      const diffTime = leaveEnd.getTime() - leaveStart.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      leaveDaysThisMonth += diffDays;
    });

    // حساب أيام الغياب
    const effectiveWorkDays = workDaysInMonth - leaveDaysThisMonth;
    const absentDays = Math.max(0, effectiveWorkDays - presentDays);

    // الطلبات المعلقة
    const pendingLeaves = await prisma.leaveRequest.count({
      where: {
        userId,
        status: 'PENDING',
      },
    });

    const pendingVisitors = await prisma.visitorRequest.count({
      where: {
        userId,
        status: 'PENDING',
      },
    });

    const pendingAfterHours = await prisma.afterHoursPermit.count({
      where: {
        userId,
        status: 'PENDING',
      },
    });

    // الأحداث القادمة
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);

    const upcomingHolidays = await prisma.officialHoliday.count({
      where: {
        startDate: {
          gte: today,
          lte: nextWeek,
        },
      },
    });

    const approvedLeaves = await prisma.leaveRequest.count({
      where: {
        userId,
        status: 'APPROVED',
        startDate: {
          gte: today,
          lte: nextWeek,
        },
      },
    });

    const scheduledVisitors = await prisma.visitorRequest.count({
      where: {
        userId,
        status: 'APPROVED',
        visitDate: {
          gte: today,
          lte: nextWeek,
        },
      },
    });

    // حساب معدلات الحضور
    const totalAttendanceRate = effectiveWorkDays > 0 ? 
      Math.round((presentDays / effectiveWorkDays) * 100) : 100;
    
    const fullAttendanceRate = effectiveWorkDays > 0 ? 
      Math.round((attendanceDaysByType.fullAttendance / effectiveWorkDays) * 100) : 100;

    // الحصول على أول وآخر سجل لليوم
    const firstRecord = todayAttendance[0];
    const lastRecord = todayAttendance[todayAttendance.length - 1];

    const stats = {
      todayAttendance: {
        status: attendanceStatus,
        checkInTime: firstRecord?.checkInTime?.toISOString() || null,
        checkOutTime: lastRecord?.checkOutTime?.toISOString() || null,
        exitType: lastRecord?.exitType || null,
        isOnLeave: !!currentLeave,
        leaveReason: currentLeave?.reason || null,
        workingHours: totalWorkingHours,
        expectedWorkingHours: workingHours,
        isWeekend,
        isOfficialHoliday: !!isOfficialHoliday,
        holidayName: isOfficialHoliday?.name || null,
      },
      thisMonthStats: {
        totalWorkDays: workDaysInMonth,
        fullAttendanceDays: attendanceDaysByType.fullAttendance,
        partialAttendanceDays: attendanceDaysByType.partialAttendance,
        leaveDays: leaveDaysThisMonth,
        absentDays,
        lateDays: attendanceDaysByType.late,
        attendanceRate: totalAttendanceRate,
        fullAttendanceRate,
        effectiveWorkDays,
      },
      pendingRequests: {
        leaves: pendingLeaves,
        visitors: pendingVisitors,
        afterHours: pendingAfterHours,
      },
      upcomingEvents: {
        holidays: upcomingHolidays,
        approvedLeaves,
        scheduledVisitors,
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("خطأ في جلب إحصائيات الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإحصائيات" },
      { status: 500 }
    );
  }
}

// دالة لحساب أيام العمل في الشهر (استثناء الجمعة والسبت والعطل الرسمية)
async function getWorkDaysInMonth(year: number, month: number): Promise<number> {
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let workDays = 0;

  // جلب العطل الرسمية في الشهر
  const startOfMonth = new Date(year, month, 1);
  const endOfMonth = new Date(year, month + 1, 0);
  
  const officialHolidays = await prisma.officialHoliday.findMany({
    where: {
      OR: [
        {
          startDate: { gte: startOfMonth, lte: endOfMonth },
        },
        {
          endDate: { gte: startOfMonth, lte: endOfMonth },
        },
        {
          AND: [
            { startDate: { lte: startOfMonth } },
            { endDate: { gte: endOfMonth } },
          ],
        },
      ],
    },
  });

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dayOfWeek = date.getDay();
    
    // تخطي الجمعة والسبت
    if (dayOfWeek === 5 || dayOfWeek === 6) {
      continue;
    }

    // التحقق من العطل الرسمية
    const isHoliday = officialHolidays.some(holiday => {
      const holidayStart = new Date(holiday.startDate);
      const holidayEnd = new Date(holiday.endDate);
      return date >= holidayStart && date <= holidayEnd;
    });

    if (!isHoliday) {
      workDays++;
    }
  }

  return workDays;
}
