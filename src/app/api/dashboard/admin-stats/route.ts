import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // جلب إعدادات النظام للحصول على ساعات العمل المعتمدة
    const settings = await prisma.settings.findFirst();
    const minWorkingHours = settings?.workHoursRequired || 4;
    const workingHours = settings?.workingHours || 7;

    // إجمالي الموظفين في النظام
    const totalEmployees = await prisma.user.count({
      where: {
        role: {
          in: ["EMPLOYEE", "MANAGER"]
        }
      }
    });

    // إجمالي الأقسام
    const totalDepartments = await prisma.department.count();

    // الحصول على جميع الموظفين
    const allEmployees = await prisma.user.findMany({
      where: {
        role: {
          in: ["EMPLOYEE", "MANAGER"]
        }
      },
      select: {
        id: true,
        name: true,
        employeeNumber: true,
      },
    });

    const allEmployeeIds = allEmployees.map(emp => emp.id);

    // التحقق من العطل الرسمية اليوم
    const isOfficialHoliday = await prisma.officialHoliday.findFirst({
      where: {
        startDate: { lte: today },
        endDate: { gte: today },
      },
    });

    // التحقق من أن اليوم ليس عطلة نهاية أسبوع (الجمعة والسبت)
    const isWeekend = today.getDay() === 5 || today.getDay() === 6;

    // الموظفون في إجازة معتمدة اليوم
    const employeesOnApprovedLeave = await prisma.leaveRequest.findMany({
      where: {
        userId: { in: allEmployeeIds },
        status: 'APPROVED',
        startDate: { lte: today },
        endDate: { gte: today },
      },
      select: {
        userId: true,
        reason: true,
        user: {
          select: {
            name: true,
            employeeNumber: true,
          }
        }
      },
    });

    const employeesOnLeaveIds = new Set(employeesOnApprovedLeave.map(leave => leave.userId));

    // جلب جميع سجلات الحضور اليوم
    const allTodayAttendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        userId: { in: allEmployeeIds },
        date: { gte: startOfDay, lt: endOfDay },
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          }
        }
      },
      orderBy: [
        { userId: 'asc' },
        { entryNumber: 'asc' }
      ],
    });

    // تجميع السجلات حسب المستخدم وحساب إجمالي ساعات العمل لكل موظف
    const employeeAttendanceMap = new Map();
    
    allTodayAttendanceRecords.forEach(record => {
      const userId = record.userId;
      
      if (!employeeAttendanceMap.has(userId)) {
        employeeAttendanceMap.set(userId, {
          user: record.user,
          records: [],
          totalWorkingHours: 0
        });
      }
      
      const employeeData = employeeAttendanceMap.get(userId);
      employeeData.records.push(record);
      
      // حساب ساعات العمل لهذا السجل
      if (record.checkInTime && record.checkOutTime) {
        const checkIn = new Date(record.checkInTime);
        const checkOut = new Date(record.checkOutTime);
        const hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        employeeData.totalWorkingHours += hoursWorked;
      }
    });

    // تصنيف الموظفين حسب نوع الحضور بناءً على مجموع ساعات العمل
    const fullAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      status?: string;
      exitTime?: string;
      exitType?: string;
      totalWorkingHours?: number;
    }> = [];
    const partialAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      exitTime: string;
      exitType: string;
      totalWorkingHours: number;
    }> = [];

    // معالجة كل موظف لديه سجلات حضور
    employeeAttendanceMap.forEach((employeeData, userId) => {
      const { user, records, totalWorkingHours } = employeeData;
      const firstRecord = records[0]; // أول دخول
      const lastRecord = records[records.length - 1]; // آخر حركة
      
      // تحديد نوع الحضور بناءً على مجموع ساعات العمل
      if (totalWorkingHours >= minWorkingHours) {
        // حضور: ساعات العمل >= الحد الأدنى
        fullAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          status: lastRecord.checkOutTime ? undefined : 'في المكتب',
          exitTime: lastRecord.checkOutTime?.toISOString(),
          exitType: lastRecord.exitType,
          totalWorkingHours: Math.round(totalWorkingHours * 10) / 10
        });
      } else if (totalWorkingHours > 0) {
        // حضور جزئي: ساعات العمل < الحد الأدنى وأكبر من 0
        partialAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          exitTime: lastRecord.checkOutTime?.toISOString() || new Date().toISOString(),
          exitType: lastRecord.exitType || 'PERSONAL',
          totalWorkingHours: Math.round(totalWorkingHours * 10) / 10
        });
      }
      // إذا كان totalWorkingHours = 0، فهذا يعني أن الموظف دخل ولم يخرج بعد
      // في هذه الحالة نعتبره حضور (في المكتب)
      else {
        fullAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          status: 'في المكتب',
          totalWorkingHours: 0
        });
      }
    });

    // الموظفون الذين لديهم سجل حضور اليوم
    const employeesWithAttendanceToday = new Set(employeeAttendanceMap.keys());

    // حساب الغياب الحقيقي (ليس في إجازة وليس له سجل حضور)
    const absentEmployees = allEmployees.filter(employee => 
      !employeesOnLeaveIds.has(employee.id) && !employeesWithAttendanceToday.has(employee.id)
    );

    // الأعداد النهائية
    const fullAttendanceToday = fullAttendanceEmployees.length;
    const partialAttendanceToday = partialAttendanceEmployees.length;
    const onLeaveToday = employeesOnApprovedLeave.length;
    const absentToday = absentEmployees.length;

    // إحصائيات إضافية
    const totalVisitorsToday = await prisma.visitorRequest.count({
      where: {
        visitDate: { gte: startOfDay, lt: endOfDay },
        status: 'APPROVED'
      }
    });

    const activeVisitors = await prisma.visitorRequest.count({
      where: {
        visitDate: { gte: startOfDay, lt: endOfDay },
        status: 'APPROVED',
        checkOutTime: null
      }
    });

    // الطلبات المعلقة
    const pendingLeaves = await prisma.leaveRequest.count({
      where: {
        status: 'PENDING',
      },
    });

    const pendingVisitors = await prisma.visitorRequest.count({
      where: {
        status: 'PENDING',
      },
    });

    const pendingAfterHours = await prisma.afterHoursPermit.count({
      where: {
        status: 'PENDING',
      },
    });

    // إحصائيات الشهر الحالي
    const monthlyAttendance = await prisma.attendanceRecord.findMany({
      where: {
        userId: { in: allEmployeeIds },
        date: { gte: startOfMonth, lt: endOfDay },
      },
    });

    const workDaysInMonth = getWorkDaysInMonth(today.getFullYear(), today.getMonth());
    const expectedAttendance = totalEmployees * workDaysInMonth;
    const actualAttendance = monthlyAttendance.length;
    const attendanceRate = expectedAttendance > 0 ? Math.round((actualAttendance / expectedAttendance) * 100) : 0;

    // ساعات العمل الإضافية (أكثر من ساعات العمل المطلوبة)
    const recordsWithBothTimes = monthlyAttendance.filter(record =>
      record.checkInTime && record.checkOutTime
    );

    const overtimeHours = recordsWithBothTimes.reduce((total, record) => {
      const checkIn = new Date(record.checkInTime);
      const checkOut = new Date(record.checkOutTime!);
      const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      return total + Math.max(0, hours - workingHours);
    }, 0);

    const stats = {
      overview: {
        totalEmployees,
        totalDepartments,
        fullAttendanceToday,     // حضور (ساعات العمل >= الحد الأدنى)
        partialAttendanceToday,  // حضور جزئي (ساعات العمل < الحد الأدنى)
        onLeaveToday,            // في إجازة معتمدة
        absentToday,             // غياب بدون إذن
        totalVisitorsToday,
        activeVisitors,
        isWeekend,
        isOfficialHoliday: !!isOfficialHoliday,
        holidayName: isOfficialHoliday?.name || null,
        minWorkingHours,         // الحد الأدنى لساعات العمل
        workingHours,            // إجمالي ساعات العمل اليومية
      },
      pendingApprovals: {
        leaves: pendingLeaves,
        visitors: pendingVisitors,
        afterHours: pendingAfterHours,
        total: pendingLeaves + pendingVisitors + pendingAfterHours,
      },
      monthlyStats: {
        attendanceRate,
        overtimeHours: Math.round(overtimeHours * 10) / 10,
        workDaysInMonth,
        actualAttendanceDays: actualAttendance,
      },
      details: {
        fullAttendanceEmployees, // حضور (ساعات العمل >= الحد الأدنى)
        partialAttendanceEmployees, // حضور جزئي (ساعات العمل < الحد الأدنى)
        employeesOnLeave: employeesOnApprovedLeave.map(leave => ({
          id: leave.userId,
          name: leave.user.name,
          employeeNumber: leave.user.employeeNumber,
          leaveReason: leave.reason,
        })),
        absentEmployees: absentEmployees.map(emp => ({
          id: emp.id,
          name: emp.name,
          employeeNumber: emp.employeeNumber,
        })),
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("خطأ في جلب إحصائيات المدير العام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإحصائيات" },
      { status: 500 }
    );
  }
}

function getWorkDaysInMonth(year: number, month: number): number {
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let workDays = 0;

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dayOfWeek = date.getDay();
    if (dayOfWeek !== 5 && dayOfWeek !== 6) {
      workDays++;
    }
  }

  return workDays;
} 