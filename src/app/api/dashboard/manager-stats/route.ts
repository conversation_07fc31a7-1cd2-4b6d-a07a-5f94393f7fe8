import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "MANAGER" && userRole !== "ADMIN" && userRole !== "HR") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const userId = session.user.id;
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);

    // جلب إعدادات النظام للحصول على ساعات العمل المعتمدة
    const settings = await prisma.settings.findFirst();
    const minWorkingHours = settings?.workHoursRequired || 4;
    const workingHours = settings?.workingHours || 7;
    const workStartTime = settings?.workStartTime || '08:30';

    // الحصول على معلومات المدير وقسمه
    const manager = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        department: true,
      },
    });

    if (!manager || !manager.departmentId) {
      return NextResponse.json({ error: "لم يتم العثور على معلومات القسم" }, { status: 404 });
    }

    // الحصول على جميع أعضاء الفريق في نفس القسم
    const teamMembers = await prisma.user.findMany({
      where: {
        departmentId: manager.departmentId,
        role: {
          in: ["EMPLOYEE", "MANAGER"]
        }
      },
      select: {
        id: true,
        name: true,
        employeeNumber: true,
      },
    });

    const teamMemberIds = teamMembers.map(member => member.id);
    const totalEmployees = teamMembers.length;

    // التحقق من العطل الرسمية اليوم
    const isOfficialHoliday = await prisma.officialHoliday.findFirst({
      where: {
        startDate: { lte: today },
        endDate: { gte: today },
      },
    });

    // التحقق من أن اليوم ليس عطلة نهاية أسبوع (الجمعة والسبت)
    const isWeekend = today.getDay() === 5 || today.getDay() === 6;

    // الموظفون في إجازة معتمدة اليوم
    const employeesOnApprovedLeave = await prisma.leaveRequest.findMany({
      where: {
        userId: { in: teamMemberIds },
        status: 'APPROVED',
        startDate: { lte: today },
        endDate: { gte: today },
      },
      select: {
        userId: true,
        reason: true,
        user: {
          select: {
            name: true,
            employeeNumber: true,
          }
        }
      },
    });

    const employeesOnLeaveIds = new Set(employeesOnApprovedLeave.map(leave => leave.userId));

    // جلب جميع سجلات الحضور اليوم
    const allTodayAttendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        userId: { in: teamMemberIds },
        date: { gte: startOfDay, lt: endOfDay },
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          }
        }
      },
      orderBy: [
        { userId: 'asc' },
        { entryNumber: 'asc' }
      ],
    });

    // تجميع السجلات حسب المستخدم وحساب إجمالي ساعات العمل لكل موظف
    const employeeAttendanceMap = new Map();
    
    allTodayAttendanceRecords.forEach(record => {
      const userId = record.userId;
      
      if (!employeeAttendanceMap.has(userId)) {
        employeeAttendanceMap.set(userId, {
          user: record.user,
          records: [],
          totalWorkingHours: 0
        });
      }
      
      const employeeData = employeeAttendanceMap.get(userId);
      employeeData.records.push(record);
      
      // حساب ساعات العمل لهذا السجل
      if (record.checkInTime && record.checkOutTime) {
        const checkIn = new Date(record.checkInTime);
        const checkOut = new Date(record.checkOutTime);
        const hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        employeeData.totalWorkingHours += hoursWorked;
      }
    });

    // تصنيف الموظفين حسب نوع الحضور بناءً على مجموع ساعات العمل
    const fullAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      status?: string;
      exitTime?: string;
      exitType?: string;
      totalWorkingHours?: number;
    }> = [];
    const partialAttendanceEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      exitTime: string;
      exitType: string;
      totalWorkingHours?: number;
    }> = [];
    const lateEmployees: Array<{
      id: string;
      name: string;
      employeeNumber: string | null;
      checkInTime: string;
      minutesLate: number;
    }> = [];

    // حساب وقت التأخير
    const lateThreshold = new Date(startOfDay);
    const [startHours, startMinutes] = workStartTime.split(':');
    lateThreshold.setHours(parseInt(startHours), parseInt(startMinutes), 0, 0);

    // معالجة كل موظف لديه سجلات حضور
    employeeAttendanceMap.forEach((employeeData, userId) => {
      const { user, records, totalWorkingHours } = employeeData;
      const firstRecord = records[0]; // أول دخول
      const lastRecord = records[records.length - 1]; // آخر حركة
      
      const checkInTime = new Date(firstRecord.checkInTime);
      const isLate = checkInTime > lateThreshold;
      const minutesLate = isLate ? 
        Math.round((checkInTime.getTime() - lateThreshold.getTime()) / (1000 * 60)) : 0;

      // تحديد نوع الحضور بناءً على مجموع ساعات العمل
      if (totalWorkingHours >= minWorkingHours) {
        // حضور: ساعات العمل >= الحد الأدنى
        fullAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          status: lastRecord.checkOutTime ? undefined : 'في المكتب',
          exitTime: lastRecord.checkOutTime?.toISOString(),
          exitType: lastRecord.exitType,
          totalWorkingHours: Math.round(totalWorkingHours * 10) / 10
        });
      } else if (totalWorkingHours > 0) {
        // حضور جزئي: ساعات العمل < الحد الأدنى وأكبر من 0
        partialAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          exitTime: lastRecord.checkOutTime?.toISOString() || new Date().toISOString(),
          exitType: lastRecord.exitType || 'PERSONAL',
          totalWorkingHours: Math.round(totalWorkingHours * 10) / 10
        });
      }
      // إذا كان totalWorkingHours = 0، فهذا يعني أن الموظف دخل ولم يخرج بعد
      // في هذه الحالة نعتبره حضور (في المكتب)
      else {
        fullAttendanceEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          status: 'في المكتب',
          totalWorkingHours: 0
        });
      }

      // إضافة للمتأخرين إذا كان متأخراً
      if (isLate) {
        lateEmployees.push({
          id: userId,
          name: user.name,
          employeeNumber: user.employeeNumber,
          checkInTime: firstRecord.checkInTime.toISOString(),
          minutesLate
        });
      }
    });

    // الموظفون الذين لديهم سجل حضور اليوم
    const employeesWithAttendanceToday = new Set(employeeAttendanceMap.keys());

    // حساب الغياب الحقيقي (ليس في إجازة وليس له سجل حضور)
    const absentEmployees = teamMembers.filter(member => 
      !employeesOnLeaveIds.has(member.id) && !employeesWithAttendanceToday.has(member.id)
    );

    // الأعداد النهائية
    const fullAttendanceToday = fullAttendanceEmployees.length;
    const partialAttendanceToday = partialAttendanceEmployees.length;
    const onLeaveToday = employeesOnApprovedLeave.length;
    const absentToday = absentEmployees.length;
    const lateToday = lateEmployees.length;

    // التحقق من صحة الحسابات
    const totalAccountedFor = fullAttendanceToday + partialAttendanceToday + onLeaveToday + absentToday;
    
    // إضافة تسجيل للتطوير
    if (process.env.NODE_ENV === "development") {
      console.log("إحصائيات الحضور:", {
        totalEmployees,
        fullAttendanceToday,
        partialAttendanceToday,
        onLeaveToday,
        absentToday,
        lateToday,
        totalAccountedFor,
        employeesWithAttendance: employeesWithAttendanceToday.size,
        employeesOnLeave: employeesOnLeaveIds.size,
        teamMemberIds: teamMemberIds.length,
        attendanceRecords: allTodayAttendanceRecords.length,
        isWeekend,
        isOfficialHoliday: !!isOfficialHoliday,
        minWorkingHours,
        workingHours
      });
      
      // تفاصيل إضافية
      console.log("تفاصيل الموظفين:");
      console.log("- في إجازة:", employeesOnApprovedLeave.map(emp => emp.user.name));
      console.log("- حضور:", fullAttendanceEmployees.map(emp => emp.name));
      console.log("- حضور جزئي:", partialAttendanceEmployees.map(emp => emp.name));
      console.log("- غائبون:", absentEmployees.map(emp => emp.name));
    }

    // الطلبات المعلقة (من الفريق فقط)
    const pendingLeaves = await prisma.leaveRequest.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'PENDING',
      },
    });

    const pendingVisitors = await prisma.visitorRequest.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'PENDING',
      },
    });

    const pendingAfterHours = await prisma.afterHoursPermit.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'PENDING',
      },
    });

    // إحصائيات القسم (للفريق فقط)
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthlyAttendance = await prisma.attendanceRecord.findMany({
      where: {
        userId: { in: teamMemberIds },
        date: { gte: startOfMonth, lt: endOfDay },
      },
    });

    const workDaysInMonth = getWorkDaysInMonth(today.getFullYear(), today.getMonth());
    const expectedAttendance = totalEmployees * workDaysInMonth;
    const actualAttendance = monthlyAttendance.length;
    const attendanceRate = expectedAttendance > 0 ? Math.round((actualAttendance / expectedAttendance) * 100) : 0;

    // متوسط ساعات العمل
    const recordsWithBothTimes = monthlyAttendance.filter(record =>
      record.checkInTime && record.checkOutTime
    );

    const totalWorkingHours = recordsWithBothTimes.reduce((total, record) => {
      const checkIn = new Date(record.checkInTime);
      const checkOut = new Date(record.checkOutTime!);
      const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      return total + hours;
    }, 0);

    const avgWorkingHours = recordsWithBothTimes.length > 0 ?
      Math.round((totalWorkingHours / recordsWithBothTimes.length) * 10) / 10 : 0;

    // ساعات إضافية (أكثر من ساعات العمل المطلوبة)
    const overtimeHours = recordsWithBothTimes.reduce((total, record) => {
      const checkIn = new Date(record.checkInTime);
      const checkOut = new Date(record.checkOutTime!);
      const hours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      return total + Math.max(0, hours - workingHours);
    }, 0);

    // الأحداث القادمة
    const upcomingApprovedLeaves = await prisma.leaveRequest.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'APPROVED',
        startDate: { gte: today, lte: nextWeek },
      },
    });

    const upcomingVisitors = await prisma.visitorRequest.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'APPROVED',
        visitDate: { gte: today, lte: nextWeek },
      },
    });

    const upcomingAfterHours = await prisma.afterHoursPermit.count({
      where: {
        userId: { in: teamMemberIds },
        status: 'APPROVED',
        date: { gte: today, lte: nextWeek },
      },
    });

    // النشاط الحديث (اليوم)
    const todayApprovals = await prisma.leaveRequest.count({
      where: {
        userId: { in: teamMemberIds },
        status: { in: ['APPROVED', 'REJECTED'] },
        updatedAt: { gte: startOfDay, lt: endOfDay },
      },
    });

    const newRequestsToday = await prisma.leaveRequest.count({
      where: {
        userId: { in: teamMemberIds },
        createdAt: { gte: startOfDay, lt: endOfDay },
      },
    }) + await prisma.visitorRequest.count({
      where: {
        userId: { in: teamMemberIds },
        createdAt: { gte: startOfDay, lt: endOfDay },
      },
    }) + await prisma.afterHoursPermit.count({
      where: {
        userId: { in: teamMemberIds },
        createdAt: { gte: startOfDay, lt: endOfDay },
      },
    });

    const stats = {
      teamOverview: {
        totalEmployees,
        fullAttendanceToday,     // حضور (ساعات العمل >= الحد الأدنى)
        partialAttendanceToday,  // حضور جزئي (ساعات العمل < الحد الأدنى)
        onLeaveToday,            // في إجازة معتمدة
        absentToday,             // غياب بدون إذن
        lateToday,               // متأخرون
        isWeekend,
        isOfficialHoliday: !!isOfficialHoliday,
        holidayName: isOfficialHoliday?.name || null,
        minWorkingHours,         // الحد الأدنى لساعات العمل
        workingHours,            // إجمالي ساعات العمل اليومية
      },
      pendingApprovals: {
        leaves: pendingLeaves,
        visitors: pendingVisitors,
        afterHours: pendingAfterHours,
        total: pendingLeaves + pendingVisitors + pendingAfterHours,
      },
      departmentStats: {
        attendanceRate,
        avgWorkingHours,
        overtimeHours: Math.round(overtimeHours * 10) / 10,
      },
      recentActivity: {
        newRequests: newRequestsToday,
        approvedToday: todayApprovals,
        rejectedToday: 0, // يمكن حسابها بشكل منفصل إذا لزم الأمر
      },
      upcomingEvents: {
        scheduledLeaves: upcomingApprovedLeaves,
        expectedVisitors: upcomingVisitors,
        afterHoursWork: upcomingAfterHours,
      },
      // تفاصيل إضافية للمراقبة
      details: {
        fullAttendanceEmployees, // حضور (ساعات العمل >= الحد الأدنى)
        partialAttendanceEmployees, // حضور جزئي (ساعات العمل < الحد الأدنى)
        employeesOnLeave: employeesOnApprovedLeave.map(leave => ({
          id: leave.userId,
          name: leave.user.name,
          employeeNumber: leave.user.employeeNumber,
          leaveReason: leave.reason,
        })),
        absentEmployees: absentEmployees.map(emp => ({
          id: emp.id,
          name: emp.name,
          employeeNumber: emp.employeeNumber,
        })),
        lateEmployees,
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("خطأ في جلب إحصائيات المدير:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الإحصائيات" },
      { status: 500 }
    );
  }
}

function getWorkDaysInMonth(year: number, month: number): number {
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let workDays = 0;

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dayOfWeek = date.getDay();
    if (dayOfWeek !== 5 && dayOfWeek !== 6) {
      workDays++;
    }
  }

  return workDays;
}
