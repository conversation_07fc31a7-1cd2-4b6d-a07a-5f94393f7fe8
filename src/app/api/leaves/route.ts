import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب جميع طلبات الإجازة
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    const userId = session.user.id;

    let whereClause = {};

    // إذا كان المستخدم موظف عادي، يرى طلباته فقط
    if (userRole === "EMPLOYEE" || userRole === "SECURITY") {
      whereClause = { userId };
    }
    // إذا كان المستخدم مدير، يرى طلباته وطلبات الموظفين التابعين له
    else if (userRole === "MANAGER") {
      whereClause = {
        OR: [
          { userId }, // طلبات المدير نفسه
          {
            user: {
              managerId: userId, // طلبات الموظفين التابعين لهذا المدير
            },
          },
        ],
      };
    }
    // HR والأدمن يرون جميع الطلبات

    const leaves = await prisma.leaveRequest.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(leaves);
  } catch (error) {
    console.error("خطأ في جلب طلبات الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب طلبات الإجازة" },
      { status: 500 }
    );
  }
}

// POST - إنشاء طلب إجازة جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { startDate, endDate, reason } = body;

    // التحقق من البيانات المطلوبة
    if (!startDate || !endDate || !reason) {
      return NextResponse.json(
        { error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من صحة التواريخ
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      return NextResponse.json(
        { error: "لا يمكن أن يكون تاريخ البداية في الماضي" },
        { status: 400 }
      );
    }

    if (end < start) {
      return NextResponse.json(
        { error: "تاريخ النهاية يجب أن يكون بعد تاريخ البداية" },
        { status: 400 }
      );
    }

    // التحقق من عدم تداخل الإجازات
    const overlappingLeave = await prisma.leaveRequest.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: ["PENDING", "APPROVED"],
        },
        OR: [
          {
            AND: [
              { startDate: { lte: start } },
              { endDate: { gte: start } },
            ],
          },
          {
            AND: [
              { startDate: { lte: end } },
              { endDate: { gte: end } },
            ],
          },
          {
            AND: [
              { startDate: { gte: start } },
              { endDate: { lte: end } },
            ],
          },
        ],
      },
    });

    if (overlappingLeave) {
      return NextResponse.json(
        { error: "يوجد طلب إجازة متداخل مع هذه التواريخ" },
        { status: 400 }
      );
    }

    // إنشاء طلب الإجازة
    const leave = await prisma.leaveRequest.create({
      data: {
        userId: session.user.id,
        startDate: start,
        endDate: end,
        reason: reason.trim(),
        status: "PENDING",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(leave, { status: 201 });
  } catch (error) {
    console.error("خطأ في إنشاء طلب الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في إنشاء طلب الإجازة" },
      { status: 500 }
    );
  }
}
