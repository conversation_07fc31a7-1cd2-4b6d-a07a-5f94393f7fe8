import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - جلب طلب إجازة واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const leave = await prisma.leaveRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!leave) {
      return NextResponse.json({ error: "طلب الإجازة غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const canAccess =
      userRole === "ADMIN" ||
      userRole === "HR" ||
      userRole === "MANAGER" ||
      leave.userId === session.user.id;

    if (!canAccess) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 403 });
    }

    return NextResponse.json(leave);
  } catch (error) {
    console.error("خطأ في جلب طلب الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب طلب الإجازة" },
      { status: 500 }
    );
  }
}

// PATCH - تحديث حالة طلب الإجازة
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { status } = body;

    // التحقق من صحة الحالة
    const validStatuses = ["PENDING", "APPROVED", "REJECTED", "CANCELLED"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "حالة غير صحيحة" },
        { status: 400 }
      );
    }

    // جلب طلب الإجازة
    const existingLeave = await prisma.leaveRequest.findUnique({
      where: { id },
    });

    if (!existingLeave) {
      return NextResponse.json({ error: "طلب الإجازة غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const canManage = userRole === "ADMIN" || userRole === "HR" || userRole === "MANAGER";
    const isOwner = existingLeave.userId === session.user.id;

    // فقط المدراء يمكنهم الموافقة أو الرفض
    if ((status === "APPROVED" || status === "REJECTED") && !canManage) {
      return NextResponse.json({ error: "غير مصرح لك بهذا الإجراء" }, { status: 403 });
    }

    // صاحب الطلب يمكنه الإلغاء فقط
    if (status === "CANCELLED" && !isOwner && !canManage) {
      return NextResponse.json({ error: "غير مصرح لك بهذا الإجراء" }, { status: 403 });
    }

    // تحديث حالة الطلب
    const updatedLeave = await prisma.leaveRequest.update({
      where: { id },
      data: { status },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(updatedLeave);
  } catch (error) {
    console.error("خطأ في تحديث طلب الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث طلب الإجازة" },
      { status: 500 }
    );
  }
}

// PUT - تحديث طلب الإجازة
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const body = await request.json();
    const { startDate, endDate, reason } = body;

    // التحقق من البيانات المطلوبة
    if (!startDate || !endDate || !reason) {
      return NextResponse.json(
        { error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }

    // جلب طلب الإجازة الحالي
    const existingLeave = await prisma.leaveRequest.findUnique({
      where: { id },
    });

    if (!existingLeave) {
      return NextResponse.json({ error: "طلب الإجازة غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const canEdit =
      userRole === "ADMIN" ||
      userRole === "HR" ||
      userRole === "MANAGER" ||
      (existingLeave.userId === session.user.id && existingLeave.status === "PENDING");

    if (!canEdit) {
      return NextResponse.json({ error: "غير مصرح لك بتعديل هذا الطلب" }, { status: 403 });
    }

    // التحقق من صحة التواريخ
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      return NextResponse.json(
        { error: "لا يمكن أن يكون تاريخ البداية في الماضي" },
        { status: 400 }
      );
    }

    if (end < start) {
      return NextResponse.json(
        { error: "تاريخ النهاية يجب أن يكون بعد تاريخ البداية" },
        { status: 400 }
      );
    }

    // تحديث طلب الإجازة
    const updatedLeave = await prisma.leaveRequest.update({
      where: { id },
      data: {
        startDate: start,
        endDate: end,
        reason: reason.trim(),
        // إعادة تعيين الحالة إلى PENDING إذا تم التعديل
        status: existingLeave.status === "REJECTED" ? "PENDING" : existingLeave.status,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json(updatedLeave);
  } catch (error) {
    console.error("خطأ في تحديث طلب الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تحديث طلب الإجازة" },
      { status: 500 }
    );
  }
}

// DELETE - حذف طلب الإجازة
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // جلب طلب الإجازة
    const existingLeave = await prisma.leaveRequest.findUnique({
      where: { id },
    });

    if (!existingLeave) {
      return NextResponse.json({ error: "طلب الإجازة غير موجود" }, { status: 404 });
    }

    // التحقق من الصلاحيات
    const userRole = session.user.role;
    const canDelete =
      userRole === "ADMIN" ||
      userRole === "HR" ||
      userRole === "MANAGER" ||
      (existingLeave.userId === session.user.id && existingLeave.status === "PENDING");

    if (!canDelete) {
      return NextResponse.json({ error: "غير مصرح لك بحذف هذا الطلب" }, { status: 403 });
    }

    // حذف طلب الإجازة
    await prisma.leaveRequest.delete({
      where: { id },
    });

    return NextResponse.json({ message: "تم حذف طلب الإجازة بنجاح" });
  } catch (error) {
    console.error("خطأ في حذف طلب الإجازة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في حذف طلب الإجازة" },
      { status: 500 }
    );
  }
}
