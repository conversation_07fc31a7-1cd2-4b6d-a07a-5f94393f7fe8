import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function POST() {
  try {
    // الحصول على الجلسة الحالية
    const session = await getServerSession(authOptions);
    
    if (session) {
      // لا نحتاج لعمل أي شيء خاص، NextAuth سيدير الجلسة
      console.log(`تسجيل خروج للمستخدم: ${session.user?.name}`);
    }

    // إرجاع استجابة سريعة
    return NextResponse.json({ 
      message: "تم تسجيل الخروج بنجاح",
      success: true 
    }, { status: 200 });

  } catch (error) {
    console.error("خطأ في تسجيل الخروج:", error);
    
    // حتى لو حدث خطأ، نعتبر العملية ناجحة
    return NextResponse.json({ 
      message: "تم تسجيل الخروج",
      success: true 
    }, { status: 200 });
  }
} 
