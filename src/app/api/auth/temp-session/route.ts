import { NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function GET() {
  try {
    const cookieStore = await cookies();
    const tempSession = cookieStore.get('temp-session');

    if (!tempSession) {
      return NextResponse.json({ session: null }, { status: 200 });
    }

    // Decode the session token
    try {
      const sessionData = JSON.parse(Buffer.from(tempSession.value, 'base64').toString('utf-8'));
      
      // Check if expired
      if (Date.now() > sessionData.exp) {
        return NextResponse.json({ session: null }, { status: 200 });
      }

      return NextResponse.json({ 
        session: {
          user: sessionData.user,
          expires: new Date(sessionData.exp).toISOString()
        }
      });

    } catch (decodeError) {
      console.error('❌ خطأ في فك تشفير الجلسة المؤقتة:', decodeError);
      return NextResponse.json({ session: null }, { status: 200 });
    }

  } catch (error) {
    console.error("❌ خطأ في قراءة الجلسة المؤقتة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في الخادم" },
      { status: 500 }
    );
  }
} 
