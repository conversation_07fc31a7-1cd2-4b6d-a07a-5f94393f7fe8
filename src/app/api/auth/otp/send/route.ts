import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { whatsappService } from "@/lib/services/whatsappService";

// توليد رمز OTP عشوائي (4 أرقام مثل Python)
function generateOTP(length: number = 4): string {
  const digits = '0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  return result;
}

// تنسيق رقم الهاتف العماني
function formatPhoneNumber(phone: string): { formatted: string; isValid: boolean } {
  // تنظيف رقم الهاتف من الرموز والمسافات
  const cleanPhone = phone.replace(/[^\d]/g, '');

  // Debug: removed console.log
let formattedPhone = cleanPhone;

  // إزالة الرمز + إذا كان موجوداً في البداية
  if (phone.startsWith('+')) {
    formattedPhone = cleanPhone;
  }

  // التحقق من أن الرقم يبدأ برمز الدولة (968 للعمان)
  if (!formattedPhone.startsWith('968') && formattedPhone.startsWith('0')) {
    formattedPhone = '968' + formattedPhone.slice(1);
  } else if (!formattedPhone.startsWith('968') && formattedPhone.length === 8) {
    formattedPhone = '968' + formattedPhone;
  }

  // التحقق من صحة الرقم (يجب أن يكون 11 رقم ويبدأ بـ 968)
  const isValid = formattedPhone.startsWith('968') && formattedPhone.length === 11;

  // Debug: removed console.log
return { formatted: formattedPhone, isValid };
}

// إنشاء OTP جديد وإرساله
async function createNewOTP(user: any, phone: string, expiryMinutes: number = 10) {
  // إنشاء رمز جديد (4 أرقام مثل Python)
  const otpCode = generateOTP(4);
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

  // Debug: removed console.log

  // حذف أي OTP قديم غير مستخدم لنفس الرقم
  await prisma.oTP.deleteMany({
    where: {
      phone: phone,
      used: false
    }
  });

  // Debug: removed console.log
// إنشاء OTP جديد في قاعدة البيانات
  const newOtp = await prisma.oTP.create({
    data: {
      phone: phone,
      code: otpCode,
      expiresAt,
      used: false
    }
  });

  // Debug: removed console.log
// إرسال OTP عبر WhatsApp
  const sendResult = await sendOTPViaWhatsApp(user, otpCode);

  return { otp: newOtp, success: sendResult };
}

// إرسال OTP عبر WhatsApp مع قالب مخصص
async function sendOTPViaWhatsApp(user: any, otpCode: string): Promise<boolean> {
  try {
    // الحصول على الإعدادات
    const settings = await prisma.settings.findFirst();
    const siteName = settings?.companyName || "نظام إدارة الحضور";
    const userName = user.name || user.email;
    const expiryMinutes = 10;

    // استخدام قالب من الإعدادات أو القالب الافتراضي
    let message: string;
    if (settings?.otpTemplate) {
      // Debug: removed console.log
// استبدال المتغيرات في القالب (دعم المتغيرات الجديدة والقديمة)
      message = settings.otpTemplate
        .replace('{site_name}', siteName)
        .replace('{user_name}', userName)
        .replace('{otp_code}', otpCode)  // المتغير الجديد
        .replace('{otp}', otpCode)       // المتغير القديم للتوافق مع النظام السابق
        .replace('{expiry_minutes}', expiryMinutes.toString());
        
      // Debug: removed console.log
} else {
      // القالب الافتراضي مثل Python
      message = `*رمز التحقق من ${siteName}*

مرحباً ${userName}،

رمز التحقق الخاص بك هو: *${otpCode}*

هذا الرمز صالح لمدة ${expiryMinutes} دقائق فقط.
يرجى عدم مشاركة هذا الرمز مع أي شخص.

شكراً لك.`;
    }

    // Debug: removed console.log
// إرسال الرسالة باستخدام خدمة WhatsApp
    const result = await whatsappService.sendMessage({
      to: user.phone,
      message: message
    });

    // Debug: removed console.log
return result.success;
  } catch (error) {
    console.error('❌ خطأ في إرسال OTP عبر WhatsApp:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { phone } = body;

    // Debug: removed console.log
if (!phone) {
      return NextResponse.json(
        { error: "رقم الهاتف مطلوب" },
        { status: 400 }
      );
    }

    // تنسيق رقم الهاتف
    const { formatted: formattedPhone, isValid } = formatPhoneNumber(phone);

    if (!isValid) {
      return NextResponse.json(
        { error: "رقم الهاتف غير صالح. يجب أن يكون رقم عماني صحيح" },
        { status: 400 }
      );
    }

    // البحث عن المستخدم برقم الهاتف (البحث في آخر 8 أرقام)
    const searchPhone = formattedPhone.slice(-8);
    const user = await prisma.user.findFirst({
      where: {
        phone: {
          contains: searchPhone
        }
      }
    });

    // Debug: removed console.log
if (!user) {
      return NextResponse.json(
        { error: "رقم الهاتف غير مسجل في النظام" },
        { status: 404 }
      );
    }

    // إنشاء OTP جديد وإرساله (10 دقائق انتهاء صلاحية مثل Python)
    const { otp, success } = await createNewOTP(user, formattedPhone, 10);

    if (!success) {
      // في حالة فشل إرسال WhatsApp، نحذف OTP ونعيد رسالة خطأ
      await prisma.oTP.delete({
        where: {
          id: otp.id
        }
      });

      // Debug: removed console.log
return NextResponse.json(
        { error: "فشل في إرسال رمز التحقق عبر WhatsApp. تحقق من إعدادات WhatsApp." },
        { status: 500 }
      );
    }

    // Debug: removed console.log
return NextResponse.json({
      success: true,
      message: "تم إرسال رمز التحقق إلى رقم هاتفك عبر WhatsApp",
      phone: formattedPhone,
      expiresIn: "10 دقائق"
    });

  } catch (error) {
    console.error("❌ خطأ في إرسال OTP:", error);
    return NextResponse.json(
      { error: "حدث خطأ في الخادم" },
      { status: 500 }
    );
  }
}
