import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// تنسيق رقم الهاتف العماني (نفس الدالة من send/route.ts)
function formatPhoneNumber(phone: string): { formatted: string; isValid: boolean } {
  const cleanPhone = phone.replace(/[^\d]/g, '');

  let formattedPhone = cleanPhone;

  if (phone.startsWith('+')) {
    formattedPhone = cleanPhone;
  }

  if (!formattedPhone.startsWith('968') && formattedPhone.startsWith('0')) {
    formattedPhone = '968' + formattedPhone.slice(1);
  } else if (!formattedPhone.startsWith('968') && formattedPhone.length === 8) {
    formattedPhone = '968' + formattedPhone;
  }

  const isValid = formattedPhone.startsWith('968') && formattedPhone.length === 11;

  return { formatted: formattedPhone, isValid };
}

// التحقق من صحة رمز OTP (مثل Python)
async function verifyOTP(phone: string, otpCode: string) {
  try {
    // Debug: removed console.log
// البحث عن آخر رمز تحقق غير مستخدم للرقم
    const otp = await prisma.oTP.findFirst({
      where: {
        phone: phone,
        code: otpCode,
        used: false,
        expiresAt: {
          gt: new Date() // لم تنته صلاحيته بعد
        }
      },
      orderBy: {
        createdAt: 'desc' // أحدث OTP
      }
    });

    // Debug: removed console.log

    if (!otp) {
      return { success: false, error: "رمز التحقق غير صالح أو منتهي الصلاحية" };
    }

    // تحديث حالة الرمز ليصبح مستخدماً (مثل Python)
    await prisma.oTP.update({
      where: {
        id: otp.id
      },
      data: {
        used: true
      }
    });

    // Debug: removed console.log
return { success: true, otp };
  } catch (error) {
    console.error('❌ خطأ في التحقق من OTP:', error);
    return { success: false, error: "حدث خطأ في التحقق من الرمز" };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { phone, code } = body;

    // Debug: removed console.log
if (!phone || !code) {
      return NextResponse.json(
        { error: "رقم الهاتف ورمز التحقق مطلوبان" },
        { status: 400 }
      );
    }

    // تنسيق رقم الهاتف
    const { formatted: formattedPhone, isValid } = formatPhoneNumber(phone);

    if (!isValid) {
      return NextResponse.json(
        { error: "رقم الهاتف غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من صحة رمز OTP
    const verificationResult = await verifyOTP(formattedPhone, code);

    if (!verificationResult.success) {
      return NextResponse.json(
        { error: verificationResult.error },
        { status: 400 }
      );
    }

    // البحث عن المستخدم برقم الهاتف (البحث في آخر 8 أرقام)
    const searchPhone = formattedPhone.slice(-8);
    const user = await prisma.user.findFirst({
      where: {
        phone: {
          contains: searchPhone
        }
      },
      include: {
        department: true
      }
    });

    // Debug: removed console.log
if (!user) {
      return NextResponse.json(
        { error: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // Debug: removed console.log
// إرجاع بيانات المستخدم للمصادقة
    return NextResponse.json({
      success: true,
      message: "تم التحقق من رمز OTP بنجاح",
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        phone: user.phone,
        employeeNumber: user.employeeNumber,
        position: user.position,
        department: user.department ? {
          id: user.department.id,
          name: user.department.name
        } : null
      }
    });

  } catch (error) {
    console.error("❌ خطأ في التحقق من OTP:", error);
    return NextResponse.json(
      { error: "حدث خطأ في الخادم" },
      { status: 500 }
    );
  }
}
