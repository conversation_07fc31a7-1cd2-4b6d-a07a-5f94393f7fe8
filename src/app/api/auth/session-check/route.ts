import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ 
        valid: false, 
        reason: "no_session" 
      }, { status: 401 });
    }

    // التحقق من انتهاء صلاحية الجلسة (4 ساعات)
    const now = Date.now();
    const lastActivity = session.lastActivity || 0;
    const maxInactivity = 4 * 60 * 60 * 1000; // 4 ساعات بالميلي ثانية

    if (now - lastActivity > maxInactivity) {
      return NextResponse.json({ 
        valid: false, 
        reason: "session_expired",
        message: "انتهت صلاحية الجلسة بسبب عدم النشاط"
      }, { status: 401 });
    }

    return NextResponse.json({ 
      valid: true, 
      user: session.user,
      lastActivity: session.lastActivity
    });

  } catch (error) {
    console.error("خطأ في فحص الجلسة:", error);
    return NextResponse.json({ 
      valid: false, 
      reason: "server_error",
      message: "حدث خطأ في الخادم"
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ 
        success: false, 
        reason: "no_session" 
      }, { status: 401 });
    }

    // We don't need to process the activity data since it's handled in JWT callback
    await request.json(); // Just consume the request body

    // تحديث آخر نشاط (يتم التعامل معه في JWT callback)
    return NextResponse.json({ 
      success: true, 
      message: "تم تحديث النشاط",
      timestamp: Date.now()
    });

  } catch (error) {
    console.error("خطأ في تحديث النشاط:", error);
    return NextResponse.json({ 
      success: false, 
      reason: "server_error",
      message: "حدث خطأ في الخادم"
    }, { status: 500 });
  }
} 