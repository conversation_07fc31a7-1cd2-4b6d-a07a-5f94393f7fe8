import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    // تاريخ اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // جلب تصاريح ما بعد العمل المعتمدة لليوم
    const permits = await prisma.afterHoursPermit.findMany({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: "APPROVED", // فقط التصاريح المعتمدة من الموارد البشرية
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
            position: true,
            department: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        startTime: "asc",
      },
    });

    return NextResponse.json(permits);
  } catch (error) {
    console.error("خطأ في جلب التصاريح المعتمدة:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب التصاريح المعتمدة" },
      { status: 500 }
    );
  }
}
