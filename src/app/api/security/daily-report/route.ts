import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateParam = searchParams.get("date");
    
    if (!dateParam) {
      return NextResponse.json({ error: "التاريخ مطلوب" }, { status: 400 });
    }

    const selectedDate = new Date(dateParam);
    const startOfDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());
    const endOfDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate() + 1);

    // إجمالي الموظفين
    const totalEmployees = await prisma.user.count({
      where: {
        role: {
          in: ["EMPLOYEE", "MANAGER", "HR"],
        },
      },
    });

    // الموظفون الحاضرون
    const presentEmployees = await prisma.attendanceRecord.groupBy({
      by: ["userId"],
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
    });

    // الموظفون المتأخرون (يمكن تحديد الوقت من الإعدادات)
    const settings = await prisma.settings.findFirst();
    let lateArrivals = 0;
    
    if (settings?.workStartTime) {
      const [hours, minutes] = settings.workStartTime.split(":");
      const workStartTime = new Date(startOfDay);
      workStartTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      
      lateArrivals = await prisma.attendanceRecord.count({
        where: {
          date: {
            gte: startOfDay,
            lt: endOfDay,
          },
          entryNumber: 1, // أول دخول في اليوم
          checkInTime: {
            gt: workStartTime,
          },
        },
      });
    }

    // الخروج المبكر
    const earlyDepartures = await prisma.attendanceRecord.count({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
        exitType: "PERSONAL",
      },
    });

    // إجمالي الزوار
    const totalVisitors = await prisma.visitorRequest.count({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: "APPROVED",
      },
    });

    // الزوار النشطون (داخل المبنى)
    const activeVisitors = await prisma.visitorRequest.count({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: "APPROVED",
        checkInTime: {
          not: null,
        },
        checkOutTime: null,
      },
    });

    // تصاريح العمل بعد الدوام
    const afterHoursPermits = await prisma.afterHoursPermit.count({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: "APPROVED",
      },
    });

    // الموظفون العاملون بعد الدوام حالياً
    const activeAfterHours = await prisma.afterHoursPermit.count({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: "APPROVED",
        checkInTime: {
          not: null,
        },
        checkOutTime: null,
      },
    });

    const report = {
      date: dateParam,
      totalEmployees,
      presentEmployees: presentEmployees.length,
      absentEmployees: totalEmployees - presentEmployees.length,
      lateArrivals,
      earlyDepartures,
      totalVisitors,
      activeVisitors,
      afterHoursPermits,
      activeAfterHours,
    };

    return NextResponse.json(report);
  } catch (error) {
    console.error("خطأ في جلب التقرير اليومي:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب التقرير اليومي" },
      { status: 500 }
    );
  }
}
