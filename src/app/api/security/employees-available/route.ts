import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

const prisma = new PrismaClient();

// GET - جلب الموظفين المتاحين للحضور (باستثناء الذين في إجازة معتمدة)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "غير مصرح" }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    // تاريخ اليوم
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // جلب الموظفين مع استثناء الذين في إجازة معتمدة اليوم
    const employees = await prisma.user.findMany({
      where: {
        role: {
          not: "ADMIN" // استثناء المدراء من كشف الحضور
        },
        leaveRequests: {
          none: {
            status: "APPROVED",
            startDate: {
              lte: tomorrow
            },
            endDate: {
              gte: today
            }
          }
        }
      },
      select: {
        id: true,
        employeeNumber: true,
        name: true,
        position: true,
        department: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(employees);
  } catch (error) {
    console.error("خطأ في جلب الموظفين المتاحين:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب الموظفين المتاحين" },
      { status: 500 }
    );
  }
} 