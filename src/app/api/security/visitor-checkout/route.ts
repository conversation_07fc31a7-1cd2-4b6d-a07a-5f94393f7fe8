import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { visitorId } = body;

    if (!visitorId) {
      return NextResponse.json({ error: "معرف الزائر مطلوب" }, { status: 400 });
    }

    // التحقق من وجود طلب الزيارة
    const visitorRequest = await prisma.visitorRequest.findUnique({
      where: { id: visitorId },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!visitorRequest) {
      return NextResponse.json({ error: "طلب الزيارة غير موجود" }, { status: 404 });
    }

    // التحقق من أن الزائر سجل دخوله
    if (!visitorRequest.checkInTime) {
      return NextResponse.json({ 
        error: "لم يتم تسجيل دخول الزائر بعد" 
      }, { status: 400 });
    }

    // التحقق من أن الزائر لم يسجل خروجه بالفعل
    if (visitorRequest.checkOutTime) {
      return NextResponse.json({ 
        error: "تم تسجيل خروج الزائر بالفعل" 
      }, { status: 400 });
    }

    // تحديث طلب الزيارة بوقت الخروج
    const updatedVisitorRequest = await prisma.visitorRequest.update({
      where: { id: visitorId },
      data: {
        checkOutTime: new Date(),
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تسجيل خروج الزائر بنجاح",
      visitor: updatedVisitorRequest,
    });

  } catch (error) {
    console.error("خطأ في تسجيل خروج الزائر:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل خروج الزائر" },
      { status: 500 }
    );
  }
}
