import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { whatsappService } from "@/lib/services/whatsappService";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { employeeId } = body;

    if (!employeeId) {
      return NextResponse.json({ error: "معرف الموظف مطلوب" }, { status: 400 });
    }

    // التحقق من وجود الموظف
    const employee = await prisma.user.findUnique({
      where: { id: employeeId },
    });

    if (!employee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    // تاريخ اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // البحث عن آخر سجل دخول بدون خروج
    const activeRecord = await prisma.attendanceRecord.findFirst({
      where: {
        userId: employeeId,
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
        checkOutTime: null,
      },
      orderBy: {
        entryNumber: "desc",
      },
    });

    if (!activeRecord) {
      return NextResponse.json({
        error: "لا يوجد سجل دخول نشط للموظف"
      }, { status: 400 });
    }

    // جلب إعدادات العمل لتحديد نوع الخروج التلقائي
    const settings = await prisma.settings.findFirst();
    let finalExitType: "OFFICIAL" | "PERSONAL" | "WORK" | "HEALTH" = "OFFICIAL"; // افتراضي

    if (settings && settings.workEndTime) {
      const workEndTime = new Date();
      const [hours, minutes] = settings.workEndTime.split(":");
      workEndTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // إذا كان الخروج قبل انتهاء الدوام، يُعتبر استئذان شخصي
      if (today < workEndTime) {
        finalExitType = "PERSONAL";
      }
    }

    // تحديث سجل الحضور
    const updatedRecord = await prisma.attendanceRecord.update({
      where: { id: activeRecord.id },
      data: {
        checkOutTime: new Date(),
        exitType: finalExitType,
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    // إرسال إشعار في حالة الخروج المبكر
    if (finalExitType === "PERSONAL" && settings?.whatsappEnabled) {
      try {
        // جلب بيانات المدير لإرسال الإشعار
        const employee = await prisma.user.findUnique({
          where: { id: employeeId },
          include: {
            manager: {
              select: {
                name: true,
                phone: true,
              },
            },
          },
        });

        if (employee?.manager?.phone) {
          const exitTime = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Muscat'
          });

          await whatsappService.sendEarlyExitNotification(
            employee.manager.phone,
            employee.name,
            exitTime
          );

          // Debug: removed console.log
        }
      } catch (notificationError) {
        console.error("خطأ في إرسال إشعار الخروج المبكر:", notificationError);
      }
    }

    return NextResponse.json({
      message: "تم تسجيل الخروج بنجاح",
      record: updatedRecord,
      exitType: finalExitType,
    });

  } catch (error) {
    console.error("خطأ في تسجيل الخروج:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل الخروج" },
      { status: 500 }
    );
  }
}
