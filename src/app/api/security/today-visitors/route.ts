import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    // تاريخ اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // جلب زوار اليوم
    const visitors = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        // إزالة شرط الاعتماد - جميع الزوار
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(visitors);
  } catch (error) {
    console.error("خطأ في جلب زوار اليوم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب زوار اليوم" },
      { status: 500 }
    );
  }
}
