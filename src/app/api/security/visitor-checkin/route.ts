import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { whatsappService } from "@/lib/services/whatsappService";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { visitorId } = body;

    if (!visitorId) {
      return NextResponse.json({ error: "معرف الزائر مطلوب" }, { status: 400 });
    }

    // التحقق من وجود طلب الزيارة
    const visitorRequest = await prisma.visitorRequest.findUnique({
      where: { id: visitorId },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!visitorRequest) {
      return NextResponse.json({ error: "طلب الزيارة غير موجود" }, { status: 404 });
    }

    // إزالة شرط الاعتماد - جميع الزوار مسموح لهم بالدخول
    // if (visitorRequest.status !== "APPROVED") {
    //   return NextResponse.json({
    //     error: "طلب الزيارة غير معتمد"
    //   }, { status: 400 });
    // }

    // التحقق من أن الزائر لم يسجل دخوله بالفعل
    if (visitorRequest.checkInTime) {
      return NextResponse.json({
        error: "تم تسجيل دخول الزائر بالفعل"
      }, { status: 400 });
    }

    // التحقق من أن تاريخ الزيارة هو اليوم
    const today = new Date();
    const visitDate = new Date(visitorRequest.visitDate);
    const isToday = today.toDateString() === visitDate.toDateString();

    if (!isToday) {
      return NextResponse.json({
        error: "تاريخ الزيارة ليس اليوم"
      }, { status: 400 });
    }

    // تحديث طلب الزيارة بوقت الدخول
    const updatedVisitorRequest = await prisma.visitorRequest.update({
      where: { id: visitorId },
      data: {
        checkInTime: new Date(),
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    // إرسال إشعار للموظف المضيف
    try {
      const settings = await prisma.settings.findFirst();
      if (settings?.whatsappEnabled) {
        // جلب رقم هاتف الموظف المضيف
        const hostEmployee = await prisma.user.findUnique({
          where: { id: visitorRequest.userId },
          select: {
            phone: true,
            name: true,
          },
        });

        if (hostEmployee?.phone) {
          await whatsappService.sendVisitorArrivalNotification(
            hostEmployee.phone,
            visitorRequest.visitorName,
            visitorRequest.purpose
          );

          // Debug: removed console.log
}
      }
    } catch (notificationError) {
      console.error("خطأ في إرسال إشعار وصول الزائر:", notificationError);
    }

    return NextResponse.json({
      message: "تم تسجيل دخول الزائر بنجاح",
      visitor: updatedVisitorRequest,
    });

  } catch (error) {
    console.error("خطأ في تسجيل دخول الزائر:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل دخول الزائر" },
      { status: 500 }
    );
  }
}
