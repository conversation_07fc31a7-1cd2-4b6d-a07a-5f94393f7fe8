import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { permitId } = body;

    if (!permitId) {
      return NextResponse.json({ error: "معرف التصريح مطلوب" }, { status: 400 });
    }

    // التحقق من وجود التصريح
    const permit = await prisma.afterHoursPermit.findUnique({
      where: { id: permitId },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!permit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    // التحقق من أن الموظف سجل دخوله
    if (!permit.checkInTime) {
      return NextResponse.json({ 
        error: "لم يتم تسجيل دخول الموظف بعد" 
      }, { status: 400 });
    }

    // التحقق من أن الموظف لم يسجل خروجه بالفعل
    if (permit.checkOutTime) {
      return NextResponse.json({ 
        error: "تم تسجيل خروج الموظف بالفعل" 
      }, { status: 400 });
    }

    // تحديث التصريح بوقت الخروج
    const updatedPermit = await prisma.afterHoursPermit.update({
      where: { id: permitId },
      data: {
        checkOutTime: new Date(),
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تسجيل خروج الموظف من العمل بعد الدوام بنجاح",
      permit: updatedPermit,
    });

  } catch (error) {
    console.error("خطأ في تسجيل خروج العمل بعد الدوام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل خروج العمل بعد الدوام" },
      { status: 500 }
    );
  }
}
