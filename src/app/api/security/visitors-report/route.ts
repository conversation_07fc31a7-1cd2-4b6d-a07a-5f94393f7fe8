import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateParam = searchParams.get("date");

    if (!dateParam) {
      return NextResponse.json({ error: "التاريخ مطلوب" }, { status: 400 });
    }

    const selectedDate = new Date(dateParam);
    const startOfDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());
    const endOfDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate() + 1);

    // جلب سجلات الزوار للتاريخ المحدد
    const visitorRecords = await prisma.visitorRequest.findMany({
      where: {
        visitDate: {
          gte: startOfDay,
          lt: endOfDay,
        },
        // إزالة شرط الاعتماد - جميع الزوار
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(visitorRecords);
  } catch (error) {
    console.error("خطأ في جلب تقرير الزوار:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب تقرير الزوار" },
      { status: 500 }
    );
  }
}
