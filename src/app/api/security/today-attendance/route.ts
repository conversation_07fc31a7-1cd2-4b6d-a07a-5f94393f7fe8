import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    // تاريخ اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // جلب سجلات الحضور لليوم
    const attendance = await prisma.attendanceRecord.findMany({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
      orderBy: [
        { userId: "asc" },
        { entryNumber: "asc" },
      ],
    });

    return NextResponse.json(attendance);
  } catch (error) {
    console.error("خطأ في جلب حضور اليوم:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب حضور اليوم" },
      { status: 500 }
    );
  }
}
