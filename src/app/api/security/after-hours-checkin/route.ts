import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { permitId } = body;

    if (!permitId) {
      return NextResponse.json({ error: "معرف التصريح مطلوب" }, { status: 400 });
    }

    // التحقق من وجود التصريح
    const permit = await prisma.afterHoursPermit.findUnique({
      where: { id: permitId },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    if (!permit) {
      return NextResponse.json({ error: "التصريح غير موجود" }, { status: 404 });
    }

    // التحقق من أن التصريح معتمد من الموارد البشرية
    if (permit.status !== "APPROVED") {
      return NextResponse.json({ 
        error: "التصريح غير معتمد من الموارد البشرية" 
      }, { status: 400 });
    }

    // التحقق من أن الموظف لم يسجل دخوله بالفعل
    if (permit.checkInTime) {
      return NextResponse.json({ 
        error: "تم تسجيل دخول الموظف بالفعل" 
      }, { status: 400 });
    }

    // التحقق من أن تاريخ التصريح هو اليوم
    const today = new Date();
    const permitDate = new Date(permit.date);
    const isToday = today.toDateString() === permitDate.toDateString();

    if (!isToday) {
      return NextResponse.json({ 
        error: "تاريخ التصريح ليس اليوم" 
      }, { status: 400 });
    }

    // ملاحظة: تم إزالة التحقق من الوقت للسماح بالدخول في أي وقت

    // تحديث التصريح بوقت الدخول
    const updatedPermit = await prisma.afterHoursPermit.update({
      where: { id: permitId },
      data: {
        checkInTime: new Date(),
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تسجيل دخول الموظف للعمل بعد الدوام بنجاح",
      permit: updatedPermit,
    });

  } catch (error) {
    console.error("خطأ في تسجيل دخول العمل بعد الدوام:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل دخول العمل بعد الدوام" },
      { status: 500 }
    );
  }
}
