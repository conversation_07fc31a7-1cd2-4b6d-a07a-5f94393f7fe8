import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (userRole !== "SECURITY" && userRole !== "ADMIN") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const body = await request.json();
    const { employeeId } = body;

    if (!employeeId) {
      return NextResponse.json({ error: "معرف الموظف مطلوب" }, { status: 400 });
    }

    // جلب الإعدادات للحصول على ساعات العمل
    const settings = await prisma.settings.findFirst();
    const workingHours = settings?.workingHours || 7;

    // التحقق من وجود الموظف
    const employee = await prisma.user.findUnique({
      where: { id: employeeId },
      select: { id: true, name: true, employeeNumber: true }
    });

    if (!employee) {
      return NextResponse.json({ error: "الموظف غير موجود" }, { status: 404 });
    }

    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // البحث عن آخر سجل حضور لهذا الموظف في نفس اليوم
    const existingRecords = await prisma.attendanceRecord.findMany({
      where: {
        userId: employeeId,
        date: startOfDay,
      },
      orderBy: { entryNumber: 'desc' },
    });

    let entryNumber = 1;
    if (existingRecords.length > 0) {
      const lastRecord = existingRecords[0];
      
      // إذا كان آخر سجل لم يخرج بعد، لا يمكن تسجيل دخول جديد
      if (!lastRecord.checkOutTime) {
        return NextResponse.json({ 
          error: "الموظف موجود بالفعل في العمل ولم يسجل خروج" 
        }, { status: 400 });
      }
      
      // إذا وصل إلى الحد الأقصى للحركات (4 حركات)
      if (lastRecord.entryNumber >= 4) {
        return NextResponse.json({ 
          error: "تم الوصول للحد الأقصى من الحركات اليومية (4 حركات)" 
        }, { status: 400 });
      }
      
      entryNumber = lastRecord.entryNumber + 1;
    }

    // حساب وقت انتهاء العمل المتوقع = وقت الدخول + ساعات العمل
    const checkInTime = new Date();
    const expectedEndTime = new Date(checkInTime.getTime() + (workingHours * 60 * 60 * 1000));

    // إنشاء سجل حضور جديد مع وقت انتهاء العمل المتوقع
    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        userId: employeeId,
        date: startOfDay,
        checkInTime: checkInTime,
        entryNumber,
        recordedBy: session.user.id,
        emp_working_hrs: expectedEndTime.getTime(), // حفظ وقت انتهاء العمل المتوقع كـ timestamp
      },
      include: {
        user: {
          select: {
            name: true,
            employeeNumber: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "تم تسجيل الدخول بنجاح",
      record: {
        ...attendanceRecord,
        expectedEndTime: expectedEndTime.toISOString(), // إضافة وقت انتهاء العمل المتوقع للاستجابة
      },
      // معلومات إضافية للتحقق من صحة الحسابات
      debug: {
        checkInTime: checkInTime.toISOString(),
        expectedEndTime: expectedEndTime.toISOString(),
        checkInTimeLocal: checkInTime.toLocaleString('ar-SA', {timeZone: 'Asia/Muscat'}),
        expectedEndTimeLocal: expectedEndTime.toLocaleString('ar-SA', {timeZone: 'Asia/Muscat'}),
        workingHours: workingHours,
        empWorkingHrsTimestamp: expectedEndTime.getTime(),
        calculationCorrect: (expectedEndTime.getTime() - checkInTime.getTime()) === (workingHours * 60 * 60 * 1000)
      }
    });
  } catch (error) {
    console.error("خطأ في تسجيل الحضور:", error);
    return NextResponse.json(
      { error: "حدث خطأ في تسجيل الحضور" },
      { status: 500 }
    );
  }
}
