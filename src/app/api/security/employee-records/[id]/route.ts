import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "غير مصرح لك بالوصول" }, { status: 401 });
    }

    // التحقق من صلاحية الأمن
    if (session.user.role !== "SECURITY") {
      return NextResponse.json({ error: "ليس لديك صلاحية للوصول" }, { status: 403 });
    }

    const resolvedParams = await params;
    const employeeId = resolvedParams.id;

    // تاريخ اليوم
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // جلب سجلات اليوم للموظف
    const records = await prisma.attendanceRecord.findMany({
      where: {
        userId: employeeId,
        date: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      orderBy: {
        entryNumber: "asc",
      },
    });

    return NextResponse.json(records);
  } catch (error) {
    console.error("خطأ في جلب سجلات الموظف:", error);
    return NextResponse.json(
      { error: "حدث خطأ في جلب سجلات الموظف" },
      { status: 500 }
    );
  }
}
