'use client';

import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // تسجيل الخطأ العام
    console.error('Global application error:', error);
  }, [error]);

  return (
    <html lang="ar" dir="rtl">
      <body>
        <div className="min-h-screen flex items-center justify-center bg-red-50">
          <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md mx-4 border border-red-200">
            <div className="text-center">
              <div className="text-red-500 text-6xl mb-4">🚨</div>
              <h2 className="text-2xl font-bold text-red-900 mb-4">
                خطأ عام في النظام
              </h2>
              <p className="text-red-700 mb-6">
                حدث خطأ خطير في النظام. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم الفني.
              </p>
              <div className="space-y-3">
                <button
                  onClick={reset}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  إعادة المحاولة
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  إعادة تحميل الصفحة
                </button>
              </div>
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm text-red-500">
                    تفاصيل الخطأ العام (وضع التطوير)
                  </summary>
                  <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto text-red-800">
                    {error.message}
                    {error.stack && '\n\n' + error.stack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
