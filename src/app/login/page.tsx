"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { usePublicSettings } from "@/providers/settings-provider";
import { FaPhone, FaEnvelope, FaLock, FaKey, FaExclamationTriangle } from "react-icons/fa";

const loginSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صحيح"),
  password: z.string().min(1, "كلمة المرور مطلوبة"),
});

const otpSchema = z.object({
  phone: z.string().min(8, "رقم الهاتف يجب أن يكون 8 أرقام على الأقل").max(8, "رقم الهاتف يجب أن يكون 8 أرقام"),
  code: z.string().min(4, "رمز التحقق يجب أن يكون 4 أرقام على الأقل"),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type OTPFormValues = z.infer<typeof otpSchema>;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { settings, loading: settingsLoading } = usePublicSettings();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginMethod, setLoginMethod] = useState<'password' | 'otp'>('password');
  const [otpSent, setOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [otpPhone, setOtpPhone] = useState<string>(''); // الرقم المنسق للـ API
  const [otpPhoneDisplay, setOtpPhoneDisplay] = useState<string>(''); // الرقم للعرض

  // التحقق من انتهاء صلاحية الجلسة
  const isSessionExpired = searchParams?.get('expired') === 'true';

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  const {
    register: registerOtp,
    handleSubmit: handleSubmitOtp,
    formState: { errors: errorsOtp },
    setValue: setValueOtp,
    watch: watchOtp,
  } = useForm<OTPFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      phone: "",
      code: "",
    },
  });

  // تحديث عنوان الصفحة عند تحميل الإعدادات
  useEffect(() => {
    if (!settingsLoading && settings?.companyName) {
      document.title = `تسجيل الدخول - ${settings.companyName}`;
    }
  }, [settings, settingsLoading]);

  // تنظيف البيانات المحلية عند تحميل صفحة تسجيل الدخول
  useEffect(() => {
    // تنظيف بيانات النشاط المحفوظة
    localStorage.removeItem('lastActivity');
    sessionStorage.clear();
    
    // عرض رسالة انتهاء الجلسة إذا كانت موجودة
    if (isSessionExpired) {
      setError("انتهت صلاحية جلستك بسبب عدم النشاط. يرجى تسجيل الدخول مرة أخرى.");
    }
  }, [isSessionExpired]);

  // العد التنازلي لإعادة الإرسال
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await signIn("credentials", {
        redirect: false,
        email: data.email,
        password: data.password,
      });

      if (result?.error) {
        setError(result.error);
      } else {
        // تنظيف رسالة انتهاء الجلسة عند نجاح تسجيل الدخول
        router.push("/dashboard");
        router.refresh();
      }
    } catch (error) {
      setError("حدث خطأ أثناء تسجيل الدخول: " + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  const sendOTP = async (phone: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/otp/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone }),
      });

      const result = await response.json();
      
      if (response.ok) {
        setOtpSent(true);
        setOtpPhone(result.phone || phone); // الرقم المنسق (11 رقم) للـ API
        setOtpPhoneDisplay(phone); // الرقم الأصلي (8 أرقام) للعرض
        setCountdown(300); // 5 دقائق
        setValueOtp('phone', phone); // استخدام الرقم الأصلي في الفورم
      } else {
        setError(result.error);
      }
    } catch (error) {
      setError("حدث خطأ في إرسال رمز التحقق");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmitOTP = async (data: OTPFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the same phone that was used to send OTP (from otpPhone state)
      const phoneToUse = otpPhone || data.phone;
      
      // إضافة timeout للحماية من التأخير الطويل
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.')), 30000); // 30 ثانية
      });
      
      // Use NextAuth.js signIn with OTP provider
      const signInPromise = signIn("otp", {
        redirect: false,
        phone: phoneToUse,
        code: data.code,
      });
      
      // استخدام Promise.race للحماية من التأخير
      const result = await Promise.race([signInPromise, timeoutPromise]) as any;

      if (result?.error) {
        setError(result.error);
      } else if (result?.ok) {
        // Success! Redirect to dashboard
        router.push("/dashboard");
        router.refresh();
      } else {
        setError("حدث خطأ غير متوقع في تسجيل الدخول");
      }

    } catch (error) {
      setError("حدث خطأ أثناء تسجيل الدخول: " + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 w-full max-w-md">
        {/* الشعار */}
        {settings?.logo && (
          <div className="text-center mb-6">
            <img
              src={settings.logo}
              alt={settings.companyName || "شعار الشركة"}
              className="h-16 w-auto mx-auto object-contain"
            />
          </div>
        )}

        <h1 className="text-2xl font-bold text-center mb-2">تسجيل الدخول</h1>
        {settings?.companyName && (
          <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
            {settings.companyName}
          </p>
        )}

        {/* رسالة انتهاء الجلسة */}
        {isSessionExpired && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4 flex items-center">
            <FaExclamationTriangle className="ml-2" />
            <span>انتهت صلاحية جلستك بسبب عدم النشاط. يرجى تسجيل الدخول مرة أخرى.</span>
          </div>
        )}

        {/* أزرار اختيار طريقة تسجيل الدخول */}
        <div className="flex mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            type="button"
            onClick={() => {
              setLoginMethod('password');
              setOtpSent(false);
              setError(null);
            }}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              loginMethod === 'password'
                ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FaEnvelope className="inline ml-2" />
            كلمة المرور
          </button>
          <button
            type="button"
            onClick={() => {
              setLoginMethod('otp');
              setOtpSent(false);
              setError(null);
            }}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              loginMethod === 'otp'
                ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FaPhone className="inline ml-2" />
            رمز التحقق
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* نموذج تسجيل الدخول بكلمة المرور */}
        {loginMethod === 'password' && (
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-4">
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                <FaEnvelope className="inline ml-2" />
                البريد الإلكتروني
              </label>
              <input
                type="email"
                className="input"
                {...register("email")}
                disabled={isLoading}
                placeholder="أدخل بريدك الإلكتروني"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div className="mb-6">
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                <FaLock className="inline ml-2" />
                كلمة المرور
              </label>
              <input
                type="password"
                className="input"
                {...register("password")}
                disabled={isLoading}
                placeholder="أدخل كلمة المرور"
              />
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            <button
              type="submit"
              className="btn-primary w-full"
              disabled={isLoading}
            >
              {isLoading ? "جاري تسجيل الدخول..." : "تسجيل الدخول"}
            </button>
          </form>
        )}

        {/* نموذج تسجيل الدخول بـ OTP */}
        {loginMethod === 'otp' && (
          <div>
            {!otpSent ? (
              <div>
                <div className="mb-6">
                  <label className="block text-gray-700 dark:text-gray-300 mb-2">
                    <FaPhone className="inline ml-2" />
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    className="input"
                    {...registerOtp("phone")}
                    disabled={isLoading}
                    placeholder="أدخل رقم هاتفك (8 أرقام)"
                    maxLength={8}
                    dir="ltr"
                  />
                  {errorsOtp.phone && (
                    <p className="text-red-500 text-sm mt-1">{errorsOtp.phone.message}</p>
                  )}
                  <p className="text-gray-500 text-xs mt-1">
                    سيتم إرسال رمز التحقق عبر WhatsApp
                  </p>
                </div>

                <button
                  type="button"
                  onClick={async () => {
                    const phoneValue = (document.querySelector('input[type="tel"]') as HTMLInputElement)?.value;
                    if (phoneValue && phoneValue.length === 8) {
                      await sendOTP(phoneValue);
                    } else {
                      setError('يرجى إدخال رقم هاتف مكون من 8 أرقام');
                    }
                  }}
                  className="btn-primary w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "جاري الإرسال..." : "إرسال رمز التحقق"}
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmitOtp(onSubmitOTP)}>
                <div className="mb-4">
                  <label className="block text-gray-700 dark:text-gray-300 mb-2">
                    <FaPhone className="inline ml-2" />
                    رقم الهاتف
                  </label>
                  <input
                    type="text"
                    className="input bg-gray-100"
                    value={otpPhoneDisplay}
                    disabled
                  />
                </div>

                <div className="mb-6">
                  <label className="block text-gray-700 dark:text-gray-300 mb-2">
                    <FaKey className="inline ml-2" />
                    رمز التحقق
                  </label>
                  <input
                    type="text"
                    className="input text-center text-lg tracking-widest"
                    {...registerOtp("code")}
                    disabled={isLoading}
                    placeholder="0000"
                    maxLength={4}
                    onChange={(e) => {
                      registerOtp("code").onChange(e);
                    }}
                  />
                  {errorsOtp.code && (
                    <p className="text-red-500 text-sm mt-1">{errorsOtp.code.message}</p>
                  )}
                  <div className="flex justify-between items-center mt-2">
                    <p className="text-gray-500 text-xs">
                      تم إرسال الرمز إلى رقم هاتفك
                    </p>
                    {countdown > 0 ? (
                      <p className="text-blue-600 text-xs">
                        إعادة الإرسال خلال {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
                      </p>
                    ) : (
                      <button
                        type="button"
                        onClick={() => sendOTP(otpPhoneDisplay)}
                        className="text-blue-600 text-xs hover:underline"
                        disabled={isLoading}
                      >
                        إعادة الإرسال
                      </button>
                    )}
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => {
                      setOtpSent(false);
                      setCountdown(0);
                      setOtpPhone('');
                      setOtpPhoneDisplay('');
                      setError(null);
                    }}
                    className="btn-secondary flex-1"
                    disabled={isLoading}
                  >
                    تغيير الرقم
                  </button>
                  <button
                    type="submit"
                    className="btn-primary flex-1"
                    disabled={isLoading}
                  >
                    {isLoading ? "جاري التحقق..." : "تسجيل الدخول"}
                  </button>
                </div>
              </form>
            )}
          </div>
        )}

        {/* معلومات الأمان */}
        <div className="mt-6 p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-600 text-center">
            🔒 لأمانك، ستنتهي صلاحية جلستك تلقائياً بعد 4 ساعات من عدم النشاط
          </p>
        </div>
      </div>
    </div>
  );
}
