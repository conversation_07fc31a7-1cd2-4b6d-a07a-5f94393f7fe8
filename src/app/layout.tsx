import type { Metadata } from "next";
import "./globals.css";
import "../styles/fonts.css";
import { AuthProvider } from "@/providers/auth-provider";
import { ThemeProvider } from "@/providers/theme-provider";
import { SettingsProvider } from "@/providers/settings-provider";
import DynamicFavicon from "@/components/DynamicFavicon";
import ClientBodyClass from "@/components/ClientBodyClass";

export const metadata: Metadata = {
  title: "نظام الموارد البشرية",
  description: "نظام إدارة الموارد البشرية والحضور والانصراف",
  robots: {
    index: false,
    follow: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        {/* Static favicon fallback - will be replaced by DynamicFavicon */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        
        <link rel="preload" href="/fonts/tajawal-400.ttf" as="font" type="font/ttf" crossOrigin="anonymous" />
        <link rel="preload" href="/fonts/tajawal-500.ttf" as="font" type="font/ttf" crossOrigin="anonymous" />
        <link rel="preload" href="/fonts/tajawal-700.ttf" as="font" type="font/ttf" crossOrigin="anonymous" />
        
        <meta httpEquiv="x-dns-prefetch-control" content="off" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="format-detection" content="date=no" />
        <meta name="format-detection" content="address=no" />
        <meta name="format-detection" content="email=no" />
        
        <DynamicFavicon />
      </head>
      <body suppressHydrationWarning={true}>
        <ClientBodyClass />
        <SettingsProvider>
          <AuthProvider>
            <ThemeProvider>
              {children}
            </ThemeProvider>
          </AuthProvider>
        </SettingsProvider>
      </body>
    </html>
  );
}
