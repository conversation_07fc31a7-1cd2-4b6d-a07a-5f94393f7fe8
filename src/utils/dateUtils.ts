import { format, parseISO } from "date-fns";

/**
 * تنسيق التاريخ بصيغة dd/mm/yyyy
 * @param date - التاريخ المراد تنسيقه (Date object أو string)
 * @returns التاريخ منسق بصيغة dd/mm/yyyy
 */
export function formatDate(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, "dd/MM/yyyy");
  } catch (error) {
    console.error("خطأ في تنسيق التاريخ:", error);
    return "تاريخ غير صالح";
  }
}

/**
 * تنسيق التاريخ والوقت بصيغة dd/mm/yyyy HH:mm
 * @param date - التاريخ المراد تنسيقه (Date object أو string)
 * @returns التاريخ والوقت منسق بصيغة dd/mm/yyyy HH:mm
 */
export function formatDateTime(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, "dd/MM/yyyy HH:mm");
  } catch (error) {
    console.error("خطأ في تنسيق التاريخ والوقت:", error);
    return "تاريخ غير صالح";
  }
}

/**
 * تنسيق الوقت فقط بصيغة HH:mm
 * @param date - التاريخ المراد تنسيقه (Date object أو string)
 * @returns الوقت منسق بصيغة HH:mm
 */
export function formatTime(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, "HH:mm");
  } catch (error) {
    console.error("خطأ في تنسيق الوقت:", error);
    return "وقت غير صالح";
  }
}

/**
 * الحصول على التاريخ الحالي بصيغة dd/mm/yyyy
 * @returns التاريخ الحالي منسق بصيغة dd/mm/yyyy
 */
export function getCurrentDate(): string {
  return formatDate(new Date());
}

/**
 * الحصول على التاريخ والوقت الحالي بصيغة dd/mm/yyyy HH:mm
 * @returns التاريخ والوقت الحالي منسق بصيغة dd/mm/yyyy HH:mm
 */
export function getCurrentDateTime(): string {
  return formatDateTime(new Date());
}

/**
 * تحويل التاريخ من صيغة dd/mm/yyyy إلى Date object
 * @param dateString - التاريخ بصيغة dd/mm/yyyy
 * @returns Date object أو null في حالة الخطأ
 */
export function parseDate(dateString: string): Date | null {
  try {
    const parts = dateString.split('/');
    if (parts.length !== 3) return null;
    
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // الشهر يبدأ من 0
    const year = parseInt(parts[2], 10);
    
    const date = new Date(year, month, day);
    
    // التحقق من صحة التاريخ
    if (date.getDate() !== day || date.getMonth() !== month || date.getFullYear() !== year) {
      return null;
    }
    
    return date;
  } catch (error) {
    console.error("خطأ في تحليل التاريخ:", error);
    return null;
  }
}

/**
 * التحقق من صحة التاريخ بصيغة dd/mm/yyyy
 * @param dateString - التاريخ المراد التحقق منه
 * @returns true إذا كان التاريخ صحيح، false إذا كان غير صحيح
 */
export function isValidDate(dateString: string): boolean {
  return parseDate(dateString) !== null;
}
