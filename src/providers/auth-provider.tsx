"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";

// مكون مراقبة الجلسة
function SessionMonitor({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    let inactivityTimer: NodeJS.Timeout;
    let sessionCheckInterval: NodeJS.Timeout;
    
    // مدة عدم النشاط المسموحة (4 ساعات)
    const INACTIVITY_TIMEOUT = 4 * 60 * 60 * 1000; // 4 ساعات بالميلي ثانية
    
    // فحص الجلسة كل ساعة
    const SESSION_CHECK_INTERVAL = 60 * 60 * 1000; // ساعة واحدة
    
    // تتبع آخر نشاط
    let lastActivity = Date.now();

    // تحديث آخر نشاط
    const updateActivity = () => {
      lastActivity = Date.now();
      localStorage.setItem('lastActivity', lastActivity.toString());
    };

    // فحص انتهاء صلاحية الجلسة
    const checkSessionExpiry = () => {
      if (!session) return;
      
      const now = Date.now();
      const storedLastActivity = localStorage.getItem('lastActivity');
      const lastActivityTime = storedLastActivity ? parseInt(storedLastActivity) : lastActivity;
      
      // إذا تجاوز الوقت المسموح، قم بتسجيل الخروج
      if (now - lastActivityTime > INACTIVITY_TIMEOUT) {
        console.log("انتهت صلاحية الجلسة بسبب عدم النشاط");
        handleSessionExpiry();
      }
    };

    // معالجة انتهاء صلاحية الجلسة
    const handleSessionExpiry = async () => {
      try {
        await signOut({ 
          redirect: false,
          callbackUrl: '/login?expired=true'
        });
        
        // تنظيف البيانات المحلية
        localStorage.removeItem('lastActivity');
        sessionStorage.clear();
        
        // إعادة التوجيه إلى صفحة تسجيل الدخول
        router.push('/login?expired=true');
      } catch (error) {
        console.error("خطأ في تسجيل الخروج:", error);
        // إعادة التوجيه حتى لو فشل تسجيل الخروج
        window.location.href = '/login?expired=true';
      }
    };

    // إعداد مراقبة النشاط
    const setupActivityMonitoring = () => {
      if (!session) return;

      // الأحداث التي تشير إلى نشاط المستخدم
      const activityEvents = [
        'mousedown', 'mousemove', 'keypress', 'scroll', 
        'touchstart', 'click', 'focus', 'blur'
      ];

      // إضافة مستمعي الأحداث
      activityEvents.forEach(event => {
        document.addEventListener(event, updateActivity, true);
      });

      // تحديث النشاط الأولي
      updateActivity();

      // إعداد مؤقت عدم النشاط
      inactivityTimer = setInterval(checkSessionExpiry, 60000); // فحص كل دقيقة

      // إعداد فحص الجلسة الدوري (كل ساعة)
      sessionCheckInterval = setInterval(() => {
        if (session) {
          // فحص صحة الجلسة مع الخادم
          fetch('/api/auth/session')
            .then(response => {
              if (!response.ok) {
                handleSessionExpiry();
              }
            })
            .catch(() => {
              // في حالة فشل الاتصال، لا نقوم بتسجيل الخروج
              console.warn("فشل في فحص الجلسة مع الخادم");
            });
        }
      }, SESSION_CHECK_INTERVAL);

      // تنظيف المستمعين عند إلغاء التحميل
      return () => {
        activityEvents.forEach(event => {
          document.removeEventListener(event, updateActivity, true);
        });
      };
    };

    // إعداد مراقبة إغلاق المتصفح/التبويب
    const setupBrowserCloseDetection = () => {
      const handleBeforeUnload = () => {
        // تنظيف البيانات المحلية عند إغلاق المتصفح
        localStorage.removeItem('lastActivity');
        sessionStorage.clear();
        
        // إشارة للخادم بأن المستخدم غادر (optional)
        if (session) {
          navigator.sendBeacon('/api/auth/logout', JSON.stringify({ 
            reason: 'browser_close' 
          }));
        }
      };

      const handleVisibilityChange = () => {
        if (document.hidden) {
          // المستخدم غادر التبويب
          updateActivity();
        } else {
          // المستخدم عاد للتبويب
          updateActivity();
          checkSessionExpiry();
        }
      };

      // إضافة مستمعي الأحداث
      window.addEventListener('beforeunload', handleBeforeUnload);
      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    };

    // تشغيل المراقبة فقط إذا كان المستخدم مسجل الدخول
    if (status === 'authenticated' && session) {
      const cleanupActivity = setupActivityMonitoring();
      const cleanupBrowserClose = setupBrowserCloseDetection();

      return () => {
        cleanupActivity?.();
        cleanupBrowserClose?.();
        if (inactivityTimer) clearInterval(inactivityTimer);
        if (sessionCheckInterval) clearInterval(sessionCheckInterval);
      };
    }

    // تنظيف عند تغيير حالة الجلسة
    return () => {
      if (inactivityTimer) clearInterval(inactivityTimer);
      if (sessionCheckInterval) clearInterval(sessionCheckInterval);
    };
  }, [session, status, router]);

  return <>{children}</>;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  return (
    <SessionProvider
      // تحسين إعدادات session للأمان والأداء
      refetchInterval={60 * 60} // إعادة جلب كل ساعة (بدلاً من إلغائها)
      refetchOnWindowFocus={true} // إعادة جلب عند التركيز للتحقق من صحة الجلسة
      refetchWhenOffline={false} // إلغاء إعادة الجلب عند انقطاع الاتصال
    >
      <SessionMonitor>
        {children}
      </SessionMonitor>
    </SessionProvider>
  );
}
