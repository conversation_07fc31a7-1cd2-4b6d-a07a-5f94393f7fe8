"use client";

import { ReactNode, createContext, useContext, useEffect, useState, useCallback } from "react";

// نوع البيانات للإعدادات العامة
export interface PublicSettings {
  companyName: string;
  logo?: string | null;
  favicon?: string | null;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  timezone?: string;
  workStartTime?: string;
  workEndTime?: string;
}

// نوع البيانات للإعدادات الكاملة (للمدراء فقط)
export interface FullSettings extends PublicSettings {
  workHoursRequired: number;
  workingHours: number;
  whatsappApiUrl?: string;
  whatsappApiSecret?: string;
  whatsappAccountKey?: string;
  whatsappEnabled: boolean;
  otpTemplate?: string;
  earlyExitTemplate?: string;
  visitorArrivalTemplate?: string;
  permitApprovalTemplate?: string;
}

// نوع البيانات لحالة المزود
type SettingsProviderState = {
  settings: PublicSettings | null;
  fullSettings: FullSettings | null;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
  refreshFullSettings: () => Promise<void>;
  updateSettings: (newSettings: Partial<FullSettings>) => Promise<boolean>;
};

// الحالة الأولية
const initialState: SettingsProviderState = {
  settings: null,
  fullSettings: null,
  loading: true,
  error: null,
  refreshSettings: async () => {},
  refreshFullSettings: async () => {},
  updateSettings: async () => false,
};

// إنشاء السياق
const SettingsProviderContext = createContext<SettingsProviderState>(initialState);

// خصائص المزود
type SettingsProviderProps = {
  children: ReactNode;
};

// مزود الإعدادات
export function SettingsProvider({ children }: SettingsProviderProps) {
  const [settings, setSettings] = useState<PublicSettings | null>(null);
  const [fullSettings, setFullSettings] = useState<FullSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب الإعدادات العامة (متاحة للجميع)
  const refreshSettings = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/settings/public', {
        headers: {
          'Cache-Control': 'public, max-age=300',
        },
      });

      if (!response.ok) {
        throw new Error('فشل في جلب الإعدادات');
      }

      const data = await response.json();
      setSettings(data);

      // تطبيق الفافيكون إذا كان متوفراً
      if (data.favicon) {
        updateFavicon(data.favicon);
      }

      // تحديث عنوان الصفحة
      if (data.companyName) {
        updatePageTitle(data.companyName);
      }

    } catch (err) {
      console.error('خطأ في جلب الإعدادات:', err);
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
      
      // استخدام الإعدادات الافتراضية في حالة الخطأ
      setSettings({
        companyName: 'النظام الذكي للحضور والانصراف',
        logo: null,
        favicon: null,
        primaryColor: '#1e40af',
        secondaryColor: '#3b82f6',
        accentColor: '#10b981',
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // جلب الإعدادات الكاملة (للمدراء فقط)
  const refreshFullSettings = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/settings');

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // المستخدم ليس لديه صلاحية - هذا طبيعي
          return;
        }
        throw new Error('فشل في جلب الإعدادات الكاملة');
      }

      const data = await response.json();
      setFullSettings(data);

    } catch (err) {
      console.error('خطأ في جلب الإعدادات الكاملة:', err);
      // لا نعرض خطأ للمستخدمين العاديين
      if (err instanceof Error && !err.message.includes('401') && !err.message.includes('403')) {
        setError(err.message);
      }
    }
  }, []);

  // تحديث الإعدادات (للمدراء فقط)
  const updateSettings = useCallback(async (newSettings: Partial<FullSettings>): Promise<boolean> => {
    try {
      setError(null);
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تحديث الإعدادات');
      }

      const updatedData = await response.json();
      setFullSettings(updatedData);

      // تحديث الإعدادات العامة أيضاً
      setSettings({
        companyName: updatedData.companyName,
        logo: updatedData.logo,
        favicon: updatedData.favicon,
        primaryColor: updatedData.primaryColor,
        secondaryColor: updatedData.secondaryColor,
        accentColor: updatedData.accentColor,
      });

      // تطبيق التغييرات على الصفحة
      if (updatedData.favicon) {
        updateFavicon(updatedData.favicon);
      }
      if (updatedData.companyName) {
        updatePageTitle(updatedData.companyName);
      }

      return true;
    } catch (err) {
      console.error('خطأ في تحديث الإعدادات:', err);
      setError(err instanceof Error ? err.message : 'حدث خطأ في التحديث');
      return false;
    }
  }, []);

  // تحديث الفافيكون
  const updateFavicon = (faviconUrl: string) => {
    try {
      const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = faviconUrl;
      if (!document.querySelector("link[rel*='icon']")) {
        document.getElementsByTagName('head')[0].appendChild(link);
      }
    } catch (error) {
      console.error('خطأ في تحديث الفافيكون:', error);
    }
  };

  // تحديث عنوان الصفحة
  const updatePageTitle = (companyName: string) => {
    try {
      const currentTitle = document.title;
      const basePage = currentTitle.split(' - ')[0];
      document.title = basePage ? `${basePage} - ${companyName}` : companyName;
    } catch (error) {
      console.error('خطأ في تحديث عنوان الصفحة:', error);
    }
  };

  // تحميل الإعدادات عند بدء التشغيل
  useEffect(() => {
    refreshSettings();
    refreshFullSettings();
  }, [refreshSettings, refreshFullSettings]);

  // قيمة السياق
  const value = {
    settings,
    fullSettings,
    loading,
    error,
    refreshSettings,
    refreshFullSettings,
    updateSettings,
  };

  return (
    <SettingsProviderContext.Provider value={value}>
      {children}
    </SettingsProviderContext.Provider>
  );
}

// خطاف استخدام الإعدادات
export const useSettings = () => {
  const context = useContext(SettingsProviderContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
};

// خطاف للإعدادات العامة فقط (محسن للأداء)
export const usePublicSettings = () => {
  const { settings, loading, error, refreshSettings } = useSettings();
  return { settings, loading, error, refreshSettings };
};

// خطاف للإعدادات الكاملة (للمدراء)
export const useFullSettings = () => {
  const { fullSettings, loading, error, refreshFullSettings, updateSettings } = useSettings();
  return { fullSettings, loading, error, refreshFullSettings, updateSettings };
}; 