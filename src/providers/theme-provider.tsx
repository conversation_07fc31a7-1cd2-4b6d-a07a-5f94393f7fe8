"use client";

import { ReactNode, createContext, useContext, useEffect, useState } from "react";

type ThemeProviderProps = {
  children: ReactNode;
};

type ThemeProviderState = {
  primaryColor: string;
  secondaryColor: string;
  setPrimaryColor: (color: string) => void;
  setSecondaryColor: (color: string) => void;
};

const initialState: ThemeProviderState = {
  primaryColor: "#1e40af",
  secondaryColor: "#3b82f6",
  setPrimaryColor: () => null,
  setSecondaryColor: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [primaryColor, setPrimaryColor] = useState(initialState.primaryColor);
  const [secondaryColor, setSecondaryColor] = useState(initialState.secondaryColor);

  // Apply theme colors to CSS variables
  useEffect(() => {
    document.documentElement.style.setProperty("--primary-color", primaryColor);
    document.documentElement.style.setProperty("--secondary-color", secondaryColor);
  }, [primaryColor, secondaryColor]);

  const value = {
    primaryColor,
    secondaryColor,
    setPrimaryColor,
    setSecondaryColor,
  };

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
