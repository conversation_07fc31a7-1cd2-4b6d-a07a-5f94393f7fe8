# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AttendPro is a Next.js-based HR management system with attendance tracking, leave management, visitor management, and after-hours work permits. The system is designed for Arabic-speaking users with RTL support.

## Key Commands

### Development
```bash
npm run dev          # Start development server
npm run dev:fast     # Start with Turbo mode
npm run dev:clean    # Clean cache and start dev server
```

### Build & Production
```bash
npm run build        # Build for production
npm run start        # Start production server
npm run build:analyze # Build with bundle analysis
```

### Database & Testing
```bash
npm run seed         # Seed database with sample data
npm run lint         # Run ESLint
```

### TypeScript
```bash
npx tsc --noEmit    # Type check without emitting files
```

## High-Level Architecture

### Tech Stack
- **Framework**: Next.js 15.3.2 with App Router
- **Language**: TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: NextAuth.js with OTP support
- **Styling**: Tailwind CSS with RTL support
- **Font**: <PERSON><PERSON>wal (local Arabic font)
- **Timezone**: Asia/Muscat

### Core Modules

1. **Authentication System** (`src/modules/auth/`)
   - OTP-based login via WhatsApp
   - Role-based access control (ADMIN, HR, MANAGER, EMPLOYEE, SECURITY)
   - Session management with automatic expiry

2. **Attendance Management** (`src/modules/attendance/`)
   - Check-in/check-out tracking with multiple entries per day (up to 4)
   - Exit types: OFFICIAL, PERSONAL, WORK, HEALTH
   - Flexible working hours calculation based on actual check-in time + configured hours
   - Integration with leave requests and official holidays

3. **Leave Management** (`src/modules/leaves/`)
   - Leave request workflow with approval system
   - Integration with attendance reports

4. **Visitor Management** (`src/modules/visitors/`)
   - Pre-registration and approval workflow
   - Real-time check-in/check-out at security gate

5. **Settings System** (`src/modules/settings/`)
   - Centralized configuration stored in database
   - Dynamic theming with logo/favicon upload
   - WhatsApp integration settings
   - Working hours configuration

### API Structure
All API routes follow the pattern: `/api/[module]/[action]/route.ts`
- Uses Next.js 15 route handlers with async params
- Authentication required for all protected routes
- Role-based access control implemented at API level

### Database Schema
- Prisma schema at `prisma/schema.prisma`
- Key models: User, Department, AttendanceRecord, LeaveRequest, VisitorRequest, AfterHoursPermit, Settings
- Relationships: Users belong to departments, have managers, and can be department heads

### Security Considerations
- All routes protected by middleware (`middleware.ts`)
- Session-based authentication with automatic logout
- OTP verification for login
- Role-based access control throughout the system

## Development Guidelines

1. **Date/Time Handling**
   - Always use Asia/Muscat timezone
   - Date format: DD/MM/YYYY (Gregorian calendar in Arabic)
   - Use `date-fns` for date operations

2. **RTL Support**
   - All UI components must support RTL layout
   - Use Tailwind's RTL utilities (`rtl:` prefix)
   - Text alignment and spacing should respect RTL

3. **Error Handling**
   - Use try-catch blocks in all API routes
   - Return appropriate HTTP status codes
   - Log errors for debugging

4. **Performance**
   - Use React Server Components where possible
   - Implement proper loading states
   - Optimize database queries with Prisma

5. **Testing Data**
   - Use scripts in `scripts/` folder for creating test data
   - Run `npm run seed` for initial data setup

## Common Issues & Solutions

1. **White Page Errors**: Check for missing async/await in data fetching
2. **OTP Performance**: Ensure proper session handling and avoid unnecessary re-renders
3. **Favicon Issues**: Settings are cached, check `src/providers/settings-provider.tsx`
4. **Date Display**: Ensure consistent date formatting across the app
5. **Permission Errors**: Verify role-based access in both frontend and API routes