#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 تحسين أداء AttendPro...');

// 1. تنظيف cache directories
const cacheDirs = [
  '.next',
  'node_modules/.cache',
  '.next/cache'
];

cacheDirs.forEach(dir => {
  try {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ تم تنظيف: ${dir}`);
    }
  } catch (error) {
    console.log(`⚠️  تعذر تنظيف ${dir}: ${error.message}`);
  }
});

// 2. إنشاء cache directories مع permissions صحيحة
const newCacheDirs = [
  '.next/cache/webpack',
  'node_modules/.cache'
];

newCacheDirs.forEach(dir => {
  try {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ تم إنشاء: ${dir}`);
  } catch (error) {
    console.log(`⚠️  تعذر إنشاء ${dir}: ${error.message}`);
  }
});

// 3. إنشاء ملف تحسين package.json scripts
const packageJsonPath = 'package.json';
if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // إضافة scripts محسنة
    packageJson.scripts = {
      ...packageJson.scripts,
      "dev:fast": "next dev --turbo",
      "clean": "rm -rf .next && rm -rf node_modules/.cache",
      "dev:clean": "npm run clean && npm run dev",
      "build:analyze": "ANALYZE=true npm run build"
    };

    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ تم تحديث package.json scripts');
  } catch (error) {
    console.log(`⚠️  تعذر تحديث package.json: ${error.message}`);
  }
}

// 4. إنشاء .env.local محسن للأداء
const envContent = `
# تحسين الأداء للتطوير
NEXT_TELEMETRY_DISABLED=1
NODE_ENV=development

# تحسين Prisma
PRISMA_QUERY_ENGINE_LIBRARY=1

# تحسين NextAuth
NEXTAUTH_URL=http://localhost:3000

# تعطيل debug غير الضروري
DEBUG=0
`;

try {
  const envPath = '.env.local';
  const existingEnv = fs.existsSync(envPath) ? fs.readFileSync(envPath, 'utf8') : '';
  
  // إضافة التحسينات إذا لم تكن موجودة
  if (!existingEnv.includes('NEXT_TELEMETRY_DISABLED')) {
    fs.appendFileSync(envPath, envContent);
    console.log('✅ تم تحديث .env.local للأداء');
  }
} catch (error) {
  console.log(`⚠️  تعذر تحديث .env.local: ${error.message}`);
}

console.log('\n🎉 تم الانتهاء من تحسين الأداء!');
console.log('\n📋 الخطوات التالية:');
console.log('1. npm run dev:fast  # للتشغيل السريع');
console.log('2. npm run dev:clean # للتنظيف والتشغيل');
console.log('3. مراقبة أوقات التحميل في browser');

console.log('\n💡 نصائح للأداء:');
console.log('- استخدم npm run dev:fast بدلاً من npm run dev');
console.log('- راقب Network tab في DevTools');
console.log('- قم بإعادة تشغيل server عند تغيير port'); 