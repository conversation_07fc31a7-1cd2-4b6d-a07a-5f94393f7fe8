import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const { pathname } = req.nextUrl;

    // Allow access to login page and API routes
    if (pathname.startsWith('/login') || 
        pathname.startsWith('/api/auth') || 
        pathname.startsWith('/api/settings/public') ||
        pathname.startsWith('/_next') ||
        pathname.startsWith('/favicon')) {
      return NextResponse.next();
    }

    // Check if user has valid session
    if (!token) {
      const loginUrl = new URL('/login', req.url);
      return NextResponse.redirect(loginUrl);
    }

    // Check session expiry (4 hours = 14400 seconds)
    const now = Math.floor(Date.now() / 1000);
    const tokenIat = typeof token.iat === 'number' ? token.iat : 0;
    const sessionAge = now - tokenIat;
    const maxSessionAge = 4 * 60 * 60; // 4 hours in seconds

    if (sessionAge > maxSessionAge) {
      // Session expired, redirect to login
      const loginUrl = new URL('/login?expired=true', req.url);
      return NextResponse.redirect(loginUrl);
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Always allow access to login and public API routes
        if (pathname.startsWith('/login') || 
            pathname.startsWith('/api/auth') || 
            pathname.startsWith('/api/settings/public') ||
            pathname.startsWith('/_next') ||
            pathname.startsWith('/favicon')) {
          return true;
        }

        // Require authentication for all other routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}; 