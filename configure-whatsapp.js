const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function configureWhatsApp() {
  try {
    console.log('🔧 إعداد خدمة WhatsApp...\n');

    // Check current settings
    const settings = await prisma.settings.findFirst();
    
    if (!settings) {
      console.log('❌ لم يتم العثور على إعدادات النظام');
      return;
    }

    console.log('📋 الإعدادات الحالية:');
    console.log(`   🔗 رابط API: ${settings.whatsappApiUrl}`);
    console.log(`   🔑 المفتاح السري: ${settings.whatsappApiSecret ? 'موجود' : '❌ مفقود'}`);
    console.log(`   👤 مفتاح الحساب: ${settings.whatsappAccountKey ? 'موجود' : '❌ مفقود'}`);
    console.log(`   ✅ مفعل: ${settings.whatsappEnabled ? 'نعم' : '❌ لا'}\n`);

    if (!settings.whatsappApiSecret || !settings.whatsappAccountKey) {
      console.log('⚠️ تحتاج إلى إعداد WhatsApp لتفعيل خدمة OTP');
      console.log('');
      console.log('📝 الخطوات المطلوبة:');
      console.log('1. احصل على API Secret من CloudText (https://w.gcccons.org)');
      console.log('2. احصل على Account Key من CloudText');
      console.log('3. استخدم الأمر التالي لتحديث الإعدادات:');
      console.log('');
      console.log('💡 مثال على التحديث:');
      console.log('```');
      console.log('// قم بتشغيل هذا في قاعدة البيانات أو عبر الإعدادات في النظام');
      console.log('UPDATE Settings SET');
      console.log('  whatsappApiSecret = "YOUR_API_SECRET_HERE",');
      console.log('  whatsappAccountKey = "YOUR_ACCOUNT_KEY_HERE",');
      console.log('  whatsappEnabled = true');
      console.log('WHERE id = "' + settings.id + '";');
      console.log('```');
      console.log('');
      console.log('🔧 أو استخدم صفحة الإعدادات في النظام:');
      console.log('   👉 اذهب إلى http://localhost:3000/dashboard/settings');
      console.log('   👉 اختر تبويب "WhatsApp"');
      console.log('   👉 أدخل البيانات المطلوبة');
      console.log('   👉 احفظ الإعدادات');
    } else {
      console.log('✅ إعدادات WhatsApp مكتملة!');
      
      if (!settings.whatsappEnabled) {
        console.log('⚠️ لكن الخدمة غير مفعلة. تفعيل الخدمة...');
        
        await prisma.settings.update({
          where: { id: settings.id },
          data: { whatsappEnabled: true }
        });
        
        console.log('✅ تم تفعيل خدمة WhatsApp');
      }
    }

    // Test case examples
    console.log('\n📱 أرقام الهواتف المتاحة للاختبار:');
    const users = await prisma.user.findMany({
      select: { name: true, phone: true }
    });

    const usersWithPhones = users.filter(user => user.phone && user.phone.trim() !== '');

    usersWithPhones.forEach((user, index) => {
      const cleanPhone = user.phone.replace(/[^\d]/g, '');
      const last8 = cleanPhone.slice(-8);
      console.log(`   ${index + 1}. ${user.name}: ${last8} (آخر 8 أرقام)`);
    });

    console.log('\n🧪 لاختبار OTP بعد الإعداد:');
    console.log('   node test-otp-simple.js');

  } catch (error) {
    console.error('❌ خطأ في إعداد WhatsApp:', error);
  } finally {
    await prisma.$disconnect();
  }
}

configureWhatsApp(); 